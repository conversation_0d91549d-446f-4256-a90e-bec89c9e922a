package com.apex.sdx.core.adapter;

/**
 * <AUTHOR>
 * @date 2023/3/29
 */
public class PgSqlAdapter extends AbstractSqlAdapter {

    @Override
    public String sysdate() {
        return "now()";
    }

    @Override
    public String date_8_format() {
        return date_8_format(sysdate());
    }

    @Override
    public String date_8_format(String date) {
        return "to_char(" + date + ",'yyyymmdd')";
    }

    @Override
    public String date_10_format() {
        return date_10_format(sysdate());
    }

    @Override
    public String date_10_format(String date) {
        return "to_char(" + date + ",'yyyy-mm-dd')";
    }

    @Override
    public String date_18_format() {
        return date_18_format(sysdate());
    }

    @Override
    public String date_18_format(String date) {
        return "to_char(" + date + ",'yyyymmdd hh24:mi:ss')";
    }

    @Override
    public String time_8_format() {
        return "to_char(" + sysdate() + ", 'hh24miss')";
    }

    @Override
    public String time_10_format() {
        return "to_char(" + sysdate() + ", 'hh24:mi:ss')";
    }

    @Override
    public String to_date_8(String obj) {
        return "to_date(" + obj + ", 'yyyymmdd')";
    }

    @Override
    public String to_date_10(String obj) {
        return "to_date(" + obj + ", 'yyyy-mm-dd')";
    }

    @Override
    public String to_date_18(String obj) {
        return "to_date(" + obj + ", 'yyyy-mm-dd hh24:mi:ss')";
    }

}
