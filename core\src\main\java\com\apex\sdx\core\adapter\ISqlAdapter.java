package com.apex.sdx.core.adapter;

/**
 * sql语句适配器
 *
 * <AUTHOR>
 * @date 2023/3/29
 */
public interface ISqlAdapter {

    /**
     * 系统时间
     *
     * @return
     */
    String sysdate();

    /**
     * 系统日期格式化 yyyymmdd
     *
     * @return
     */
    String date_8_format();

    /**
     * 指定日期格式化 yyyymmdd
     *
     * @return
     */
    String date_8_format(String date);

    /**
     * 系统日期格式化 yyyy-mm-dd
     *
     * @return
     */
    String date_10_format();

    /**
     * 指定日期格式化 yyyy-mm-dd
     *
     * @return
     */
    String date_10_format(String date);

    /**
     * 日期格式化 yyyyMMdd hh24:mi:ss
     *
     * @return
     */
    String date_18_format();

    /**
     * 日期格式化 yyyyMMdd hh24:mi:ss
     *
     * @return
     */
    String date_18_format(String date);

    /**
     * 系统时间格式化 hh24miss
     *
     * @return
     */
    String time_8_format();

    /**
     * 系统时间格式化 hh24:mi:ss
     *
     * @return
     */
    String time_10_format();

    /**
     * 日期格式化 yyyymmdd
     *
     * @param obj 字符串日期
     * @return
     */
    String to_date_8(String obj);

    /**
     * 日期格式化 yyyy-mm-dd
     *
     * @param obj 字符串日期
     * @return
     */
    String to_date_10(String obj);


    /**
     * 日期格式化 yyyyMMdd hh24:mi:ss
     *
     * @return
     */
    String to_date_18(String date);

    /**
     * @param source    为字段时不用加单引号,为字符串时需要在两侧拼接单引号
     * @param target    为字段时不用加单引号,为字符串时需要在两侧拼接单引号
     * @param operation 操作符 <>=
     * @param value
     * @param concatStr 连接串
     * @return
     */
    String instr(String source, String target, String operation, String value, String concatStr);

    /**
     * max函数使用
     *
     * @param field    需要max查询的字段名
     * @param fieldMpp max查询结果映射的字段名，字符串为空时不进行映射
     * @return
     */
    String max(String field, String fieldMpp);

    /**
     * nvl函数
     *
     * @param field        需要nvl查询的字段名
     * @param fieldMpp     nvl查询结果映射的字段名，字符串为空时不进行映射
     * @param defaultValue 默认值
     * @return
     */
    String nvl(String field, String fieldMpp, String defaultValue);

    /**
     * 字符串拼接
     * @param str1
     * @param str2
     * @return
     */
    String concat(String str1, String... str2);
}
