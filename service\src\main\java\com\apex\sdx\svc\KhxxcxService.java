package com.apex.sdx.svc;

import com.apex.sdx.api.req.khgl.KhxxcxReq;
import com.apex.sdx.api.req.khgl.ZhsdxqkReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.khgl.KhxxVo;
import com.apex.sdx.api.vo.khgl.KhxxtjVo;
import com.apex.sdx.api.vo.khgl.TzzcfbVo;
import com.apex.sdx.api.vo.khgl.ZhsdxqkVo;
import com.apex.sdx.convert.KhxxDtoMapping;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.entity.Tkhxx;
import com.apex.sdx.core.mybatis.service.TkhxxService;
import com.apex.sdx.core.utils.DateUtil;
import com.apexsoft.LiveService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.khgl.IKhxxcxService;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Jingliang
 * @Date 2025-01-16
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "客户信息查询")
public class KhxxcxService implements IKhxxcxService {

    @Autowired
    TkhxxService tkhxxService;

    @Autowired
    private KhxxDtoMapping khxxDtoMapping;

    @Override
    public QueryResponse<KhxxVo> khjbxxcx(KhxxcxReq req) throws Exception {
        Assert.notNull(req, KhxxcxReq::getKhh);
        QueryResponse<KhxxVo> result = new QueryResponse<>(1, "查询成功");
        String khh = req.getKhh();
        String zjbh = req.getZjbh();
        Integer zjlb = req.getZjlb();
        Tkhxx tkhxx = tkhxxService.getKhxxByKhhZjbhZjlb(khh, zjbh, zjlb);//这里面不要去动 在这个后面返回
        List<Tkhxx> khxxList = new ArrayList<>();
        List<KhxxVo> khxxVos = new ArrayList<>();
        if(tkhxx!=null){
            khxxList.add(tkhxx);
            khxxVos = khxxDtoMapping.listConver(khxxList);

            String today = DateUtil.getNowDate(DateUtil.F_DATE8);
            for (KhxxVo map : khxxVos) {
                //当Tzzpdyxq不为空，计算与今天相差天数
                String tzzpdsyts = Optional.ofNullable(map.getTzzpdyxq()).map(e -> String.valueOf(DateUtil.getDaysBetween(today, String.valueOf(e), DateUtil.F_DATE8))).orElse("");
                map.setTzzpdyxqsyts(tzzpdsyts);
                //当Cpyxq不为空，计算与今天相差天数
                Optional<Integer> i = Optional.ofNullable(map.getCpyxq()).map(e -> DateUtil.getDaysBetween(today, String.valueOf(e), DateUtil.F_DATE8));
                map.setCpyxqsyts(String.valueOf(i.get()));
            }
        }
        result.setRecords(khxxVos);
        return result;
    }

    @Override
    public QueryResponse<KhxxtjVo> khxxtj(KhxxcxReq req) {
        QueryResponse<KhxxtjVo> result = new QueryResponse<>(1, "查询成功");
        List<KhxxtjVo> KhxxVoList = tkhxxService.khxxtj();
        result.setRecords(KhxxVoList);
        return result;
    }

    @Override
    public QueryPageResponse<ZhsdxqkVo> zhztmxcx(ZhsdxqkReq req) throws Exception {
        QueryPageResponse<ZhsdxqkVo> result = new QueryPageResponse<>(1, "查询成功");
        String zhqk = req.getZhqk();
        String zhzt = req.getZhzt();
        String khh = req.getKhh();

        Page<ZhsdxqkVo> page =
                tkhxxService.queryZhztmx(
                        khh, zhqk, zhzt, req.isSearchCount(), req.getPagesize(), req.getPagenum());
        result.page(page);
        return result;
    }

    @Override
    public QueryPageResponse<ZhsdxqkVo> zjyxqmxcx(ZhsdxqkReq req) throws Exception {
        QueryPageResponse<ZhsdxqkVo> result = new QueryPageResponse<>(1, "查询成功");
        String zhqk = req.getZhqk();
        String zjjzrq = req.getZjjzrq();
        String khh = req.getKhh();

        Page<ZhsdxqkVo> page =
                tkhxxService.queryZjyxqmx(
                        khh, zhqk, zjjzrq, req.isSearchCount(), req.getPagesize(), req.getPagenum());
        result.page(page);
        return result;
    }

    @Override
    public QueryPageResponse<ZhsdxqkVo> fxqfxdjmxcx(ZhsdxqkReq req) throws Exception {
        QueryPageResponse<ZhsdxqkVo> result = new QueryPageResponse<>(1, "查询成功");
        String xqfxdj = req.getXqfxdj();
        String khh = req.getKhh();

        Page<ZhsdxqkVo> page =
                tkhxxService.queryFxqfxdjmx(
                        khh, xqfxdj, req.isSearchCount(), req.getPagesize(), req.getPagenum());
        result.page(page);
        return result;
    }

    @Override
    public QueryPageResponse<ZhsdxqkVo> fxcpyxmxcx(ZhsdxqkReq req) throws Exception {
        QueryPageResponse<ZhsdxqkVo> result = new QueryPageResponse<>(1, "查询成功");
        String zhqk = req.getZhqk();
        String cpyxq = req.getCpyxq();
        String khh = req.getKhh();

        Page<ZhsdxqkVo> page =
                tkhxxService.queryFxcpyxmx(
                        khh, zhqk, cpyxq, req.isSearchCount(), req.getPagesize(), req.getPagenum());
        result.page(page);
        return result;
    }

    @Override
    public QueryPageResponse<ZhsdxqkVo> zytzzcpmxcx(ZhsdxqkReq req) throws Exception {
        QueryPageResponse<ZhsdxqkVo> result = new QueryPageResponse<>(1, "查询成功");
        String tzzpdyxq = req.getTzzpdyxq();
        String khh = req.getKhh();

        Page<ZhsdxqkVo> page =
                tkhxxService.queryZytzzcpmx(
                        khh, tzzpdyxq, req.isSearchCount(), req.getPagesize(), req.getPagenum());
        result.page(page);
        return result;
    }

    @Override
    public QueryPageResponse<ZhsdxqkVo> qtxxmxcx(ZhsdxqkReq req) throws Exception {
        QueryPageResponse<ZhsdxqkVo> result = new QueryPageResponse<>(1, "查询成功");
        String qtxxzt = req.getQtxxzt();
        String khh = req.getKhh();

        Page<ZhsdxqkVo> page =
                tkhxxService.queryQtxxmx(
                        khh, qtxxzt, req.isSearchCount(), req.getPagesize(), req.getPagenum());
        result.page(page);
        return result;
    }

    @Override
    public QueryResponse<TzzcfbVo> tzzcfbcx(KhxxcxReq req) throws Exception {
        QueryResponse<TzzcfbVo> result = new QueryResponse<>(1, "查询成功");
        TzzcfbVo tzzcfbVo = tkhxxService.queryTzzcfb();

        List<TzzcfbVo> records = new ArrayList<>();
        records.add(tzzcfbVo);
        result.setRecords(records);

        return result;
    }
}
