package sdx.query;

import com.apex.sdx.api.req.query.JgkhxxhcsqcxReq;
import com.apex.sdx.api.req.query.SjhcxxcxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.vo.khgl.JgkhxxhcsqVo;
import com.apex.sdx.api.vo.khgl.SjhcxxVo;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR>
 * @Date 2025-02-14
 * @Description:
 */
public interface IZdZhywsqService {

    @LiveMethod(paramAsRequestBody = true, note = "机构客户信息核查申请查询")
    QueryPageResponse<JgkhxxhcsqVo> jgkhxxhcsqcx(JgkhxxhcsqcxReq req) throws Exception;

}
