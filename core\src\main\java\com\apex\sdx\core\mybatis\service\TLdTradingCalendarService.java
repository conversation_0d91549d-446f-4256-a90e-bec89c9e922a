package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.core.mybatis.entity.TLdTradingCalendar;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【t_ld_trading_calendar(交易日历表)】的数据库操作Service
* @createDate 2025-06-03 14:10:21
*/
public interface TLdTradingCalendarService extends IService<TLdTradingCalendar> {

    /**
     * 获取交易日集合
     * @param zrr 自然日
     * @return
     */
    TLdTradingCalendar queryJyrByZrr(String zrr) throws Exception;

}
