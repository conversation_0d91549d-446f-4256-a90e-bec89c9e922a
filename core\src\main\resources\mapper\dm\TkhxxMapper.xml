<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.apex.sdx.core.mybatis.mapper.TkhxxMapper">

    <!-- 基础统计查询 -->
    <select id="khxxtjBasic" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalCount,
            SUM(CASE WHEN khzt = 0 THEN 1 ELSE 0 END) AS zhztzcgs,
            SUM(CASE WHEN khzt NOT IN (0,3) THEN 1 ELSE 0 END) AS zhztycgs
        FROM sdx.tkhxx
    </select>

    <!-- 风险等级统计查询 -->
    <select id="khxxtjRisk" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN xqfxdj = 1 THEN 1 ELSE 0 END) AS zhfxqfxdjzcgs,
            SUM(CASE WHEN xqfxdj IN (2, 3) THEN 1 ELSE 0 END) AS zhfxqfxdjycgs
        FROM sdx.tkhxx
    </select>

    <!-- 日期相关统计查询 -->
    <select id="khxxtjDate" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN zjjzrq &gt;= to_char(CURRENT_DATE, 'yyyymmdd') THEN 1 ELSE 0 END) AS zjyxqzcgs,
            SUM(CASE WHEN zjjzrq &lt; to_char(CURRENT_DATE, 'yyyymmdd') THEN 1 ELSE 0 END) AS zjyxqycgs,
            SUM(CASE WHEN cpyxq &gt;= to_char(CURRENT_DATE, 'yyyymmdd') THEN 1 ELSE 0 END) AS fxcpyxzcgs,
            SUM(CASE WHEN cpyxq &lt; to_char(CURRENT_DATE, 'yyyymmdd') THEN 1 ELSE 0 END) AS fxcpyxycgs
        FROM sdx.tkhxx
    </select>

    <!-- 投资者统计查询 -->
    <select id="khxxtjInvestor" resultType="java.util.Map">
        SELECT
            COUNT(*) as tzzflTotal,
            SUM(CASE WHEN tzzpdyxq &gt;= to_char(CURRENT_DATE, 'yyyymmdd') THEN 1 ELSE 0 END) AS zytzzcpzcgs,
            SUM(CASE WHEN tzzpdyxq &lt; to_char(CURRENT_DATE, 'yyyymmdd') THEN 1 ELSE 0 END) AS zytzzcpycgs
        FROM sdx.tkhxx
        WHERE tzzfl IN (1, 2, 3)
    </select>

    <!-- 其他信息统计查询 -->
    <select id="khxxtjOther" resultType="java.util.Map">
        SELECT
            COUNT(*) as qtTotal,
            SUM(CASE WHEN jymdjg = 0 AND jymdlx = 1 THEN 1 ELSE 0 END) AS zhqtxxzcgs,
            SUM(CASE WHEN jymdjg IN (1, 2) AND jymdlx = 1 THEN 1 ELSE 0 END) AS zhqtxxycgs
        FROM sdx.thmdjyjgls
    </select>

    <!-- 保留原有的khxxtj查询作为占位符 -->
    <select id="khxxtj" resultType="com.apex.sdx.api.vo.khgl.KhxxtjVo">
        SELECT 1 as placeholder
    </select>

    <select id="queryTzzflxx" resultType="com.apex.sdx.api.resp.query.KhsdxfbRes">
        <choose>
            <!-- 普通投资者(tzzfl=0)占比计算：普通投资者数量 / 所有投资者数量 -->
            <when test="tzzfl != null and tzzfl == 0">
                select count(*) zs,
                       Round((count(*) * 1.0 / NUllif((select count(*) from sdx.tkhxx), 0)), 4) zb
                from sdx.tkhxx
                <where>
                    and tzzfl = #{tzzfl}
                </where>
            </when>
            <!-- 专业投资者(tzzfl=1,2,3)占比计算：某类型专业投资者数量 / 专业投资者总数量，并计算日增长和月增长 -->
            <when test="tzzfl != null and tzzfl != 0">
                select
                    count(*) zs,
                    Round((count(*) * 1.0 / NUllif((select count(*) from sdx.tkhxx where tzzfl in (1, 2, 3)), 0)), 4) zb,
                    to_char(
                        (select count(*) from sdx.tkhxx
                         where tzzfl = #{tzzfl}
                         and tzzpdrq = to_number(to_char(sysdate - 1, 'YYYYMMDD')))
                    ) as rzz,
                    to_char(
                        (select count(*) from sdx.tkhxx
                         where tzzfl = #{tzzfl}
                         and tzzpdrq &gt;= to_number(to_char(trunc(sysdate, 'MM'), 'YYYYMMDD'))
                         and tzzpdrq &lt;= to_number(to_char(sysdate, 'YYYYMMDD')))
                    ) as yzz
                from sdx.tkhxx
                <where>
                    and tzzfl = #{tzzfl}
                </where>
            </when>
            <!-- 默认情况 -->
            <otherwise>
                select count(*) zs,
                       Round((count(*) * 1.0 / NUllif((select count(*) from sdx.tkhxx), 0)), 4) zb
                from sdx.tkhxx
                <where>
                    <if test="tzzfl != null and tzzfl != ''">
                        and tzzfl = #{tzzfl}
                    </if>
                </where>
            </otherwise>
        </choose>
    </select>
    <select id="queryFxcsnl" resultType="com.apex.sdx.api.resp.query.KhsdxfbRes">
        select count(*) zs, Round((count(*) * 1.0 / NUllif((select count(*) from sdx.tkhxx where khzt = 0), 0)), 4) zb
        from sdx.tkhxx
        <where>
            and khzt = 0
            <if test="cpdj != null and cpdj != ''">
                <choose>
                    <!-- 当查询cpdj=0时，包含cpdj为-1、0、null的所有未测评客户 -->
                    <when test="cpdj == '0' or cpdj == 0">
                        and (cpdj = -1 or cpdj = 0 or cpdj is null)
                    </when>
                    <!-- 其他风险等级按实际值查询 -->
                    <otherwise>
                        and cpdj = #{cpdj}
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>
    
    <!-- 查询ABC类投资者数据 -->
    <select id="queryAbcTzzflxx" resultType="com.apex.sdx.api.resp.query.KhsdxfbRes">
        SELECT
            tzzfl as tzzfl,
            count(*) as zs,
            ROUND((count(*) * 1.0 / (
                SELECT count(*) FROM sdx.tkhxx WHERE tzzfl IN (1, 2, 3)
            )), 4) as zb
        FROM sdx.tkhxx
        WHERE tzzfl IN (1, 2, 3)
        GROUP BY tzzfl
        ORDER BY tzzfl
    </select>
    
    <!-- 查询账户适当性情况-证件有效期明细 -->
    <select id="queryZjyxqmx" resultType="com.apex.sdx.api.vo.khgl.ZhsdxqkVo">
        SELECT khh, khmc, zjjzrq,
            CASE WHEN zjjzrq IS NOT NULL AND zjjzrq &lt; to_char(CURRENT_DATE, 'yyyymmdd') THEN '过期' ELSE '有效' END AS zhqk
        FROM sdx.tkhxx
        <where>
            <if test="khh != null and khh != ''">AND khh = #{khh}</if>
            <if test="zjjzrq != null and zjjzrq != ''">AND zjjzrq = #{zjjzrq}</if>
            <if test="zhqk != null and zhqk != ''">
                AND (CASE WHEN zjjzrq IS NOT NULL AND zjjzrq &lt; to_char(CURRENT_DATE, 'yyyymmdd') THEN '过期' ELSE '有效' END) = #{zhqk}
            </if>
        </where>
    </select>
    
    <!-- 查询账户适当性情况-风险承受能力等级明细 -->
    <select id="queryFxqfxdjmx" resultType="com.apex.sdx.api.vo.khgl.ZhsdxqkVo">
        SELECT khh, khmc, fxqszrq,
            CASE
                WHEN xqfxdj = '1' THEN '正常'
                WHEN xqfxdj = '2' OR xqfxdj = '3' THEN '异常'
                ELSE NULL
            END AS zhqk,
            CASE
                WHEN xqfxdj = '1' THEN '正常'
                WHEN xqfxdj = '2' THEN '关注'
                WHEN xqfxdj = '3' THEN '高风险'
                ELSE NULL
            END AS xqfxdj
        FROM sdx.tkhxx
        <where>
            <if test="khh != null and khh != ''">AND khh = #{khh}</if>
            <if test="xqfxdj != null and xqfxdj != ''">AND xqfxdj = #{xqfxdj}</if>
        </where>
    </select>
    
    <!-- 查询账户适当性情况-风险产品有效期明细 -->
    <select id="queryFxcpyxmx" resultType="com.apex.sdx.api.vo.khgl.ZhsdxqkVo">
        SELECT khh, khmc, cpyxq,
            CASE WHEN cpyxq IS NOT NULL AND cpyxq &lt; to_char(CURRENT_DATE, 'yyyymmdd') THEN '过期' ELSE '有效' END AS zhqk
        FROM sdx.tkhxx
        <where>
            <if test="khh != null and khh != ''">AND khh = #{khh}</if>
            <if test="cpyxq != null and cpyxq != ''">AND cpyxq = #{cpyxq}</if>
            <if test="zhqk != null and zhqk != ''">
                AND (CASE WHEN cpyxq IS NOT NULL AND cpyxq &lt; to_char(CURRENT_DATE, 'yyyymmdd') THEN '过期' ELSE '有效' END) = #{zhqk}
            </if>
        </where>
    </select>
    
    <!-- 查询账户适当性情况-专业投资者测评明细 -->
    <select id="queryZytzzcpmx" resultType="com.apex.sdx.api.vo.khgl.ZhsdxqkVo">
        SELECT khh, khmc, tzzpdyxq,
            CASE WHEN tzzpdyxq IS NOT NULL AND tzzpdyxq &lt; to_char(CURRENT_DATE, 'yyyymmdd') THEN '过期' ELSE '有效' END AS zhqk
        FROM sdx.tkhxx
        <where>
            <if test="khh != null and khh != ''">AND khh = #{khh}</if>
            <if test="tzzpdyxq != null and tzzpdyxq != ''">AND tzzpdyxq = #{tzzpdyxq}</if>
        </where>
    </select>
    
    <!-- 查询账户适当性情况-其他信息明细 -->
    <select id="queryQtxxmx" resultType="com.apex.sdx.api.vo.khgl.ZhsdxqkVo">
        SELECT
            t.khh,
            t.khqc AS khmc,
            CASE
                WHEN t.jymdjg = 0 THEN '正常'
                WHEN t.jymdjg IN (1, 2) THEN '异常'
                ELSE NULL
            END AS zhqk,
            CASE
                WHEN t.jymdjg = 0 THEN '正常'
                WHEN t.jymdjg = 1 THEN '黑名单'
                WHEN t.jymdjg = 2 THEN '疑似黑名单'
                ELSE NULL
            END AS jymdjg
        FROM sdx.THMDJYJGLS t
        <where>
            t.jymdlx = 1
            <if test="khh != null and khh != ''">
                AND t.khh = #{khh}
            </if>
            <if test="qtxxzt != null and qtxxzt != ''">
                <choose>
                    <when test="qtxxzt == '0'">AND t.jymdjg = 0</when>
                    <when test="qtxxzt == '1'">AND t.jymdjg = 1</when>
                    <when test="qtxxzt == '2'">AND t.jymdjg = 2</when>
                </choose>
            </if>
        </where>
    </select>
    
    <!-- 根据风险等级和投资者类型查询投资者数量统计 -->
    <select id="queryTzzcfbStats" resultType="java.util.Map">
        SELECT
            CASE
                WHEN cpdj = -1 OR cpdj IS NULL THEN 0
                ELSE cpdj
            END as cpdj,
            tzzfl,
            COUNT(1) as "count"
        FROM sdx.tkhxx
        WHERE khzt = 0
        GROUP BY
            CASE
                WHEN cpdj = -1 OR cpdj IS NULL THEN 0
                ELSE cpdj
            END,
            tzzfl
        ORDER BY tzzfl, cpdj
    </select>
    
    <!-- 查询风险等级字典 -->
    <select id="queryFxcsnlDic" resultType="java.util.Map">
        SELECT ibm, note FROM livebos.txtdm WHERE fldm = 'SDX_FXCSNL' ORDER BY ibm ASC
    </select>
    
    <!-- 查询账户状态明细 -->
    <select id="queryZhztmx" resultType="com.apex.sdx.api.vo.khgl.ZhsdxqkVo">
        SELECT
            CASE WHEN t.khzt = 0 THEN '正常' ELSE '异常' END AS zhqk,
            t.khh,
            t.khmc,
            dt.note AS zhzt
        FROM sdx.tkhxx t
        LEFT JOIN livebos.txtdm dt
            ON dt.fldm = 'SDX_KHZT'
            AND dt.ibm = t.khzt
        <where>
            <if test="khh != null and khh != ''">
                AND t.khh = #{khh}
            </if>
            <if test="zhzt != null and zhzt != ''">
                AND t.khzt = #{zhzt}
            </if>
            <if test="zhqk != null and zhqk != ''">
                AND (
                    (#{zhqk} = '0' AND t.khzt = 0)
                    OR (#{zhqk} = '1' AND t.khzt != 0)
                )
            </if>
        </where>
    </select>
</mapper>