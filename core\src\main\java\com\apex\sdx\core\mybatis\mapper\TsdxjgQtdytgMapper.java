package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.api.vo.query.CpccsdxVo;
import com.apex.sdx.api.vo.query.TgcpsdxVo;
import com.apex.sdx.core.mybatis.entity.TsdxjgQtdytg;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tsdxjg_qtdytg(全体已订阅投顾适当性)】的数据库操作Mapper
* @createDate 2025-06-11 18:54:56
* @Entity com.apex.sdx.core.mybatis.entity.TsdxjgQtdytg
*/
public interface TsdxjgQtdytgMapper extends BaseMapper<TsdxjgQtdytg> {

    @Select({"<script> " +
            "select b.cpmc, a.* " +
            "from sdx.tsdxjg_qtdytg a left join sdx.tcp_tgcpxx b on a.cpdm = b.cpdm " +
            "where a.KHH = #{khh} and a.SJLY = #{sjlx} " +
            " and a.rq = #{rq}  " +
            "   <if test='onlysdx == true'>" +
            "       and (a.sdxjg = -1 or a.sdxjg = 0) " +
            "   </if>" +
            "order by a.rq desc" +
            "</script>"})
    List<TgcpsdxVo> selectByKhhSjlxAndSdxjg(@Param("khh") String khh, @Param("sjlx") Integer sjlx, @Param("onlysdx") boolean onlysdx, @Param("rq") String rq);

}




