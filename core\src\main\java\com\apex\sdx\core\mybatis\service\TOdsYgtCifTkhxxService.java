package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhxx;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *
 */
public interface TOdsYgtCifTkhxxService extends IService<TOdsYgtCifTkhxx> {

    /**
     * 通过客户号获取客户信息
     *
     * @param khh    客户号
     * @param isNull 客户信息是否允许为空，不允许为空时抛出业务异常，false|不允许；true|允许。送null默认为允许
     * @return
     * @throws Exception
     */
    TOdsYgtCifTkhxx getKhxxByKhh(String khh, Boolean isNull);
}
