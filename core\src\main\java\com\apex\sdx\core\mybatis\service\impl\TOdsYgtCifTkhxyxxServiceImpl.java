package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.vo.query.XyzhVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhxyxx;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhxyxxService;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTkhxyxxMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_ods_ygt_cif_tkhxyxx】的数据库操作Service实现
* @createDate 2025-02-07 15:10:03
*/
@Service
public class TOdsYgtCifTkhxyxxServiceImpl extends ServiceImpl<TOdsYgtCifTkhxyxxMapper, TOdsYgtCifTkhxyxx>
    implements TOdsYgtCifTkhxyxxService{

    @Override
    public TOdsYgtCifTkhxyxx getXyzhjbxx(String khh) {
        LambdaQueryWrapper<TOdsYgtCifTkhxyxx> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(TOdsYgtCifTkhxyxx::getKhh,
                TOdsYgtCifTkhxyxx::getEmail,
                TOdsYgtCifTkhxyxx::getPjzf,
                TOdsYgtCifTkhxyxx::getXydj,
                TOdsYgtCifTkhxyxx::getZxyxq)
                .eq(TOdsYgtCifTkhxyxx::getKhh, khh);
        return this.getOne(queryWrapper, false);
    }

    @Override
    public XyzhVo getXyzhxx(String khh) {
        return this.baseMapper.selectXyzhxx(khh);
    }
}




