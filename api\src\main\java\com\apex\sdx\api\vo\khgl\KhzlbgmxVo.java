package com.apex.sdx.api.vo.khgl;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-02-13
 * @Description:
 */
@Getter
@Setter
public class KhzlbgmxVo {
    /**
     * 客户号
     */
    @LiveProperty(note = "客户号", index = 1)
    private String khh;

    @LiveProperty(note = "id", index = 2)
    private Long id;

    @LiveProperty(note = "修改日期", index = 3)
    private Integer xgrq;

    @LiveProperty(note = "修改时间", index = 4)
    private String xgsj;

    @LiveProperty(note = "字段", index = 5)
    private String zd;

    @LiveProperty(note = "字段名称", index = 6)
    private String zdmc;

    @LiveProperty(note = "旧值", index = 7)
    private String oldvalue;

    @LiveProperty(note = "新值", index = 8)
    private String newvalue;

    @LiveProperty(note = "旧值名称", index = 9)
    private String oldname;

    @LiveProperty(note = "新值名称", index = 10)
    private String newname;

    @LiveProperty(note = "修改人", index = 11)
    private Integer xgr;

    @LiveProperty(note = "修改渠道", index = 12)
    private Integer xgqd;
}
