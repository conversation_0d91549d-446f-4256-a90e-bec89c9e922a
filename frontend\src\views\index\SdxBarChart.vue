<template>
  <div>
    <ul class="top_list">
      <li><span class="top1" v-if="type==1">1</span></li>
      <li><span class="top2" v-if="type==1">2</span></li>
      <li><span class="top3" v-if="type==1">3</span></li>
      <li><span class="top" v-if="type==2">4</span></li>
      <li><span class="top" v-if="type==2">5</span></li>
      <li><span class="top" v-if="type==2">6</span></li>
    </ul>
    <div :id="chartsId" style=" width:95%; height:158px;margin: 0 25px auto;left: 5%;position: relative;">
    </div>
  </div>

</template>
<script>
import {defineComponent} from 'vue';

let barCharts = [];
export default defineComponent({
  props: ["dataset", "dataset1", "title","type"],
  data() {
    return {
      labels: [],//名称  ['私募债', '退市整理', '风险警示', '科创板', '创业板']
      values: [],//百分比  [[10, 20, 30, 40, 50],[90, 80, 70, 60, 50],[90, 80, 70, 60, 50] ]
      values1:[],//数量
      legend: ['适当', '不适当', '不通过'],
      attackSourcesColor: [
        new this.$echarts.graphic.LinearGradient(0, 1, 1, 1, [
          {offset: 0, color: '#36A6FC'},
          //{offset: 1, color: '#00B42A'},
        ]),
        new this.$echarts.graphic.LinearGradient(0, 1, 1, 1, [
          {offset: 0, color: '#FFC730'},
          //{offset: 1, color: '#FF1919'},
        ]),
        new this.$echarts.graphic.LinearGradient(0, 1, 1, 1, [
          {offset: 0, color: '#FD4521'},
          //{offset: 1, color: '#FF1919'},
        ]),
      ],
    }
  },
  methods: {
    initBarChart() {
      let el = document.getElementById(this.chartsId)
      barCharts[this.chartsId] = this.$echarts.init(el);
      this.setOption();

      // 添加窗口大小变化监听
      window.addEventListener('resize', this.handleResize);
    },
    setOption() {
      this.labels = this.$props.dataset[0];
      this.values = this.$props.dataset[1];
      this.values1 = this.$props.dataset[2];
      let option = this.getOption();
      barCharts[this.chartsId].setOption(option);

      // 初始渲染后立即调整尺寸
      this.$nextTick(() => {
        barCharts[this.chartsId]?.resize();
      });
    },
    handleResize() {
      if (barCharts[this.chartsId]) {
        barCharts[this.chartsId].resize();
      }
    },
    getOption() {
      let _this = this;
      let option = {
        tooltip: {
          show: true,
          className: 'myTooltip', // 指定自定义类名
          trigger: 'axis',
          axisPointer: {// 坐标轴指示器，坐标轴触发有效
            type: 'shadow',
          },
          formatter: function (params) {
            let dataIndex = params[0].dataIndex;//获取当前索引值
            let str = '<div style="width:200px;padding: 10px;background-color: #D9E1F0;border-radius: 10px;">' +
                '           <div style="margin-bottom: 5px"><span style="color: #454A55;">' + params[0].name + '</span></div>' +
                '          <div style="padding: 0 0 0 10px;background-color: #fff;border-radius: 10px;">';
            for(let i = 0; i < params.length; i++){
              str += '            <p>' +
                  '              <span style="color: #454A55;width: 33%;display: inline-block;">' + params[i].seriesName + '</span>' +
                  '              <span style="color: #454A55;width: 33%;display: inline-block;">' + params[i].value + '%</span>' +
                  '              <span style="color: #454A55;width: 33%;display: inline-block;">' + _this.values1[i][dataIndex] + '</span>' +
                  '            </p>'
            }
            str +=  '           </div>' +
                '      </div>';
            return  str;
          }
        },
        grid:
            {
              left: '26%',
              right: '28%',
              top: '5%',
              bottom: '10%'
            },
        legend: {
          show: false,
          data: this.legend,
          textStyle: {
            color: '#888888'
          },
          bottom: 20,
          left: 'center',
        },
        yAxis: [
          {
            type: 'category',
            data: this.labels,
            axisLine: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false  // 隐藏刻度线
            },
            axisLabel: {
              show: true,  // 显示刻度标签
              align: 'left',
              margin: 145,//刻度标签与轴线之间的距离。
              color: '#000033',
              formatter: function (value, index) {
                return '{a|' + value + '}';
              },
              rich: {
                a: {
                  fontSize: 14,
                  textAlign: 'left',
                }
              },
            },
          },
          {
            type: 'category',
            data: this.values1[3],
            axisLine: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false  // 隐藏刻度线
            },
            axisLabel: {
              show: true,  // 显示刻度标签
              color: '#000033',
              fontSize: '14px',
              padding: [0, 0, 0, 0],
              align: 'right',
              margin: 80,
              formatter: function (value) {
                return value ;
              },
            },
          },
        ],
        xAxis: [{
          type: 'value', show: false,
        },
        ],
        series: [
          ...this.getSeriesData(),
        ]
      };
      return option;
    },
    getSeriesData() {
      let _this = this;
      let data = [];
      this.legend.forEach((item, index) => {
        data.push({
          type: 'bar',
          name: item,
        //legendHoverLink: false,
          stack: 'total',
          barWidth: '30%',
          // 调整柱状图之间的间隔
          barCategoryGap: '80%',
          // 调整同一系列中柱状图之间的间隔
          barGap: '200%',
          itemStyle: {
            color: _this.attackSourcesColor[index], // 设置柱条颜色
            borderRadius: index === 0 ? [4, 0, 0, 4] : index === 1 ? [0, 0, 0, 0] : [0, 4, 4, 0], // 设置柱条圆角
          },
          data: _this.values[index],
        });
      });
      return data;
    },

  },
  mounted() {
    if (this.$props.dataset && this.$props.dataset.length > 0) {
      this.initBarChart()
    }
  },
  computed: {
    chartsId() {
      return "id_" + this.title
    },
  },
  watch: {
    dataset(newValue, oldValue) {
      if (newValue && newValue.length > 0 && JSON.stringify(newValue) != JSON.stringify(oldValue)) {
        if (barCharts[this.chartsId]) {
          this.setOption();
        } else {
          this.initBarChart();
        }
      }
    }
  },
  unmounted() {
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize);
    if (barCharts[this.chartsId]) {
      barCharts[this.chartsId].dispose();
      delete barCharts[this.chartsId];
    }
  },
});
</script>

<style scoped>

ul.top_list {
  float: left;
  margin-top: 15px;
  margin-left: 20px;
}

.top1 {
  width: 25px; /* 或你需要的尺寸 */
  height: 25px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/sdx1.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  float: left;
  margin-bottom: 18px;
  margin-top: 3px;

  text-align: center;
  font-weight: 400;
  font-size: 12px;
  color: #000033;
  line-height: 20px;
}

.top2 {
  width: 25px; /* 或你需要的尺寸 */
  height: 25px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/sdx2.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  float: left;
  margin-bottom: 20px;

  text-align: center;
  font-weight: 400;
  font-size: 12px;
  color: #000033;
  line-height: 20px;
}

.top3 {
  width: 25px; /* 或你需要的尺寸 */
  height: 25px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/sdx3.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  float: left;
  margin-bottom: 20px;

  text-align: center;
  font-weight: 400;
  font-size: 12px;
  color: #000033;
  line-height: 20px;
}

.top {
  width: 19px; /* 或你需要的尺寸 */
  height: 20px; /* 或你需要的尺寸 */
  float: left;
  margin-bottom: 18px;
  margin-top: 5px;
  background: linear-gradient(0deg, #BF935F 0%, #ECC9A4 100%);
  border-radius: 4px;

  font-size: 12px;
  color: #FFFFFF;
  line-height: 20px;
  text-align: center;
  font-weight: 400;
}

:deep(.myTooltip){
  padding: 0 !important;
  border-radius: 10px !important;
  border-width: 0px !important;
}
</style>