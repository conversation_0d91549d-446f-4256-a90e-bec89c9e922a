package com.apex.sdx.api.req.query;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025-01-16
 * @Description:
 */
@Setter
@Getter
public class KhsjzbReq {

    @NotNull(message = "不能为空")
    @LiveProperty(note = "客户号",index = 1)
    private String khh;

    @LiveProperty(note = "日期",index = 2)
    private String rq;

    @LiveProperty(note = "指标code",index = 3)
    private String idxCodes;//这个支持多值

}