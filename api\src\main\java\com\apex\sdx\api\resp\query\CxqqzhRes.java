package com.apex.sdx.api.resp.query;

import com.apex.sdx.api.resp.common.R;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/1/23 11:41
 * @Description: TODO
 */
@Setter
@Getter
@SuperBuilder
public class CxqqzhRes extends R {

    @LiveProperty(note = "客户号", index = 1)
    private String khh;

    @LiveProperty(note = "投资者分类", index = 2)
    private Integer tzzfl;

    @LiveProperty(note = "投资者分类名称", index = 3)
    private String tzzflmc;

    @LiveProperty(note = "评级总分", index = 4)
    private BigDecimal pjzf;

    @LiveProperty(note = "期权业务账户", index = 5)
    private String ywzh;

    @LiveProperty(note = "沪市限购额度上限", index = 6)
    private BigDecimal shxgedsx;

    @LiveProperty(note = "深市限购额度上限", index = 7)
    private BigDecimal szxgedsx;
}
