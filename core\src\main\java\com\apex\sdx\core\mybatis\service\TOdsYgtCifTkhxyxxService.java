package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.api.vo.query.XyzhVo;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhxyxx;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTywzh;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_ods_ygt_cif_tkhxyxx】的数据库操作Service
* @createDate 2025-02-07 15:10:03
*/
public interface TOdsYgtCifTkhxyxxService extends IService<TOdsYgtCifTkhxyxx> {

    TOdsYgtCifTkhxyxx getXyzhjbxx(String khh);

    XyzhVo getXyzhxx(String khh);
}
