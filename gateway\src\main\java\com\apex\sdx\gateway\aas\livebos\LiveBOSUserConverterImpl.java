package com.apex.sdx.gateway.aas.livebos;

import com.alibaba.fastjson.JSONObject;
import com.apex.ams.livebos.services.UserInfoReply;
import com.apex.sdx.gateway.aas.common.session.UserSession;
import com.apex.sdx.gateway.aas.modules.index.model.AuthUser;
import com.apex.sdx.gateway.base.service.UserService;
import com.apexsoft.livebos.ILiveBOSUserConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class LiveBOSUserConverterImpl implements ILiveBOSUserConverter {
    @Autowired
    UserService userService;

    @Override
    public AuthUser buildUser(UserInfoReply userInfoReply) {
        //构造登录用户信息，该登录用户信息会被存到当前会话中。
        AuthUser user = new AuthUser();
        user.setId(userInfoReply.getUserId());
        user.setUserId(userInfoReply.getLoginId()+"");
        user.setUserName(userInfoReply.getName());

        JSONObject userinfo = null;
        try{
            userinfo = userService.getUserInfo(userInfoReply.getLoginId());
            UserSession.updateUserPhoto(userinfo.getString("id"), userinfo.getString("photo"));
            userinfo.remove("photo");
        }catch (Exception e){
            userinfo = new JSONObject();
            userinfo.put("id", userInfoReply.getUserId());
            userinfo.put("userid", userInfoReply.getLoginId());
            userinfo.put("orgid", userInfoReply.getOrgId());
            userinfo.put("name", userInfoReply.getName());
            userinfo.put("status", userInfoReply.getStatus());
            userinfo.put("lastlogin", userInfoReply.getLastLogin());
            userinfo.put("grade", userInfoReply.getGrade());
            log.error(e.getMessage(),e);
        }
        user.setUser(userinfo);
        return user;
    }
}