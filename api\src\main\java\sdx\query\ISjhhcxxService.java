package sdx.query;

import com.apex.sdx.api.req.query.SjhcxxcxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.vo.khgl.SjhcxxVo;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR>
 * @Date 2025-02-14
 * @Description:
 */
public interface ISjhhcxxService {
    @LiveMethod(paramAsRequestBody = true, note = "手机核查信息查询")
    QueryPageResponse<SjhcxxVo> sjhcxxcx(SjhcxxcxReq req) throws Exception;
}
