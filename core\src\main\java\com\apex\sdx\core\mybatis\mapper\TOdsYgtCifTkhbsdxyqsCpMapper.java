package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.api.vo.query.CpbsdxyqslscxVo;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhbsdxyqsCp;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_ods_ygt_cif_tkhbsdxyqs_cp】的数据库操作Mapper
 * @createDate 2025-06-06 16:59:26
 * @Entity com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhbsdxyqsCp
 */
public interface TOdsYgtCifTkhbsdxyqsCpMapper extends BaseMapper<TOdsYgtCifTkhbsdxyqsCp> {

    @Select({"<script> " +
            "select b.cpmc ,a.* from ods.t_ods_ygt_cif_tkhbsdxyqs_cp a left join sdx.tcp_cpxx b on a.cpdm = b.cpdm  " +
            "<where>" +
            " <if test='ksrq != null and ksrq != \"\"'> " +
            " and a.qsrq &gt;= #{ksrq} " +
            " </if> " +
            " <if test='jsrq != null and jsrq != \"\"'> " +
            " and a.qsrq &lt;= #{jsrq} " +
            " </if> " +
            " <if test='cpdm != null and cpdm != \"\"'> " +
            " and a.cpdm = #{cpdm} " +
            " </if> " +
            " <if test='khh != null and khh != \"\"'> " +
            " and a.khh = #{khh} " +
            " </if> " +
            "</where> " +
            "</script>"})
    List<CpbsdxyqslscxVo> cpbsdxyqslscx(@Param("khh") String khh,@Param("ksrq") String ksrq, @Param("jsrq") String jsrq, @Param("cpdm") String cpdm);
}




