<template>
  <div class="dxsdx_khqjgk_nav">
    <div class="dxsdx_khqjgk_title">适当性事件</div>
    <div class="cljg-item">
      <span>处理状态：</span>
      <a-select
        style="width: 200px"
        :options="dictArray?.SDX_SJZT"
        :field-names="{ label: 'note', value: 'ibm' }"
        v-model:value="clzt"
        placeholder="请选择处理状态"
        allow-clear
        @select="getSdxsj"
      />
      <div class="sdxsj-content">
        <a-table
          :columns="columns"
          :data-source="data"
          :scroll="{ x: 1200 }"
          :expand-column-width="50"
          :loading="loading"
          :pagination="pagination"
          row-key="id"
          @change="pageChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'sjxq'">
              <a @click="showSjxq(record)">事件详情</a>
            </template>
          </template>
        </a-table>
        <sjxq-modal
          title="事件详情"
          v-model:open="open"
        />
      </div>
    </div>
  </div>
</template>
<script>
import {computed, defineComponent} from "vue";
import SjxqModal from "@views/dhcx/sdxsj/sjxqModal.vue";
import {commonApi} from "@/api/common";
import {message} from "ant-design-vue";
import {getDcNote,formatDate} from "@utils/bussinessUtils";

export default defineComponent({
  name: "sdxsj",
  components: { SjxqModal },
  inject: ["khh"],
  props: ['dictArray'],
  provide() {
    return {
      sjid: computed(() => this.sjid)
    }
  },
  data() {
    return {
      clzt: null,
      columns: [
        { title: '生成日期', dataIndex: 'scrq', key: 'scrq', width: 100, fixed: true,
          customRender: ({text})=> {
            return formatDate(text+'');
          },
        },
        { title: '生成时间', dataIndex: 'scsj', key: 'scsj', width: 100 },
        { title: '事件ID', dataIndex: 'id', key: 'id', width: 100 },
        { title: '事件名称', dataIndex: 'sjmc',  key: 'sjmc', width: 150 },
        { title: '事件详情', dataIndex: 'sjxq',  key: 'sjxq', width: 200 },
        { title: '处理状态', dataIndex: 'clzt',  key: 'clzt', width: 100,
          customRender: ({text})=> {
            return getDcNote("SDX_SJZT", text, this.dictArray)
          }
        },
      ],
      data: [],
      open: false,
      loading: false,
      current: 1,
      pageSize: 10,
      total: 0,
      sjid: null
    };
  },
  mounted() {
    this.getSdxsj();
  },
  watch: {
    clzt(newVal) {
      if (newVal == null) {
        this.getSdxsj();
      }
    },
  },
  computed: {
    pagination() {
      return {
        total: this.total,
        current: this.current,
        pageSize: this.pageSize,
        //showQuickJumper: true,
        showSizeChanger: true,
        showTotal(total) {
          return "共 " + total + " 条";
        },
      };
    }
  },
  methods: {
    showSjxq(record) {
      this.sjid = record.id
      this.open = true
    },
    pageChange(page){
      this.current = page.current;
      this.pageSize = page.pageSize;
      this.getSdxsj();
    },
    getSdxsj(){
      this.loading = true;
      commonApi.executeAMS(
        "sdx.query.ISdxsjService",
        "sdxsjcx",
        { khh: this.khh, clzt: this.clzt, isSearchCount: true, pagenum: this.current, pagesize: this.pageSize},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.data = res.records || [];
        this.total = res.total;
      }).finally(() => {
        this.loading = false;
      })
    },
  },
});
</script>
<style scoped>
.dxsdx_khqjgk_nav {
  background-color: #fff;
  border-radius: 8px;
  height: 100%;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.dxsdx_khqjgk_title {
  line-height: 52px;
  margin: 0 10px;
  border-bottom: 1px solid #eee;
  padding-left: 8px;
  font-size: 14px;
}

.dxsdx_khqjgk_title:before {
  display: inline-block;
  vertical-align: middle;
  content: "";
  width: 4px;
  height: 18px;
  margin-top: -2px;
  border-radius: 2px;
  background-color: #b48a3b;
  margin-right: 10px;
}

.cljg-item {
  line-height: 28px;
  padding: 15px 15px 15px 25px;
}

.sdxsj-content {
  padding: 0px 10px 0 10px;
}

.dxsdx_qxsdx_qxsjz{ overflow-x: auto }
.qxsjz_item{}
.sj_sjz{ background-color: #fafafa; min-height:130px; white-space: nowrap}
.sj_sjz ul li{width: 180px;display: inline-block; vertical-align: top; margin: 5px 0 0 0; color: #333; line-height:30px; font-size: 14px; color: #333;}
.sj_sjz ul li p:nth-child(1).sj_jd{ position: relative; border-top: 1px dotted #e6e6e6; margin-top: 8px;left: 20px; width: calc(100% - 28px); margin-bottom: 20px;}
.sj_sjz ul li p:nth-child(1).sj_jd:before{content: ""; display: inline-block; position: absolute; left: -20px; top:-6px; width: 11px; height: 11px; border-radius: 5.5px; box-sizing: border-box; border: 2px solid #ecc9a4;}
.sj_sjz ul li p:nth-child(2){ color: #888;}
.sj_sjz ul li div p:nth-child(1){ line-height: 20px;}
.sj_sjz ul li p em{ display: inline-block; font-style: normal;}
.sj_sjz ul li .pp_sj p:nth-child(2){color: #00b42a; font-size: 14px;}
.sj_sjz ul li .pp_sj p:nth-child(1){white-space: break-spaces}
.sj_sjz ul li .pp_sj p em:before{content: "\e631"; font-size: 16px; height: 16px; line-height: 16px; border-radius: 10px; background-color: #f8fffa; font-family: "iconfont" !important;display: inline-block; vertical-align: middle; margin-right: 4px;}
.sj_sjz ul li .bsd_sj p:nth-child(2){color: #ff1919; font-size: 14px;}
.sj_sjz ul li .bsd_sj p em:before{content: "\e631"; font-size: 16px; height: 16px; line-height: 16px; border-radius: 10px; background-color: #fff7f8; font-family: "iconfont" !important;display: inline-block; vertical-align: middle; margin-right: 4px;}
.sj_sjz ul li:last-child p:nth-child(1).sj_jd{border-top: none;}
</style>