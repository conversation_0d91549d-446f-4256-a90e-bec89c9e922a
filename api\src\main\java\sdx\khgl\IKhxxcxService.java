package sdx.khgl;

import com.apex.sdx.api.req.khgl.ZhsdxqkReq;
import com.apex.sdx.api.req.khgl.KhxxcxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.khgl.KhxxtjVo;
import com.apex.sdx.api.vo.khgl.ZhsdxqkVo;
import com.apex.sdx.api.vo.khgl.KhxxVo;
import com.apex.sdx.api.vo.khgl.TzzcfbVo;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR> <PERSON>liang
 * @Date 2025-01-16
 * @Description:
 */
public interface IKhxxcxService {
    @LiveMethod(paramAsRequestBody = true, note = "查询客户基本信息")
    QueryResponse<KhxxVo> khjbxxcx(KhxxcxReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "客户信息统计")
    QueryResponse<KhxxtjVo> khxxtj(KhxxcxReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "账户状态明细查询")
    QueryPageResponse<ZhsdxqkVo> zhztmxcx(ZhsdxqkReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "证件有效期明细查询")
    QueryPageResponse<ZhsdxqkVo> zjyxqmxcx(ZhsdxqkReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "反洗钱风险等级明细查询")
    QueryPageResponse<ZhsdxqkVo> fxqfxdjmxcx(ZhsdxqkReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "风险测评有效明细查询")
    QueryPageResponse<ZhsdxqkVo> fxcpyxmxcx(ZhsdxqkReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "专业投资者测评明细查询")
    QueryPageResponse<ZhsdxqkVo> zytzzcpmxcx(ZhsdxqkReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "其它信息明细查询")
    QueryPageResponse<ZhsdxqkVo> qtxxmxcx(ZhsdxqkReq req) throws Exception;
    
    @LiveMethod(paramAsRequestBody = true, note = "投资者构成及分布查询")
    QueryResponse<TzzcfbVo> tzzcfbcx(KhxxcxReq req) throws Exception;
}
