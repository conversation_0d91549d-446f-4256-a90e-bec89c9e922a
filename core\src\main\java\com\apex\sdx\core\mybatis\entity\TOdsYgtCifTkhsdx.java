package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * @TableName t_ods_ygt_cif_tkhsdx
 */
@TableName(value ="t_ods_ygt_cif_tkhsdx", schema = "ods")
@Data
public class TOdsYgtCifTkhsdx implements Serializable {
    /**
     * 
     */
    @TableId
    private String khh;

    /**
     * 
     */
    private Integer sdxfl;

    /**
     * 
     */
    private String sdxlb;

    /**
     * 
     */
    private Integer cpdj;

    /**
     * 
     */
    private BigDecimal cpdf;

    /**
     * 
     */
    private Integer cprq;

    /**
     * 
     */
    private Integer cpyxq;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TOdsYgtCifTkhsdx other = (TOdsYgtCifTkhsdx) that;
        return (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
            && (this.getSdxfl() == null ? other.getSdxfl() == null : this.getSdxfl().equals(other.getSdxfl()))
            && (this.getSdxlb() == null ? other.getSdxlb() == null : this.getSdxlb().equals(other.getSdxlb()))
            && (this.getCpdj() == null ? other.getCpdj() == null : this.getCpdj().equals(other.getCpdj()))
            && (this.getCpdf() == null ? other.getCpdf() == null : this.getCpdf().equals(other.getCpdf()))
            && (this.getCprq() == null ? other.getCprq() == null : this.getCprq().equals(other.getCprq()))
            && (this.getCpyxq() == null ? other.getCpyxq() == null : this.getCpyxq().equals(other.getCpyxq()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getSdxfl() == null) ? 0 : getSdxfl().hashCode());
        result = prime * result + ((getSdxlb() == null) ? 0 : getSdxlb().hashCode());
        result = prime * result + ((getCpdj() == null) ? 0 : getCpdj().hashCode());
        result = prime * result + ((getCpdf() == null) ? 0 : getCpdf().hashCode());
        result = prime * result + ((getCprq() == null) ? 0 : getCprq().hashCode());
        result = prime * result + ((getCpyxq() == null) ? 0 : getCpyxq().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", khh=").append(khh);
        sb.append(", sdxfl=").append(sdxfl);
        sb.append(", sdxlb=").append(sdxlb);
        sb.append(", cpdj=").append(cpdj);
        sb.append(", cpdf=").append(cpdf);
        sb.append(", cprq=").append(cprq);
        sb.append(", cpyxq=").append(cpyxq);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}