package com.apex.sdx.svc;

import com.apex.sdx.api.req.khgl.KhjyqxcxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.vo.khgl.KhjyqxVo;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhjyqxService;
import com.apexsoft.LiveService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.query.IKhjyqxService;

/**
 * <AUTHOR> <PERSON>liang
 * @Date 2025-01-22
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "客户信息交易权限查询")
public class KhjyqxService implements IKhjyqxService {

    @Autowired
    TOdsYgtCifTkhjyqxService tkhjyqxService;

    @Override
    public QueryPageResponse<KhjyqxVo> khjyqxcx(KhjyqxcxReq req) throws Exception {
        Assert.notNull(req, KhjyqxcxReq::getKhh);
        QueryPageResponse<KhjyqxVo> result = new QueryPageResponse<>(1, "查询成功");
        String khh = req.getKhh();
        String ywzh = req.getYwzh();
        String gdh = req.getGdh();
        Integer jyqx = req.getJyqx();
        Integer zt = req.getZt();
        Page<KhjyqxVo> tkhjyqxList = tkhjyqxService.queryKhjyqx(khh, ywzh, gdh, jyqx, zt, req.isSearchCount(), req.getPagesize(), req.getPagenum());
        result.page(tkhjyqxList);
        return result;
    }
}
