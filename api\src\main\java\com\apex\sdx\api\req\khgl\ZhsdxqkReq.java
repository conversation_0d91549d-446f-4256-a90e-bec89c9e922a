package com.apex.sdx.api.req.khgl;

import com.apex.sdx.api.req.common.PageRequest;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> chlingyu
 *
 * @create 2025/5/09 13:50
 */
@Setter
@Getter
public class ZhsdxqkReq extends PageRequest {

  @LiveProperty(note = "账户情况,1|正常;2|异常", index = 1)
  private String zhqk;

  @LiveProperty(note = "账户状态", index = 2)
  private String zhzt;

  @LiveProperty(note = "客户号", index = 3)
  private String khh;

  @LiveProperty(note = "证件有效期", index = 4)
  private String zjjzrq;

  @LiveProperty(note = "反洗钱风险等级", index = 5)
  private String xqfxdj;

  @LiveProperty(note = "风险测评有效期", index = 6)
  private String cpyxq;

  @LiveProperty(note = "专业投资者测评有效期", index = 7)
  private String tzzpdyxq;

  @LiveProperty(note = "其他信息状态", index = 8)
  private String qtxxzt;
}
