package com.apex.sdx.api.vo.khgl;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-01-23
 * @Description:
 */
@Getter
@Setter
public class KhwjcplsVo {

    /**
     * 问卷id
     */
    @LiveProperty(note = "问卷id", index = 1)
    private Long wjid;

    /**
     * 客户号
     */
    @LiveProperty(note = "客户号", index = 2)
    private Integer khh;

    /**
     * 适当性分类
     */
    @LiveProperty(note = "适当性分类", index = 3)
    private Integer sdxfl;

    /**
     * 适当性类别
     */
    @LiveProperty(note = "适当性类别", index = 4)
    private String sdxlb;

    /**
     * 测评等级
     */
    @LiveProperty(note = "测评等级", index = 5)
    private String cpdj;

    /**
     * 测评得分
     */
    @LiveProperty(note = "测评得分", index = 6)
    private Long cpdf;

    /**
     * 测评日期
     */
    @LiveProperty(note = "测评日期", index = 7)
    private String cprq;

    /**
     * 测评有效期
     */
    @LiveProperty(note = "测评有效期", index = 8)
    private String cpyxq;

    /**
     * 通过题数
     */
    @LiveProperty(note = "通过题数", index = 9)
    private Integer tgts;

    /**
     * 问卷
     */
    @LiveProperty(note = "调查问卷名称", index = 10)
    private String dcwjmc;

}
