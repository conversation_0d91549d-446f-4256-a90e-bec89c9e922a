package com.apex.sdx.core;

/**
 * <AUTHOR>
 * @Date 2023/8/23 17:14
 * @Description: TODO
 */
public interface Constants {
    /**文本*/
    String CHARTTYPE_TXT = "0";
    /**折线图*/
    String CHARTTYPE_LINE = "1";
    /**柱状图*/
    String CHARTTYPE_BAR = "2";
    /**阴影折线图*/
    String CHARTTYPE_LINE_Y = "3";
    /**无阴影折线图*/
    String CHARTTYPE_LINE_N = "4";

    /**饼图*/
    String CHARTTYPE_PIE = "6";

    String CHARTTYPE_FUNNEL = "10";


    /** 处理成功*/
    int SERVICE_STATUS_CLCG = 8;
    /** 处理失败*/
    int SERVICE_STATUS_CLSB = 9;


    /** 预警周期-轮询触发*/
    String YJZB_YJZQ_CYCLE = "1";
    /** 预警周期-一天一次*/
    String YJZB_YJZQ_ONCE = "2";

    /**短信发送*/
    int MSG_FSQD_SMS = 1;
    /**邮箱发送*/
    int MSG_FSQD_EMAIL = 2;
    /**集中交易*/
    int YWZH_YWXT_JZJY = 1000;
    /**融资融券*/
    int YWZH_YWXT_RZRQ = 1001;
    /**股票期权*/
    int YWZH_YWXT_GPQQ = 1002;
    /**场外业务*/
    int YWZH_YWXT_CWYW = 1003;

}
