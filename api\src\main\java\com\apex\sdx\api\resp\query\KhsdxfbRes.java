package com.apex.sdx.api.resp.query;

import com.apex.sdx.api.resp.common.R;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/5/9 13:57
 * @Description: TODO
 */
@Setter
@Getter
public class KhsdxfbRes implements Serializable {

    @LiveProperty(note = "总数", index = 1)
    private String zs;

    @LiveProperty(note = "占比", index = 2)
    private String zb;

    @LiveProperty(note = "日增长", index = 3)
    private String rzz;

    @LiveProperty(note = "月增长", index = 4)
    private String yzz;

    @LiveProperty(note = "投资者分类", index = 5)
    private String tzzfl;
}
