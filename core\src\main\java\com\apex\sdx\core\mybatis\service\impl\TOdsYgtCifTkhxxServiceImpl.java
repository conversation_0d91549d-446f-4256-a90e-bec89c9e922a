package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.core.mybatis.entity.Tkhxx;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhxx;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhxxService;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTkhxxMapper;
import org.springframework.stereotype.Service;
import com.apex.sdx.core.exception.BusinessException;

/**
 *
 */
@Service
public class TOdsYgtCifTkhxxServiceImpl extends ServiceImpl<TOdsYgtCifTkhxxMapper, TOdsYgtCifTkhxx>
    implements TOdsYgtCifTkhxxService{


    @Override
    public TOdsYgtCifTkhxx getKhxxByKhh(String khh, Boolean isNull) {
        QueryWrapper<TOdsYgtCifTkhxx> wrapper = new QueryWrapper<>();
        wrapper.eq("khh", khh);
        TOdsYgtCifTkhxx khxx = null;
        try {
            khxx = this.getOne(wrapper);
        } catch (Exception e) {
            String note = String.format("查询客户信息[客户号=%s]异常:%s", khh, e.getMessage());
            log.error(note, e);
            throw new BusinessException(-602, note);
        }
        if (khxx == null && isNull != null && !isNull) {
            throw new BusinessException(-601, "客户号[" + khh + "]不存在");
        }
        return khxx;
    }
}




