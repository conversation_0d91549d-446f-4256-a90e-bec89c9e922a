package com.apex.sdx.api.req.khgl;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-02-08
 * @Description:
 */
@Setter
@Getter
public class DcwjxqcxReq {
    @LiveProperty(note = "客户号", index = 1)
    private String khh;

    @LiveProperty(note = "客户调查问卷id", index = 2)
    private Integer khdcwjid;

    @LiveProperty(note = "适当性分类", index = 3)
    private Integer sdxfl;

    @LiveProperty(note = "金融机构", index = 4)
    private String jrjg;

    @LiveProperty(note = "客户类别", index = 5)
    private Integer khlb;

    @LiveProperty(note = "适当性类别", index = 6)
    private String sdxlb;
}
