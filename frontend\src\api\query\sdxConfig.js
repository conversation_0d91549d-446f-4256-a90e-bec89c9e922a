import request from '@/utils/request'
export default {
    // 查询参数值
    findCsz(gllx,gldx) {
        return request({
            url: `sdx.sdxgl.ICxSdxCszService/cxSdxCsz`,
            method: 'post',
            data: {gllx: gllx,gldx:gldx}
        })
    },
    // 保存适当性配置
    saveSdxConfig(data) {
        return request({
            url: `sdx.sdxgl.ISaveSdxInfoService/execute`,
            method: 'post',
            data: data
        })
    },
    // 获取适当性规则
    querySdxConfig(gzid,gzlb,cpgzjb) {
        return request({
            url: `sdx.sdxgl.IGetSdxInfoService/getSdxInfo`,
            method: 'post',
            data: {gzid: gzid,gzlb:gzlb,cpgzjb:cpgzjb}
        })
    },
    // 查询系统代码
    queryXtdm(fldm) {
        return request({
            url: `sdx.sdxgl.ICxXtdmService/cxXtdm`,
            method: 'post',
            data: {fldm: fldm}
        })
    },
    // 查询准入参数代码
    queryZrcsdm(csdm) {
        return request({
            url: `sdx.sdxgl.ICxZrcsdmService/cxZrcsdm`,
            method: 'post',
            data: {csdm: csdm}
        })
    },
    // 查询产品类型代码
    queryCplx(cplxbm) {
        return request({
            url: `sdx.sdxgl.ICxCplxService/cxCplx`,
            method: 'post',
            data: {cplxbm: cplxbm}
        })
    },
    // 查询产品信息代码
    queryCpxx(cpdm,cpmc) {
        return request({
            url: `sdx.sdxgl.ICxCpxxService/cxCpxx`,
            method: 'post',
            data: {cpdm: cpdm,cpmc:cpmc}
        })
    },
}
