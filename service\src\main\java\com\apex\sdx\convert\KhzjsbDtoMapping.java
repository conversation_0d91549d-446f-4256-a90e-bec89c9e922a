package com.apex.sdx.convert;

import com.apex.sdx.api.vo.query.KhsjzbVo;
import com.apex.sdx.core.converter.IMapping;
import com.apex.sdx.core.converter.MapStructConfig;
import com.apex.sdx.core.mybatis.entity.TicKhsjzb;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025-04-21
 * @Description:
 */
@Mapper(config = MapStructConfig.class)
@Component
public interface KhzjsbDtoMapping extends IMapping<KhsjzbVo, TicKhsjzb> {//class会自动生成
}
