package com.apex.sdx.gateway.base.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.apex.ams.livebos.services.*;
import com.apex.sdx.gateway.base.model.CommonResponse;
import com.apex.sdx.gateway.base.config.menu.OtherMenuItem;
import com.apex.sdx.gateway.base.config.menu.OtherMenuItems;
import com.apex.sdx.gateway.base.dao.LivebosGrpcDao;
import com.apex.sdx.gateway.base.dao.UserDao;
import com.apex.sdx.gateway.base.model.JSONResponse;
import com.apex.sdx.gateway.common.redis.RedisStringDao;
import com.apex.sdx.gateway.common.utils.Constant;
import com.apex.sdx.gateway.common.utils.PingYinUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName: LbProjectService
 * @Description: 方案微服务消费类
 * 江山如此多娇，引无数英雄竞折腰--XFH
 */

@Slf4j
@Service
public class LbProjectService {


    @Value("${server.livebos-path}")
    private String livebosPath;

    @Value("${application.debug}")
    boolean applicationDebug;

    @Resource
    OtherMenuItems otherMenuItems;

    @Autowired
    LivebosGrpcDao livebosGRPC;
    @Autowired
    RedisStringDao redisStringDao;
    @Autowired
    UserDao userDao;


    /**
     * 获取方案列表
     *
     * @return
     */
    /*public CommonResponse queryLbProject() {
        CommonResponse commonResponse;
        try {
            AuthUser<JSONObject> authUser = UserSession.getUserSession();
            JSONObject userInfo = authUser.getUser();
            CommonRequest req = CommonRequest.newBuilder()
                    .setTimeStamp(System.currentTimeMillis())
                    .setUid(userInfo.getString("id"))
                    .build();
            LbProjectResponse resp = stub.queryLbProject(req);
            if (resp.getCode() > 0) {
                commonResponse = new CommonResponse(JSONResponse.CODE_SUCCESS, "");
                commonResponse.setCount(resp.getCount());
                commonResponse.setRecords(JSON.parseObject(JsonFormat.printer().print(resp)).getJSONArray("records"));
            } else {
                commonResponse = new CommonResponse(JSONResponse.CODE_FAIL, resp.getNote());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
            commonResponse = new CommonResponse(JSONResponse.CODE_FAIL, e.getMessage());
        }
        return commonResponse;
    }*/


    /**
     * 设置用户方案
     *
     * @param id          用户ID
     * @param projectName 方案名称
     * @return
     */
    public CommonResponse getProjectMenus(int id, String projectName) {
        CommonResponse commonResponse;
        String menus= "";
        try {
            //menus = redisStringDao.getValue(Constant.REDIS_CACHE_PREFIX + "_menus_" + id + "_" + projectName);
            //左边列表菜单
            JSONObject menuJ = new JSONObject();
            if (StringUtils.isNotEmpty(menus)) {
                commonResponse = new CommonResponse(JSONResponse.CODE_SUCCESS, "");
                commonResponse.setData(JSONObject.parseObject(menus));
            } else {
                UserProjectReply userProjectReply = livebosGRPC.userProject(id, projectName);
                if (userProjectReply.getResult() > 0) {
                    commonResponse = new CommonResponse(JSONResponse.CODE_SUCCESS, "");
                    UserProjectInfo userProjectInfo = userProjectReply.getUserProject();
                    if (userProjectInfo.hasMenuTree()) {
                        MenuTree menuTree = userProjectInfo.getMenuTree();
                        if (menuTree.hasMenu()) {
                            Menu menu = menuTree.getMenu();
                            JSONArray menuItem = new JSONArray();
                            List<MenuItem> menuItems = menu.getItemList();
                            for (MenuItem m : menuItems) {
                                JSONObject item = new JSONObject();

                                item.put("name", m.getTitle(0).getText());
                                item.put("key", PingYinUtil.getFirstSpell(m.getTitle(0).getText()));
                                item.put("path", "");
                                item.put("icon", "SettingOutlined");
                                item.put("iframe", true);
                                item.put("objectName", m.getObject());
                                if (m.hasMenu()) {
                                    item = jxMenuItem(m, item);
                                } else {
                                    item.put("icon", "HomeOutlined");
                                    String objectName = m.getObject();
                                    if (objectName.startsWith("VUE_")) {
                                        item.put("path", m.getUrl());
                                        item.put("iframe", false);
                                    } else {
                                        if(!isLivebosNativeObject(m.getUrl())) {
                                            item.put("path", livebosPath + m.getUrl() + "&hideTitlebar=true");
                                        }else{
                                            item.put("path", livebosPath + m.getUrl());
                                        }
                                    }
                                }
                                menuItem.add(item);
                            }
                            menuJ.put("menus", menuItem);

                            redisStringDao.setKey(Constant.REDIS_CACHE_PREFIX + "_menus_" + id + "_" + projectName, menuJ.toJSONString(), Constant.REDIS_TIMEOUT);
                        } else {
                            commonResponse = new CommonResponse(JSONResponse.CODE_FAIL, "[" + userProjectInfo.getName() + "]没有功能权限");
                        }
                    } else {
                        commonResponse = new CommonResponse(JSONResponse.CODE_FAIL, "[" + userProjectInfo.getName() + "]没有功能权限");
                    }
                } else {
                    commonResponse = new CommonResponse(JSONResponse.CODE_FAIL, userProjectReply.getMessage());
                }
            }
            //右上角图标菜单

            menuJ.put("iconItems", getIconItems(id+""));
            menuJ.put("textItems", getTextItems(id+""));



            commonResponse.setData(menuJ);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
            commonResponse = new CommonResponse(JSONResponse.CODE_FAIL, e.getMessage());
        }
        return commonResponse;

    }

    /**
     * 获取柜员图标菜单
     * @param uid
     * @return
     */
    private JSONArray getIconItems(String uid){
        JSONArray iconItemArray = new JSONArray();
        List<OtherMenuItem> iconItemList = otherMenuItems.getIconItems();
        for(OtherMenuItem iconItem:iconItemList){
            String func = iconItem.getFunc();
            //判断柜员没有权限则不展示
            if(StringUtils.isNotEmpty(func) && !livebosGRPC.userHasRight(uid,func)){
                continue;
            }
            iconItemArray.add(JSONObject.parseObject(JSON.toJSONString(iconItem)));
        }

        return iconItemArray;
    }


    private JSONArray getTextItems(String uid){
        JSONArray textItemArray = new  JSONArray();

        List<OtherMenuItem> textItemList = otherMenuItems.getTextItems();
        for(OtherMenuItem textItem:textItemList){
            String func = textItem.getFunc();
            //判断柜员没有权限则不展示
            if(StringUtils.isNotEmpty(func) && !livebosGRPC.userHasRight(uid,func)){
                continue;
            }
            textItemArray.add(JSONObject.parseObject(JSON.toJSONString(textItem)));
        }

        return textItemArray;
    }

    /**
     * 解析平台菜单对象
     *
     * @param m
     * @param menuitem
     * @return
     */
    public JSONObject jxMenuItem(MenuItem m, JSONObject menuitem) {
        try {
            Menu menu = m.getMenu();
            List<MenuItem> menuItems = menu.getItemList();
            JSONArray menus = new JSONArray();
            for (MenuItem m2 : menuItems) {
                JSONObject item = new JSONObject();
                item.put("name", m2.getTitle(0).getText());
                item.put("path", "");
                item.put("icon", "SettingOutlined");
                item.put("key", PingYinUtil.getFirstSpell(m2.getTitle(0).getText()));
                item.put("iframe", true);
                item.put("objectName", m2.getObject());
                if (m2.hasMenu()) {
                    item = jxMenuItem(m2, item);
                } else {
                    item.put("icon", "HomeOutlined");
                    String objectName = m2.getObject();
                    if (objectName.startsWith("VUE_")) {
                        item.put("path", m2.getUrl());
                        item.put("iframe", false);
                    } else {
                        if(!isLivebosNativeObject(m2.getUrl())) {
                            item.put("path", livebosPath + m2.getUrl() + "&hideTitlebar=true");
                        }else{
                            item.put("path", livebosPath + m2.getUrl());
                        }
                    }
                }
                menus.add(item);
            }
            menuitem.put("children", menus);
        } catch (Exception e) {

        } finally {
            return menuitem;
        }
    }


    /**
     * 查询Liveob功能权限树
     *
     * @return
     */
    public CommonResponse queryLiveBosAuthList(String uid) {
        CommonResponse commonResponse = null;
        try {
            commonResponse = new CommonResponse(JSONResponse.CODE_SUCCESS, "");
            commonResponse.setRecords(livebosGRPC.queryUserAuthList(Long.parseLong(uid),null));
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
        return commonResponse;
    }

    public CommonResponse execBizProcess(JSONObject p) {
        CommonResponse commonResponse = null;
        try {
            commonResponse = new CommonResponse(JSONResponse.CODE_SUCCESS, "");
            String bizProcessName = p.getString("bizProcessName");
            String objectId= p.getString("objectId");
            String params= p.getString("params");
            String variables= p.getString("variables");
            Map<String, String> paramM = new HashMap<>();
            Map<String, String> variabM = new HashMap<>();
            try{
                if(StringUtils.isNotEmpty(params)) {
                    JSONObject par = JSONObject.parseObject(params);
                    for (Map.Entry<String, Object> entry : par.entrySet()) {
                        Object v = (Object) entry.getValue();
                        String key = entry.getKey();
                        paramM.put(key,v.toString());
                    }
                }

            }catch (Exception e){
                commonResponse = new CommonResponse(JSONResponse.CODE_FAIL, "参数列表必须是JSON");
                return commonResponse;
            }
            try{
                if(StringUtils.isNotEmpty(variables)) {
                    JSONObject va = JSONObject.parseObject(variables);
                    for (Map.Entry<String, Object> entry : va.entrySet()) {
                        Object v = (Object) entry.getValue();
                        String key = entry.getKey();
                        variabM.put(key,v.toString());
                    }
                }
            }catch (Exception e){
                commonResponse = new CommonResponse(JSONResponse.CODE_FAIL, "变量列表必须是JSON");
                return commonResponse;
            }
            JSONObject result = livebosGRPC.execBizProcess(bizProcessName,objectId,paramM,variabM);
            if(result.getIntValue("code")<0){
                commonResponse = new CommonResponse(JSONResponse.CODE_FAIL, result.getString("note"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
        return commonResponse;
    }


    public CommonResponse userOrganization(long uid) {
        CommonResponse commonResponse = null;
        try {
            commonResponse = new CommonResponse(JSONResponse.CODE_SUCCESS, "");
            JSONObject result = JSON.parseObject(livebosGRPC.userOrganization(uid));
            if(result.getJSONObject("result").getIntValue("result")==1){
                commonResponse.setRecords(result.getJSONArray("orgs"));
            }else{
                commonResponse = new CommonResponse(JSONResponse.CODE_FAIL, "获取组织机构失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
        return commonResponse;
    }

    public boolean isLivebosNativeObject(String url){
        boolean _b = false;
        String m = "lbFunAudit;lbFunAuthorize;lbFunAuthScope;lbFunScopeAudit;lbFunAuthorizeTemp;lbAuthorizationState";
        String[] mA = m.split(";");
        for(String s:mA){
            if(url.indexOf(s)>=0){
                _b=true;
                break;
            }
        }
        return  _b;
    }

}
