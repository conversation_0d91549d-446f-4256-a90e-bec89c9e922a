package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.api.resp.query.KhsdxfbRes;
import com.apex.sdx.api.vo.khgl.KhxxtjVo;
import com.apex.sdx.api.vo.khgl.ZhsdxqkVo;
import com.apex.sdx.api.vo.query.SdxsjVo;
import com.apex.sdx.core.mybatis.entity.Tkhxx;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface TkhxxMapper extends BaseMapper<Tkhxx> {

    List<KhxxtjVo> khxxtj();

    /**
     * 基础统计查询
     */
    Map<String, Object> khxxtjBasic();

    /**
     * 风险等级统计查询
     */
    Map<String, Object> khxxtjRisk();

    /**
     * 日期相关统计查询
     */
    Map<String, Object> khxxtjDate();

    /**
     * 投资者统计查询
     */
    Map<String, Object> khxxtjInvestor();

    /**
     * 其他信息统计查询
     */
    Map<String, Object> khxxtjOther();

    List<KhsdxfbRes> queryTzzflxx(@Param("tzzfl") String type);

    List<KhsdxfbRes> queryFxcsnl(@Param("cpdj") String type);
    
    /**
     * 查询ABC类投资者数据
     * @return ABC类投资者数据列表
     */
    List<KhsdxfbRes> queryAbcTzzflxx();

    /**
     * 根据风险等级和投资者类型查询投资者数量统计
     * @return 投资者数量统计信息
     */
    List<Map<String, Object>> queryTzzcfbStats();

    /**
     * 查询风险等级字典
     * @return 风险等级字典列表
     */
    List<Map<String, Object>> queryFxcsnlDic();

    /**
     * 查询账户状态明细
     */
    Page<ZhsdxqkVo> queryZhztmx(Page<?> page, String khh, String zhqk, String zhzt);

    /**
     * 查询账户适当性情况-证件有效期明细
     */
    Page<ZhsdxqkVo> queryZjyxqmx(Page<SdxsjVo> page, String khh, String zhqk, String zjjzrq);

    /**
     * 查询账户适当性情况-风险承受能力等级明细
     */
    Page<ZhsdxqkVo> queryFxqfxdjmx(Page<SdxsjVo> page, String khh, String xqfxdj);

    /**
     * 查询账户适当性情况-风险产品有效期明细
     */
    Page<ZhsdxqkVo> queryFxcpyxmx(Page<SdxsjVo> page, String khh, String zhqk, String cpyxq);

    /**
     * 查询账户适当性情况-专业投资者测评明细
     */
    Page<ZhsdxqkVo> queryZytzzcpmx(Page<SdxsjVo> page, String khh, String tzzpdyxq);

    /**
     * 查询账户适当性情况-其他信息明细
     */
    Page<ZhsdxqkVo> queryQtxxmx(Page<SdxsjVo> page, String khh, String qtxxzt);

}
