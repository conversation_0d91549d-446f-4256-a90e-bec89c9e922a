package com.apex.sdx.api.req.khgl;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025-01-16
 * @Description:
 */
@Setter
@Getter
public class KhxxcxReq {

    @NotNull(message = "不能为空")
    @LiveProperty(note = "客户号",index = 1)
    private String khh;

    @LiveProperty(note = "证件类别",index = 2)
    private Integer zjlb;

    @LiveProperty(note = "证件编号",index = 3)
    private String zjbh;

}