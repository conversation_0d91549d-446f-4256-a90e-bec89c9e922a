package com.apex.sdx.api.vo.sdx;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description: 业务系统适当性统计VO
 */
@Setter
@Getter
public class YwxtsdxTjVo {

    @LiveProperty(note = "业务系统", index = 1)
    private Integer ywxt;

    @LiveProperty(note = "总数量", index = 2)
    private Integer total;

    @LiveProperty(note = "适当数量", index = 3)
    private Integer sdNum;

    @LiveProperty(note = "适当占比", index = 4)
    private Double sdRate;

    @LiveProperty(note = "不适当数量", index = 5)
    private Integer bsdNum;

    @LiveProperty(note = "不适当占比", index = 6)
    private Double bsdRate;

    @LiveProperty(note = "不通过数量", index = 7)
    private Integer btgNum;

    @LiveProperty(note = "不通过占比", index = 8)
    private Double btgRate;
} 