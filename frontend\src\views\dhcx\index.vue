<template>
  <div class="khlogin-container">
    <div class="khlogin_search_con">
      <h3>客户适当性全景视图</h3>
      <div class="khlogin_input" ref="resultModuleRef">
        <a-input
          :bordered="false"
          style="width: 480px; padding-left: 20px"
          v-model:value="queryValue"
          placeholder="请输入客户号"
        />
<!--        <span class="khlogin_sb_gn">
          <a class="dk_sb"></a>
          <a class="ocr_sb"></a>
        </span>-->
        <input type="button" value="查询" @click="onSearch" style="background-color: #B48A3B"/>
        <div  v-if="isSearch">
          <div v-if="isLoad">
            <div class="khlogin_show_detail" style="text-align: center">
              <a-spin tip="Loading..."/>
            </div>
          </div>
          <div v-else-if="!isLoad">
            <div class="khlogin_show_detail" v-if="khxxList.length > 0 && khxxList[0] != null">
              <div v-for="(item, index) in khxxList" :key="index">
                <div class="user_khlx" :class="getkhlbCss(item?.khlb)">
                  {{ getDcNote("SDX_KHLB", item?.khlb, dictArray) }}
                </div>
                <div class="user_info" @click="handleSelectKhh(item.khh)">
                  <p>{{ item?.khmc }}<span>/ {{ item?.cid }} / {{ getDcNote("YYB", item?.yyb, dictArray) }}</span>
                    <br>{{ item?.khh }}
                  </p>
                </div>
              </div>
            </div>
            <div class="khlogin_show_detail" v-else>
              <div class="nodata">未查询到匹配的数据</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dhcxApi from "@/api/dhcx/dhcx";
import {defineComponent} from "vue";
import {commonApi} from "@/api/common";
import {message} from "ant-design-vue";
import {encrypt, getDcNote} from "../../utils/bussinessUtils";
import {dictApi} from "@/api/xtgl/dict";

export default defineComponent({
  name: "dhcx_index",
  data() {
    return {
      khh: "",
      spinning: false,
      showKhjcxx: false,
      khjbxxList: [],
      queryValue: "002200602408",
      isSearch: false,
      isLoad: false,
      khxxList: [],
      dictArray: [],
    };
  },
  mounted() {
    this.getSjzd();
  },
  methods: {
    getDcNote,
    getSjzd() {
      dictApi.cxsjzd({ fldm: "SDX_KHLB;YYB" }).then((res) => {
        if (res.code > 0) {
          this.dictArray = res.sjzd;
          this.getYyList();
        }
      });
    },
    getYyList() {
      commonApi.executeAMS(
        "sdx.query.IYybcxService",
        "yybcx",
        { orgcode: null },
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.dictArray.YYB = res.records.map(item => ({
          fldm: 'YYB',
          flmc: '营业部',
          cbm: item.orgcode,
          id: item.id + '',
          fid: item.fid,
          ibm: item.id + '',
          note: item.name
        }))
      })
    },
    onSearch() {
      this.isSearch = true;
      this.isLoad = true;
      commonApi.executeAMS(
        "sdx.khgl.IKhxxcxService",
        "khjbxxcx",
        { khh: this.queryValue },
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.khxxList = res.records || [];
      }).finally(() => {
        this.isLoad = false;
      });
    },
    getkhlbCss(khlb) {
      if (khlb === 0) {
        return "gr";
      } else if (khlb === 1) {
        return "jg";
      } else if (khlb === 2) {
        return "cp";
      }
    },
    handleSelectKhh(val) {
      this.isSearch = false;
      if (val.length > 0) {
        let khh = encrypt(val);
        if (process.env.NODE_ENV === "development") {
          window.open(`/app/#/khqj?khh=` + khh);
        } else {
          window.open(`/app/#/khqj?khh=` + khh);
        }
      }
    },
  },
})
</script>

<style scoped>
.khlogin-container {
  height: 100%;
  width: 100%;
  background-color: #fff;
  border-radius: 4px !important;
  border: unset;
  background-image: url('../../assets/images/khsdxgl_bg_pic.png');
  background-size: cover;
  background-position: left bottom;
  background-repeat: no-repeat;
}

.page_header {
  position: fixed;
  padding: 20px;
  font-size: 16px;
}

.first_title {
  cursor: pointer;
}

.second_title {
  color: #888888;
  cursor: default;
}

.login-context {
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.login-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer
}

.login-type-title {
  font-size: 18px;
  padding-top: 10px;
  cursor: pointer;
}

.khqj_search {
  height: calc(100%);
  width: calc(100%);
  box-shadow: 0 0 6px rgba(0, 0, 0, .1);
  border-radius: 8px;
  position: relative;
  background-color: #fafcfe;
  background-image: linear-gradient(145deg, #f8f8f8 10%, #f2f7ff 60%);
}

.khlogin_search_con {
  position: relative;
  top: calc(26%);
}

.khlogin_search_con h3 {
  color: #454967;
  font-size: 32px;
  font-weight: normal;
  text-align: center;
  line-height: 1;
  margin-bottom: 2.5%;
}

.khlogin_input {
  width: 700px;
  height: 54px;
  position: relative;
  margin: 0 auto;
  background-color: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, .1);
  border-radius: 40px;
  border: 1px solid #eee;
}

.khlogin_input .khqj_khfl {
  display: inline-block;
  vertical-align: top;
  width: 130px;
  position: relative;
  color: #454967;
  font-size: 14px;
  cursor: pointer;
  padding-left: 22px;
  line-height: 54px;
}

.khlogin_input .khqj_khfl:before {
  content: "\e609";
  font-family: "iconfont";
  color: #666;
  position: absolute;
  right: 10px;
  top: 0px;
}

.khlogin_input .khqj_khfl:after {
  content: "";
  height: 36px;
  width: 1px;
  display: inline-block;
  background-color: #eee;
  position: absolute;
  right: 0;
  top: 9px;
}

.khlogin_input input[type=text] {
  width: calc(100% - 215px);
  border: none;
  background-color: transparent;
  outline: none;
  padding-left: 10px;
  padding-right: 120px;
  font-size: 14px;
  display: inline-block;
  height: 52px;
  line-height: 52px;
  color: #454967;
}

.khlogin_input .khlogin_sb_gn {
  text-align: right;
  line-height: 52px;
  position: absolute;
  right: 100px;
}

.khlogin_input .khlogin_sb_gn a {
  cursor: pointer;
  text-align: center;
  position: relative;
  display: inline-block;
  width: 36px;
  margin: 0px 10px;
}

.khlogin_input .khlogin_sb_gn a:nth-child(2):before {
  content: "";
  height: 32px;
  width: 1px;
  display: inline-block;
  background-color: #eee;
  position: absolute;
  left: -10px;
  top: 10px;
}

.khlogin_input .khlogin_sb_gn a:after {
  font-family: "iconfont";
  color: #4658FF;
  font-size: 32px;
  line-height: 52px;
  display: inline-block;
  width: 36px;
}

.khlogin_input .khlogin_sb_gn a.dk_sb:after {
  content: "\f4ab";
}

.khlogin_input .khlogin_sb_gn a.ocr_sb:after {
  content: "\e61a";
}

.khlogin_input input[type=button] {
  border: none;
  background-color: #4658FF;
  color: #fff;
  width: 100px;
  font-size: 16px;
  height: 54px;
  border-radius: 0 27px 27px 0;
  position: absolute;
  right: 0px;
  top: 0;
  line-height: 54px;
  text-align: center;
  cursor: pointer;
}

.khlogin_input .khqj_khfl .khfl_item {
  position: absolute;
  z-index: 99;
  top: 50px;
  padding: 14px 0 5px 5px;
  width: 100px;
  line-height: 40px;
  font-size: 14px;
  color: #454967;
  text-align: left;
}

.khlogin_input .khqj_khfl .khfl_item li.cur {
  color: #4658FF
}

.khlogin_input .khqj_khfl .khfl_item li.cur:hover {
  color: #3fa6f5;
}

.khlogin_input .khqj_khfl .khfl_item:before {
  display: inline-block;
  content: "";
  width: 110px;
  height: calc(100%);
  box-shadow: 0 0 6px rgba(0, 0, 0, .1);
  background-color: #fff;
  position: absolute;
  top: 4px;
  left: -10px;
  border-radius: 4px;
}

.khlogin_input .khqj_khfl .khfl_item li {
  position: relative;
  z-index: 1;
  cursor: pointer;
}

.khlogin_input .khqj_khfl .khfl_item li:hover {
  color: #3fa6f5;
}

.khlogin_input .khlogin_show_detail {
  position: absolute;
  top: 74px;
  width: 700px;
  box-shadow: 0 0 6px rgba(0, 0, 0, .1);
  background-color: #fff;
  border-radius: 4px;
  z-index: 99;
  height: 80px;
  font-size: 16px;
  padding: 10px;
}

.khlogin_input .khlogin_show_detail:before {
  position: absolute;
  content: "";
  top: -5px;
  height: 14px;
  width: 14px;
  background-color: #fff;
  left: 130px;
  transform: rotate(45deg);
  display: inline-block;
  box-shadow: -1px -1px 2px rgba(0, 0, 0, .05);
}

.khlogin_input .khlogin_show_detail .user_khlx {
  width: 44px;
  font-size: 14px;
  height: 44px;
  border-radius: 22px;
  line-height: 44px;
  margin: 8px 15px 10px 15px;
  text-align: center;
  color: #fff;
  display: inline-block;
  vertical-align: top;
}

.khlogin_input .khlogin_show_detail .user_khlx.gr {
  background-color: #4658FF;
}

.khlogin_input .khlogin_show_detail .user_khlx.jg {
  background-color: rgb(225, 152, 42);
}

.khlogin_input .khlogin_show_detail .user_khlx.cp {
  background-color: #7a87f6;
}

.khlogin_input .khlogin_show_detail .user_info {
  padding-top: 5px;
  display: inline-block;
  width: calc(100% - 88px);
  font-size: 14px;
  text-align: left;
  color: #454967;
  line-height: 26px;
  cursor: pointer;
}

.khlogin_input .khlogin_show_detail .user_info p span {
  color: #888;
  display: inline-block;
  padding-left: 10px;
}

.nodata {
  margin: 20px;
  font-size: 16px;
}
</style>