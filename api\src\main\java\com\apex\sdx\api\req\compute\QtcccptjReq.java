package com.apex.sdx.api.req.compute;

import com.apex.sdx.api.req.common.PageRequest;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> wu<PERSON><PERSON>in
 * @create 2025/5/8 14:14
 */
@Getter
@Setter
public class QtcccptjReq extends PageRequest {

    @LiveProperty(note = "业务系统", index = 1)
    private Integer ywxt;

    @LiveProperty(note = "客户号", index = 2)
    private String khh;

    @LiveProperty(note = "日期", index = 3)
    private String rq;

}
