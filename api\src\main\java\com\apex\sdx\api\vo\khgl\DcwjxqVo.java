package com.apex.sdx.api.vo.khgl;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-02-08
 * @Description:
 */
@Getter
@Setter
public class DcwjxqVo {
    @LiveProperty(note = "id", index = 1)
    private Integer id;

    @LiveProperty(note = "问卷参数id", index = 2)
    private Integer tpjWjcsId;

    @LiveProperty(note = "编号", index = 3)
    private Integer bh;

    @LiveProperty(note = "问题类型", index = 4)
    private Integer qtype;

    @LiveProperty(note = "问题描述", index = 5)
    private String qdescribe;

    @LiveProperty(note = "问题选项", index = 6)
    private String sanswer;

    @LiveProperty(note = "sjsyff", index = 7)
    private Integer sjsyff;

    @LiveProperty(note = "glkhsxyz", index = 8)
    private String glkhsxyz;

    @LiveProperty(note = "gxgz", index = 9)
    private String gxgz;

    @LiveProperty(note = "dajx", index = 10)
    private String dajx;

    @LiveProperty(note = "答案", index = 11)
    private String da;

    @LiveProperty(note = "问卷名称", index = 12)
    private String name;
}
