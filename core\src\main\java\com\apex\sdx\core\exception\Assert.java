package com.apex.sdx.core.exception;

import com.alibaba.fastjson.JSONObject;
import com.apex.sdx.api.req.common.Request;
import com.apex.sdx.api.resp.common.Response;
import com.apex.sdx.core.utils.ParamUtil;
import com.baomidou.mybatisplus.core.toolkit.LambdaUtils;
import com.baomidou.mybatisplus.core.toolkit.support.LambdaMeta;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.reflection.property.PropertyNamer;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 断言
 */
public abstract class Assert {


    /**
     * 断言SQL注入字符串验证
     *
     * @param req
     * @return
     */
    public static void sqlRegularParam(Request req) {
        Response response = ParamUtil.SqlRegularParam(req);
        if (response.getCode() < 0) {
            throw new BusinessException(response.getNote());
        }
    }

    /**
     * SQL注入字符串验证
     *
     * @param key
     * @param value
     * @return
     * @throws Exception
     */
    public static void sqlRegular(Object key, Object value) throws Exception {
        Response response = ParamUtil.SqlRegular(key, value);
        if (response.getCode() < 0) {
            throw new BusinessException(response.getNote());
        }
    }
    /**
     * 校验是否为空
     */
    public static void notNull(Serializable value, String errMsg) {
        if(value == null) {
            throw new BusinessException(-599, errMsg);
        }
        if(value instanceof String) {
            String str = (String) value;
            if (StringUtils.isEmpty(str)) {
                throw new BusinessException(-599, errMsg);
            }
        }
    }
    /**
     * 校验是否为空
     * @param req
     * @param columns
     */
    public static void notNull(JSONObject req, String... columns) {
        for (String column : columns) {
            Object value = req.get(column);
            ParamUtil.check1Param(value, column);
        }
    }

    /**
     * 校验是否为空
     * @param req
     * @param column
     * @param errMsg
     * @param <T>
     */
    public static <T> void notNull(T req, SFunction<T, ?> column, String errMsg) {
        Object value = column.apply(req);
        boolean isNull = false;
        //参数值非空检查
        if (value instanceof String) {
            String str = (String) value;
            if (StringUtils.isBlank(str)) {
                isNull = true;
            }
        } else if (value == null){
            isNull = true;
        }
        if (isNull) {
            throw new BusinessException(-599, errMsg);
        }
    }

    /**
     * 校验是否为空
     * @param req
     * @param columns
     * @param <T>
     */
    @SafeVarargs
    public static <T> void notNull(T req, SFunction<T, ?>... columns) {
        List<String> errorList = new ArrayList<>();
        for (SFunction<T, ?> column : columns) {
            Object value = column.apply(req);
            boolean isNull = false;
            //参数值非空检查
            if (value instanceof String) {
                String str = (String) value;
                if (StringUtils.isEmpty(str)) {
                    isNull = true;
                }
            } else if (value == null){
                isNull = true;
            }
            if (isNull) {
                String name = getColumnName(column);
                errorList.add(name);
            }
        }
        if(errorList.size() > 0){
            String error = StringUtils.join(errorList.toArray(new String[0]), ",");
            throw new BusinessException(-599, "参数[" + error + "]不能为空");
        }
    }

    private static <T> String getColumnName(SFunction<T, ?> column) {
        LambdaMeta meta = LambdaUtils.extract(column);
        return PropertyNamer.methodToProperty(meta.getImplMethodName());
    }

}
