package com.apex.sdx.api.vo.query;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/6/6 16:50
 * @Description: TODO
 */
@Setter
@Getter
public class QxbsdxyqslscxVo implements Serializable {

    @LiveProperty(note = "客户号", index = 1)
    private String khh;

    @LiveProperty(note = "创新业务类别", index = 2)
    private String cxywlb;

    @LiveProperty(note = "风险承受能力", index = 3)
    private String fxcsnl;

    @LiveProperty(note = "客户投资期限", index = 4)
    private String khtzqx;

    @LiveProperty(note = "客户投资品种", index = 5)
    private String khtzpz;

    @LiveProperty(note = "客户预期收益", index = 6)
    private String khyqsy;

    @LiveProperty(note = "业务风险等级", index = 7)
    private String ywfxdj;

    @LiveProperty(note = "业务投资期限", index = 8)
    private String ywtzqx;

    @LiveProperty(note = "业务投资品种", index = 9)
    private String ywtzpz;

    @LiveProperty(note = "业务预期收益", index = 10)
    private String ywyqsy;

    @LiveProperty(note = "签署日期", index = 11)
    private String qsrq;

    @LiveProperty(note = "不适当类型", index = 12)
    private String bsdlx;
}
