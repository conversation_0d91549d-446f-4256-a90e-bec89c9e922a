package com.apex.sdx.api.req.compute;

import com.apex.sdx.api.req.common.PageRequest;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> wu<PERSON><PERSON>in
 * @create 2025/5/12 20:19
 */
@Getter
@Setter
public class QtyktywtjReq extends PageRequest {

    @LiveProperty(note = "客户号", index = 1)
    private String khh;

    @LiveProperty(note = "创新业务类别", index = 2)
    private String cxywlb;

    @LiveProperty(note = "签署状态", index = 3)
    private String qsbsdxy;

    @LiveProperty(note = "日期", index = 4)
    private String rq;

}
