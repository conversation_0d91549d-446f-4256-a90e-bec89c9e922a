package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.SjzdReq;
import com.apex.sdx.api.resp.query.SjzdRes;
import com.apex.sdx.api.vo.xtcs.XtdmVo;
import com.apex.sdx.convert.XtdmVoMapping;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.entity.Vxtdm;
import com.apex.sdx.core.mybatis.service.VxtdmService;
import com.apexsoft.LiveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import sdx.query.ISjzdService;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @Date 2025-01-16
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "数据字典查询")
public class SjzdService implements ISjzdService {

    @Autowired
    private VxtdmService vsjzdService;

    @Autowired
    private XtdmVoMapping sjzdDtoMapping;

    @Override
    public SjzdRes cxsjzd(SjzdReq req) {
        SjzdRes response = new SjzdRes(1, "查询成功");
        try {
            HashMap<String, List<XtdmVo>> map = new HashMap<String, List<XtdmVo>>();

            String fldm = req.getFldm();

            if (req.getYyb() == null) {
                String[] fldms = fldm.split(";");
                for (int i = 0; i < fldms.length; i++) {
                    if (!fldms[i].isEmpty()) {
                        List<Vxtdm> list = vsjzdService.querySjzd(fldms[i]);
                        if (!CollectionUtils.isEmpty(list)) {
                            map.put(fldms[i], sjzdDtoMapping.listConver(list));
                        }
                    }
                }
            } else {
                // 营业部数据字典
            }
            response.setSjzd(map);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setCode(-99);
            response.setNote("查询数据字典失败");
        }
        return response;
    }
}