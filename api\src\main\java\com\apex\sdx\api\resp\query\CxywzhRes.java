package com.apex.sdx.api.resp.query;

import com.apex.sdx.api.resp.common.R;
import com.apex.sdx.api.vo.khgl.YwzhVo;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @Date 2025/1/20 10:51
 * @Description: TODO
 */
@Setter
@Getter
@SuperBuilder
public class CxywzhRes extends R {
    @LiveProperty(note = "集中交易", index = 1)
    private YwzhVo jzjy;

    @LiveProperty(note = "融资融券", index = 2)
    private YwzhVo rzrq;

    @LiveProperty(note = "股票期权", index = 3)
    private YwzhVo gpqq;

    @LiveProperty(note = "场外业务", index = 4)
    private YwzhVo cwyw;
}
