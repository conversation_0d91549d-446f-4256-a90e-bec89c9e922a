package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 营运指标
 * @TableName tic_yyzb
 */
@TableName(value ="tic_yyzb")
public class TicYyzb implements Serializable {
    /**
     * 日期
     */
    private Integer rq;

    /**
     * 指标ID;TIC_ZBCS.ID
     */
    @TableId
    private Long idxId;

    /**
     * 指标代码
     */
    private String idxCode;

    /**
     * 结果
     */
    private BigDecimal result;

    /**
     * 周期
     */
    private Integer cycle;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 日期
     */
    public Integer getRq() {
        return rq;
    }

    /**
     * 日期
     */
    public void setRq(Integer rq) {
        this.rq = rq;
    }

    /**
     * 指标ID;TIC_ZBCS.ID
     */
    public Long getIdxId() {
        return idxId;
    }

    /**
     * 指标ID;TIC_ZBCS.ID
     */
    public void setIdxId(Long idxId) {
        this.idxId = idxId;
    }

    /**
     * 指标代码
     */
    public String getIdxCode() {
        return idxCode;
    }

    /**
     * 指标代码
     */
    public void setIdxCode(String idxCode) {
        this.idxCode = idxCode;
    }

    /**
     * 结果
     */
    public BigDecimal getResult() {
        return result;
    }

    /**
     * 结果
     */
    public void setResult(BigDecimal result) {
        this.result = result;
    }

    /**
     * 周期
     */
    public Integer getCycle() {
        return cycle;
    }

    /**
     * 周期
     */
    public void setCycle(Integer cycle) {
        this.cycle = cycle;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TicYyzb other = (TicYyzb) that;
        return (this.getRq() == null ? other.getRq() == null : this.getRq().equals(other.getRq()))
            && (this.getIdxId() == null ? other.getIdxId() == null : this.getIdxId().equals(other.getIdxId()))
            && (this.getIdxCode() == null ? other.getIdxCode() == null : this.getIdxCode().equals(other.getIdxCode()))
            && (this.getResult() == null ? other.getResult() == null : this.getResult().equals(other.getResult()))
            && (this.getCycle() == null ? other.getCycle() == null : this.getCycle().equals(other.getCycle()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getRq() == null) ? 0 : getRq().hashCode());
        result = prime * result + ((getIdxId() == null) ? 0 : getIdxId().hashCode());
        result = prime * result + ((getIdxCode() == null) ? 0 : getIdxCode().hashCode());
        result = prime * result + ((getResult() == null) ? 0 : getResult().hashCode());
        result = prime * result + ((getCycle() == null) ? 0 : getCycle().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", rq=").append(rq);
        sb.append(", idxId=").append(idxId);
        sb.append(", idxCode=").append(idxCode);
        sb.append(", result=").append(result);
        sb.append(", cycle=").append(cycle);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}