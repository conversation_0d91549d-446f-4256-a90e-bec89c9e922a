<template>
  <modal-component
    :title="title"
    :open="open"
    :height="600"
    width="1050px"
    :on-cancel="handleOk"
  >
    <template #content>
      <Sjxq :sjid="sjid"></Sjxq>
    </template>
  </modal-component>
</template>
<script setup>
import {inject} from "vue";
import {useVModel} from "@vueuse/core";
import ModalComponent from "@components/ModalComponent.vue";
import Sjxq from "./sjxq.vue";

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    required: true,
  }
})
const emits = defineEmits(['update:open']);
const open = useVModel(props, 'open', emits)
const sjid = inject('sjid')
const handleOk = () => {
  open.value = false;
}
</script>
<style>
.tips-important {
  color: #B48A3B;
  font-size: 20px;
  padding: 0 5px;
}
</style>
<style scoped>
.modal-container {
  padding: 10px 10px 10px 20px;
  background-color: #FFFFFF;
  height: 100%;
  border-radius: 4px;
  line-height: 30px;
  color: #888888;
  overflow-y: auto;
}
.title{
  padding-top: 10px;
}
.tips-content {
  height: 80px;
  background: rgb(245, 249, 252);
  border-radius: 4px;
  display: flex;
  align-items: center;
}
</style>