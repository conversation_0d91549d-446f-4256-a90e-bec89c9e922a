@charset "utf-8";
/* CSS Document */
*{ margin:0; padding:0; box-sizing:border-box;font-family:"Microsoft Yahei",arial,"Hiragino Sans GB",sans-serif; }
html,body{height:100%; width:100%;}
li{ list-style:none;}
img ,a{ border:0; text-decoration:none;}
.clear:after { content:''; display:table; clear:both; height:0;}
.tc{ text-align:center;}
.tl{ text-align:left;}
.tr{ text-align:right;}
.l{ float:left;}
.r{ float:right;}
.hide{ display:none;}
/*滚动条1*/
.innerbox::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}
.innerbox::-webkit-scrollbar-thumb {
    border-radius: 5px;
    /* -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2); */
    background: rgba(20,116,222,1);
}
.innerbox::-webkit-scrollbar-track {
    /* -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2); */
    border-radius: 0;
    /* background: rgba(0,0,0,0.1); */
}
/*滚动条2*/
.innerbox2::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}
.innerbox2::-webkit-scrollbar-thumb {
    border-radius: 5px;
    /* -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2); */
    background: rgba(102,102,102,.6);
}
.innerbox2::-webkit-scrollbar-track {
    /* -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2); */
    border-radius: 0;
    /* background: rgba(0,0,0,0.1); */
}
input[type=text],select,input[type=password]{ font-family: "Microsoft Yahei", arial, "Hiragino Sans GB", sans-serif;}
::-ms-reveal{display:none;}
input[type=text]::-webkit-input-placeholder{color: #ccc;}
input[type=text]::-moz-input-placeholder{ color: #ccc;}
input[type=text]:-ms-input-placeholder {color: #ccc !important;}
input[type=password]::-webkit-input-placeholder{color: #ccc;}
input[type=password]::-moz-input-placeholder{ color: #ccc;}
input[type=password]:-ms-input-placeholder {color: #ccc !important;}
textarea::-webkit-input-placeholder{color: #ccc;}
textarea::-moz-input-placeholder{ color: #ccc;}
textarea:-ms-input-placeholder {color: #ccc !important;}
select {
    appearance: none;  -moz-appearance: none;  -webkit-appearance: none;
    background-repeat:no-repeat;  background-position: right 5px center;  cursor: pointer;}
select::-ms-expand { display: none; }
/**/
.label_checkbox{line-height: 22px;}
.label_checkbox label.advice{margin-right: 6px;}
.advice{
    display: inline-block;
    background-repeat: no-repeat;
    background-position: center;
    vertical-align: middle;
    margin-top:0px;
}
.label_checkbox .advice{display:inline-block;vertical-align: middle;height: 18px;width: 18px !important;text-align: center;line-height: 18px;}
.label_radio .advice{display:inline-block;vertical-align: middle;height: 19px;width: 19px !important;line-height: 20px;text-align: center;}
.label_radio .radio-name,.label_checkbox .checkbox-name{text-align: left;font-size:12px;color:#666;line-height: 1.5;vertical-align: middle;}
input[type="radio"] + .advice{border:1px solid #aaa;border-radius: 10px;background-color:#fff;}
input[type="radio"]:checked + .advice{color:#fff;border:1px solid #458ae5; border-radius: 10px;background-color:#458ae5;}
input[type="radio"] + .advice:after{ content:" ";}
input[type="radio"]:checked + .advice:after{font-family: "iconfont" !important;content:"\e620";position: relative;font-size: 14px;top: -1px;font-weight: lighter;}

input[type="checkbox"] + .advice{border: 1px solid #aaa;border-radius: 4px;background-color:#fff;}
input[type="checkbox"]:checked + .advice{color:#fff;border:1px solid #458ae5;border-radius: 4px;background-color:#458ae5;}
input[type="checkbox"] + .advice:after{ content:" ";}
input[type="checkbox"]:checked + .advice:after{font-family: "iconfont" !important;content:"\e620";position: relative;left: 0;top: -1px;font-size: 14px;font-weight: lighter;line-height: 18px;height: 18px;text-align: center;width: 18px;}


input[type="radio"][disabled] + .advice{border:1px solid #458ae5;border-radius: 10px;background-color:#fff;opacity:.7;}
input[type="radio"][disabled]:checked + .advice{ color:#fff; border:1px solid #458ae5;border-radius:10px; background-color:#458ae5; opacity:.7;}
input[type="radio"][disabled] + .advice:after{ content:" ";}
input[type="radio"][disabled]:checked + .advice:after{ font-family: "iconfont" !important;content:"\e620";position: relative;font-size: 16px;top:  -1px; font-weight:lighter;}


input[type="checkbox"][disabled] + .advice{border:1px solid #d9d9d9;border-radius:2px; background-color:#fff; opacity:.7;}
input[type="checkbox"][disabled]:checked + .advice{line-height: 18px; color:#fff; border:1px solid #458ae5; border-radius:4px; background-color:#458ae5; opacity:.7;}
input[type="checkbox"][disabled] + .advice:after{ content:" "; }
input[type="checkbox"][disabled]:checked + .advice:after{ font-family: "iconfont" !important;content:"\e620";position: relative;left: 0px;top:  -1px;font-size: 14px; font-weight:lighter;}


input[type="radio"][disabled] + .advice + .radio-name{  color: #b8b8b8;}
input[type="radio"][disabled]:checked + .advice + .radio-name{  color: #b8b8b8;}

input[type="checkbox"][disabled] + .advice + .checkbox-name{ color: #b8b8b8;}
input[type="checkbox"][disabled]:checked + .advice + .checkbox-name { color: #b8b8b8;}

input[type="radio"]:checked.error + .advice{  color:#fff;border:1px solid #458ae5; border-radius:10px; background-color:#458ae5;}
input[type="checkbox"]:checked.error + .advice{ line-height: 20px; color:#fff; border:1px solid #458ae5;border-radius:4px; background-color:#458ae5;}
input[type="radio"]:checked.error + .advice:after{font-family: "iconfont" !important; content:"\e620";position: relative;font-size: 16px;top: -1px; font-weight:lighter;}
input[type="checkbox"]:checked.error + .advice:after{font-family: "iconfont" !important; content:"\e620";position: relative;left: 0px;top: -1px;font-size: 14px; font-weight:lighter;}


input[type="radio"].error + .advice{  color:#fff; border:1px solid #ff1919;border-radius:10px; background-color:#fff;}
input[type="checkbox"].error + .advice{ color:#fff; border:1px solid #ff1919;border-radius:4px; background-color:#fff;}
input[type="radio"].error + .advice:after{ content:" ";}
input[type="checkbox"].error + .advice:after{ content:"";}


input[type="radio"]:checked.error + .advice + .radio-name{  color: #ff1919;}
input[type="checkbox"]:checked.error + .advice + .checkbox-name{  color: #ff1919;}
input[type="radio"].error + .advice + .radio-name{  color: #ff1919;}
input[type="checkbox"].error + .advice + .checkbox-name{  color: #ff1919;}
/**/
.tyyhzx_body_color{background-color: white; height: 100%;}
.tyyhzx_body_color.tyyhxt_dl{background-image: url(../images/dxsdxglpt_login_bg.png); background-repeat: no-repeat;background-position: left bottom; background-size:cover;}
.dl_top{height: 70px; box-shadow: 0 0 6px rgba(0,0,0,.1); background-color: #fff; padding:0 20px;}
.dl_top span{width:calc(50%); display: inline-block;vertical-align: middle; line-height: 70px;}
.dl_top span:nth-child(1){text-align: left; color: #454a55; font-size: 22px;}
.dl_top span:nth-child(2){text-align: right;}
.dl_top span:nth-child(1) i.tyyhzx_logo:before{content: "\e761"; font-size: 46px; font-style: normal;color: transparent; -webkit-background-clip: text;  font-family: "iconfont" !important; display: inline-block; vertical-align: top; margin-right: 12px;  background-image: linear-gradient(to right bottom,#1cc6fd,#0243d9);}
.tyyhzx_login_bottom{ width: calc(100%); text-align: center; line-height: 40px; font-size: 14px; color: rgba(255,255,255,.5); position: fixed; bottom:0;}
.tyyhzx_dl_nav{ width: 420px;  margin: 0 auto; position: relative; top: 15%}
.tyyhzx_dl_nav .tyzhzx_dl_box{ width:100%; height:100%; display: inline-block; vertical-align: top; text-align: left; background-color: #fff; border-radius: 8px;}
.dl_title{ line-height: 40px; padding:34px 40px; font-size: 26px; color: #454a55;position: relative;}
.dl_title:before{content: ""; display: inline-block;width: 54px; left:45px; bottom:20px; border-radius: 3px; height: 5px; background-color: #B48A3B; position: absolute;}
.xtyy_more{color: #666; cursor: pointer;}
.xtyy_more:Hover{color: #3686fd;}
.xtyy_more:before{content: "\e60e"; font-size: 20px; font-style: normal; font-family: "iconfont" !important;}
.tyzhzx_dl_box .yhm,.tyzhzx_dl_box .mm{ margin: 15px 40px 30px 40px; font-size: 14px; color: #333; text-align: left; padding: 0 5px;}
.tyzhzx_dl_box .yhm p,.tyzhzx_dl_box .mm p{line-height: 28px; padding-left: 5px;}
.tyzhzx_dl_box .yhm input[type=text],.tyzhzx_dl_box .mm input[type=password]{outline: none; border-left:none; border-right:none; border-top:none; padding: 0 5px; height: 40px; line-height: 40px; width: 100%;}
.tyzhzx_dl_box .btn{margin: 0px 40px 25px 40px;}
.tyzhzx_dl_box .btn button{display: inline-block; cursor: pointer; width:calc(100%); height: 46px; border-radius: 4px; background-color: #3686fd;text-align: center; color: #fff; font-size: 14px;}
.tyzhzx_dl_box .btn button:hover{ background-color: #4f96ff;}
.tyzhzx_dl_box .qywxsm{ text-align: center; margin-top: 35px; line-height: 30px;}
.tyzhzx_dl_box .qywxsm a{display: inline-block; cursor: pointer; font-size: 14px; color: #454a55;}
.tyzhzx_dl_box .qywxsm a:before{content: "\e606"; font-size: 16px;font-family: "iconfont" !important; display: inline-block; margin-right: 8px; vertical-align: middle; margin-top: -2px;}
.tyzhzx_dl_box .qywxsm a:hover{color: #4f96ff;}
.tyyhzx_top{height: 58px; box-shadow: 0 0 6px rgba(0,0,0,.1); background-color: #fff; padding:0 15px;}
.tyyhzx_top .tyyhzx_user,.tyyhzx_top .tyyhzx_title{width:calc(50%); display: inline-block;vertical-align: middle; line-height: 58px;}
.tyyhzx_top .tyyhzx_title{text-align: left; color: #454a55; font-size: 18px;}
.tyyhzx_top .tyyhzx_user{text-align: right;}
.tyyhzx_top .tyyhzx_title i.tyyhzx_logo:before{content: "\e761"; font-size: 46px; font-style: normal;color: transparent; -webkit-background-clip: text;  font-family: "iconfont" !important; display: inline-block; vertical-align: top; margin-right: 12px;  background-image: linear-gradient(to right bottom,#1cc6fd,#0243d9);}
.tyyhzx_top span input[type=text]{ height: 30px;width: 300px;border:1px solid #d6d6d6; background-color: #fff;border-radius: 4px; padding: 0px 24px 0px 6px; line-height: 30px;font-size: 14px; display: inline-block; vertical-align: middle; margin-top: -2px;}
.tyyhzx_top span .search_tb{ position: relative; z-index: 99; left: -24px; font-style: normal; font-size: 18px; color: #bbb; line-height: 32px;}
.tyyhzx_top span .search_tb:before{content: "\e622"; font-family: "iconfont" !important;}
.tyyhzx_top span input[type=text]:focus{outline:1px solid #4f96ff;}
.tyyhzx_top span a.user_tx{ background-color: #e7f0ff; cursor: pointer; width: 40px; text-align: center; height: 40px; border-radius: 20px;display: inline-block; vertical-align: middle; margin-left: 10px; overflow: hidden;}
.tyyhzx_top span a.user_tx img{max-width:40px; max-height: 40px; }
.tyyhzx_top span p{display: inline-block; vertical-align: middle; font-size: 14px; color: #454a55;margin: 0 20px;cursor: pointer;}
.tyyhzx_top span a.user_tx svg.iconfont{width: 2em; height: 2em;fill: currentColor;overflow: hidden; margin-top: 4px;}
.tyyhzx_left_menu{ width: 60px;background-color: #fcfdfe; box-shadow: 0 0 6px rgba(0,0,0,.1); height: calc(100% - 59px);margin-top: 1px; float: left;}
.tyyhzx_left_menu ul li{ cursor: pointer; text-align: center; width: 60px; line-height: 16px; font-size: 14px; padding-top: 5px;}
.tyyhzx_left_menu ul li i:before{font-family: "iconfont" !important; content: "";  font-style: normal; display: inline-block; font-size: 22px; color: #454a55; line-height: 32px; width: 32px; height: 32px; border-radius: 4px; text-align: center; margin: 10px auto 5px auto;}
.tyyhzx_left_menu ul li.sy i:before{ content: "\e626";}
.tyyhzx_left_menu ul li.yh i:before{ content: "\e6e6";}
.tyyhzx_left_menu ul li.qx i:before{ content: "\e6b3";}
.tyyhzx_left_menu ul li.bb i:before{ content: "\e64b";}
.tyyhzx_left_menu ul li.jcsj i:before{ content: "\e6a6";}
.tyyhzx_left_menu ul li.dddl i:before{ content: "\e8b7";}
.tyyhzx_left_menu ul li.cur{color: #3686fd;}
.tyyhzx_left_menu ul li.cur i:before,.tyyhzx_left_menu ul li.cur:hover i:before{background-color: #3686fd; color: #fff;}
.tyyhzx_left_menu ul span{display: inline-block; width: calc(100%); height: 1px;background-color:  #eeeeee; margin: 15px 0 0 0;}
.tyyhzx_left_menu ul li:hover{ color: #3686fd;}
.tyyhzx_left_menu ul li:hover i:before{ color: #3686fd;}
.tyyhzx_dddl_main{width: calc(100%);display: inline-block; height: calc(100% - 47px) ; margin-top: 1px; vertical-align: top;}
.tyyhzx_top_dkyq{display: inline-block; vertical-align: top; height: 42px; width: calc(100%); background-color: #fff;box-shadow: 0 0 6px rgba(0,0,0,.1); border-left:1px solid #dee8f2;}
.tyyhzx_top_dkyq ul{ width: calc(100% - 50px); display: inline-block; vertical-align: top;}
.tyyhzx_top_dkyq a.dkyq_more{width:40px; display: inline-block; vertical-align: top; line-height: 46px;text-align: center; font-size: 20px;cursor: pointer; color: #454a55;}
.tyyhzx_top_dkyq a.dkyq_more:before{font-family: "iconfont" !important; content: "\e621"; }
.tyyhzx_top_dkyq a.dkyq_more:hover{color: #4f96ff;}
.tyyhzx_top_dkyq ul li{padding:0 8px;min-width:26px; cursor: pointer; text-align:center;display: inline-block; font-size: 14px; vertical-align: top; margin: 5px 5px; height: 36px; line-height: 36px; border-radius: 4px; color: #888;}
.tyyhzx_top_dkyq ul li:before{display: inline-block; content: ""; width: 1px; height: 24px; background-color: #eee;vertical-align: middle; margin-top: -2px;margin-right: 5px;position: relative;left:-10px;}
.tyyhzx_top_dkyq ul li:nth-child(1):before{display: none;}
.tyyhzx_top_dkyq ul li:nth-child(1){ margin-right: 2px;}
.tyyhzx_top_dkyq ul li i{font-style: normal;}
.uuc-tabs{
    display: flex;
    flex-direction: column;
    height: 100%;
}
.uuc-tabs i.home:before{
    content: "\e626";
    font-family: "iconfont" !important;
    font-size: 22px;
    font-style: normal;
}
.tyyhzx_top_dkyq ul li i.close:before{content: "\e640";font-family: "iconfont" !important; font-size: 14px; display: inline-block; vertical-align: middle; margin-left: 4px; margin-top: -2px;}
.tyyhzx_top_dkyq ul li.cur,.tyyhzx_top_dkyq ul li.cur:hover{background-color: #3686fd; color: #fff;}
.tyyhzx_top_dkyq ul li:hover{color: #4f96ff;}
.tyyhzx_dddl_main .dmk_title{ font-size: 28px;  color: #333; padding: 10px 25px; line-height: 70px;}
.tyyhzx_dddl_main .dddl_nav{ width: calc(100% - 25px);padding-left: 25px; }
.dddl_item{ display: inline-block; width: calc(20% - 17px);margin-right: 13px; vertical-align: top; background-color: #fff;border-radius: 8px; height: 280px; box-shadow: 0 0 6px rgba(0,0,0,.1);}
.dddl_item .img{height: 206px;border-radius: 8px 8px 0 0; overflow: hidden;}
.dddl_item .img img{height: 206px; cursor: pointer;}
.dddl_item .xt_name{float: left; padding-left: 15px; font-size: 14px; color: #454a55; line-height: 74px;}
.dddl_item .kdl_user{float: right; padding-right: 15px;}
.dddl_item .kdl_user ul{ padding-top: 12px;}
.dddl_item .kdl_user ul li{ width: 46px; height: 46px; cursor: pointer; border-radius: 23px; margin-left: -20px; box-shadow: 0 0 6px rgba(0,0,0,.1); background-color: #fff; text-align: center; display: inline-block; vertical-align: middle;}
.dddl_item .kdl_user ul li svg.iconfont{width: 2em; height: 2em;fill: currentColor;overflow: hidden; margin-top: 4px;}
.dddl_item .kdl_user ul:hover li{ margin-left: 5px; transition-duration:1s;}
.tyyhzx_right_main{width: calc(100% - 60px);display: inline-block; height: calc(100% - 59px) ; margin-top: 1px; vertical-align: top;}
.tyyhzx_right_main.second_menu .tyyhzx_left_second_menu{ width: 290px; height: calc(100%); box-shadow: 0 0 6px rgba(0,0,0,.1); background-color: #fcfdfe; float: left;}
.tyyhzx_right_main.second_menu .tyyhzx_top_dkyq{ width:100%;}
.tyyhzx_right_main.second_menu .tyyhzx_func_main{ overflow: auto; width: 100%;border-left: 1px solid #dee8f2; height: 100%; margin-top: 1px; background-color: #fff; display: inline-block;}
.tyyhzx_left_second_menu{ padding-top: 10px; padding-left: 5px; padding-right: 5px; overflow: auto;}
.tyyhzx_left_second_menu ul li{ margin-bottom: 2px; margin-top: 2px; line-height: 44px; font-size: 14px; color: #454a55; }
.tyyhzx_left_second_menu ul li p{ padding: 0 5px 0 35px; cursor: pointer; border-radius: 4px;}
.tyyhzx_left_second_menu ul li p.yhqjst:before{content: "\e602";}
.tyyhzx_left_second_menu ul li p.yhgl:before{content: "\e6e5";}
.tyyhzx_left_second_menu ul li p:before{font-family: "iconfont" !important;margin-top: -1px; font-size: 20px; position: relative; left: -24px; margin-right: -16px; display: inline-block; vertical-align: middle;}
.tyyhzx_left_second_menu ul li p.ywqx:before{content: "\e6a7";}
.tyyhzx_left_second_menu ul li p.glqx:before{content: "\e637";}
.tyyhzx_left_second_menu ul li p.bb:before{content: "\e624";}
.tyyhzx_left_second_menu ul li p.xtcd:before{content: "\ea98";}
.tyyhzx_left_second_menu ul li p.gnzj:before{content: "\e619";}
.tyyhzx_left_second_menu ul li p.apiyh:before{content: "\ea0c";}
.tyyhzx_left_second_menu ul li p.xtyy:before{content: "\e62a";}

.tyyhzx_left_second_menu ul li p:hover{ background-color: #f5f5f5;color: #3686fd;}
.tyyhzx_left_second_menu ul li.cur p{ background-color: #e8f1fe; color: #3686fd;}






















































