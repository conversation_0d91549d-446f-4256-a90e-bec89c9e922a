<template>
  <div>
    <div :id="chartsId" style=" width:90%; height:220px;margin: 0 25px auto">
    </div>
  </div>

</template>
<script>
import {defineComponent} from 'vue';

let barCharts = [];
export default defineComponent({
  props: ["dataset", "title"],
  data() {
    return {
      data: [],//[{ value: 735, name: '不适当' }, { value: 310, name: '适当' }, { value: 135, name: '积极'}],
      max: 0,//总数
      colorList: ['#36A6FC','#FFC730'],
    }
  },
  methods: {
    initBarChart() {
      let el = document.getElementById(this.chartsId)
      barCharts[this.chartsId] = this.$echarts.init(el);
      this.setOption();
    },
    setOption() {
      let _this = this;
      _this.data = _this.$props.dataset;
      _this.data.forEach(item => {
        _this.max += item.value;
      });
      let option = _this.getOption();
      barCharts[_this.chartsId].setOption(option)
      setTimeout(() => {
        _this.handleMouseOut();
      }, 1000);

      // 使用ECharts自带的事件系统
      _this.removeListener(); // 移除之前的监听事件，防止重复添加
      barCharts[_this.chartsId].on('mouseover', _this.handleMouseOver);
      barCharts[_this.chartsId].on('mouseout', _this.handleMouseOut);
    },
    removeListener() {
      // 移除图表
      barCharts[this.chartsId].off('mouseover'); // 先移除旧监听
      barCharts[this.chartsId].off('mouseout'); // 先移除旧监听
    },
    cencel() {
      // 取消所有高亮
      barCharts[this.chartsId].dispatchAction({
        type: 'downplay',
        seriesIndex: 0
      });
    },
    handleMouseOver(params) {
      this.cencel();
      // 高亮当前悬停的项
      if (params.dataIndex !== undefined) {
        barCharts[this.chartsId].dispatchAction({
          type: 'highlight',
          seriesIndex: params.seriesIndex,
          dataIndex: params.dataIndex
        });
      }
    },
    handleMouseOut() {
      this.cencel();
      // 鼠标移出时恢复默认高亮状态（如需要）
      // 这里可以根据需求调整，例如重新高亮第一项
      barCharts[this.chartsId].dispatchAction({
        type: 'highlight',
        seriesIndex: 0,//要操作的系列（series）的索引，默认单系列图表是 0，多系列时按顺序递增（0, 1, 2...）
        dataIndex: 1  //表示要操作的数据的索引
      });
    },
    getOption() {
      let _this = this;
      let option = {
        tooltip: {
          show: false,
          trigger: 'item',
          formatter: function (params) {
            return params.name + ': ' + params.value;
          }
        },
        legend: {
          bottom: '0%',
          left: 'center',
          icon: 'circle', // 设置图例为小圆点
          itemWidth: 8,  // 控制圆点宽度
          itemHeight: 8, // 控制圆点高度
          itemGap: 15,   // 图例项之间的间隔
        },
        title: {
          text: _this.$props.title + ' 总数量：' + _this.max,
          left: 'center',
          top: 0,
          textStyle: {
            color: '#333333',
            fontSize: 13,
          }
        },
        series: this.getSeriesData(),
      };
      return option;
    },
    getSeriesData() {
      let data = [];
      let _this = this;
      data.push({
        //关闭图例悬浮行为
        legendHoverLink:false,
        name: _this.$props.title,
        type: 'pie',
        //minAngle: 10,//最小的扇区角度（0 ~ 360），用于防止某个值过小导致扇区太小影响交互。
        itemStyle: {
          color: function (params) {
            return {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              /*colorStops: [{
                offset: 0, color: _this.colorList[params.dataIndex][0] // 起始颜色
              }, {
                offset: 1, color: _this.colorList[params.dataIndex][1] // 结束颜色
              }]*/
              colorStops: [{
                offset: 0, color: _this.colorList[params.dataIndex] // 起始颜色
              }]
            };
          }
        },
        radius: ['45%', '60%'],
        avoidLabelOverlap: false,
        clockwise: false,//是否顺时针排列，默认为 true，可以设为 false。设置为 true 时，数据顺序与饼图扇区（数据项）是相反的。
        label: {
          show: false,
          position: 'center',
          formatter: function (params) {
            let max = _this.max;
            if(max === 0){
              max = 1;//防止除数为0
            }
            const percent = (params.value / max * 100).toFixed(0);
            return '{c|' + params.name + ' }' + '\n{a|' + params.value + ' }' + '{b|/' + percent + '%}';
          },
          rich: {
            a: {
              fontSize: '18px',
              color: '#333333',
            },
            b: {
              fontSize: '14px',
              color: '#999999',
            },
            c: {
              fontSize: '14px',
              color: '#333333',
            },
          },
        },
        emphasis: {
          label: {
            show: true,
            formatter: function (params) {
              let max = _this.max;
              if(max === 0){
                max = 1;//防止除数为0
              }
              const percent = (params.value / max * 100).toFixed(0);
              return '{c|' + params.name + ' }' + '\n{a|' + params.value + ' }' + '{b|/' + percent + '%}';
            },
            rich: {
              a: {
                fontSize: '18px',
                color: '#333333',
              },
              b: {
                fontSize: '14px',
                color: '#999999',
              },
              c: {
                fontSize: '14px',
                color: '#333333',
              },
            },
          }
        },
        data: this.data,
      });
      return data;
    },

  },
  mounted() {
    if (this.$props.dataset && this.$props.dataset.length > 0) {
      this.initBarChart()
    }
  },
  computed: {
    chartsId() {
      return "id_" + this.title
    },
  },
  watch: {
    dataset(newValue, oldValue) {
      if (newValue && newValue.length > 0 && JSON.stringify(newValue) != JSON.stringify(oldValue)) {
        if (barCharts[this.chartsId]) {
          this.setOption();
        } else {
          this.initBarChart();
        }
      }
    }
  },
  unmounted() {
    this.removeListener();
    if (barCharts[this.chartsId]) {
      barCharts[this.chartsId].dispose();
      delete barCharts[this.chartsId];
    }
  },
});
</script>

<style scoped>
</style>