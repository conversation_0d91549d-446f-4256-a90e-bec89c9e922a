package com.apex.sdx.core.utils;

import cn.hutool.core.convert.Convert;

import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Title:       字符处理单元
 * Description:
 * Copyright:    Copyright (c) 2005
 * Company:     socity.cm
 *
 * <AUTHOR>
 * @sysVersion 1.0
 */


public class StringUtils {

	private static final char[] LT_ENCODE = "&lt;".toCharArray();
	private static final char[] GT_ENCODE = "&gt;".toCharArray();
	private static final char[] BR_TAG = "<BR>".toCharArray();

	public static final String STRING_A_Z_a_z_0_9 = "A,B,C,D,E,F,G,H,I,J,K,L,firstName,N,O,P,Q,R,S,T,U,V,W,lastName,Y,Z,a,b,c,d,e,f,g,h,i,j,k,l,firstName,n,o,p,q,r,s,t,u,v,w,lastName,y,z,0,1,2,3,4,5,6,7,8,9";

	/**
	 * 类似javascript  的join
	 *
	 * @param array
	 * @param separator
	 * @return
	 */
	public static String join(String[] array, String separator) {
		if (array == null || array.length == 0) {
			return null;
		}
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < array.length; i++) {
			String s = array[i];
			if (i > 0) {
				sb.append(separator);
			}
			sb.append(s);
		}
		return sb.toString();
	}

	/**
	 * 类似javascript  的push
	 *
	 * @param array
	 * @param newVal
	 * @return
	 */
	public static String[] push(String[] array, String newVal) {
		String[] target = new String[array.length + 1];
		System.arraycopy(array, 0, target, 0, array.length);
		target[target.length - 1] = newVal;
		return target;

	}


	/**
	 * 类似javascript  的push
	 *
	 * @param array
	 * @param newVal
	 * @return
	 */
	public static String[] pop(String[] array, String newVal) {
		String[] target = new String[array.length - 1];
		int pos = indexOf(array, newVal);
		if (pos == -1) {
			return array;
		}
		if (pos == 0) {
			System.arraycopy(array, 1, target, 0, array.length - 1);
		}else {
			System.arraycopy(array, 0, target, 0, pos);
		}
		if (pos < array.length) {
			System.arraycopy(array, pos + 1, target, pos, array.length - pos - 1);
		}
		return target;
	}

	/**
	 * 类似javascript  的index
	 *
	 * @param a arrays
	 * @param o object
	 * @return int
	 */
	public static int indexOf(Object[] a, Object o) {
		if (a == null || a.length == 0){
			return -1;
		}
		if (o == null) {
			for (int i = 0; i < a.length; i++) {
				if (a[i] == null) {
					return i;
				}
			}
		} else {
			for (int i = 0; i < a.length; i++) {
				if (o.equals(a[i])) {
					return i;
				}
			}
		}
		return -1;
	}



	/**
	 * This method takes a string which may contain HTML tags (ie, &lt;b&gt;,
	 * &lt;table&gt;, etc) and converts the '&lt'' and '&gt;' characters to
	 * their HTML escape sequences.
	 *
	 * @param in the text to be converted.
	 * @return the input string with the characters '&lt;' and '&gt;' replaced
	 *         with their HTML escape sequences.
	 */
	public static String escapeHTMLTags(String in) {
		if (in == null) {
			return null;
		}
		char ch;
		int i = 0;
		int last = 0;
		char[] input = in.toCharArray();
		int len = input.length;
		StringBuffer out = new StringBuffer((int) (len * 1.3));
		for (; i < len; i++) {
			ch = input[i];
			if (ch > '>') {
			} else if (ch == '<') {
				if (i > last) {
					out.append(input, last, i - last);
				}
				last = i + 1;
				out.append(LT_ENCODE);
			} else if (ch == '>') {
				if (i > last) {
					out.append(input, last, i - last);
				}
				last = i + 1;
				out.append(GT_ENCODE);
			}
		}
		if (last == 0) {
			return in;
		}
		if (i > last) {
			out.append(input, last, i - last);
		}
		return out.toString();
	}

	public static String convertNewlines(String input) {
		if (input == null) {
			return "";
		}
		char[] chars = input.toCharArray();
		int cur = 0;
		int len = chars.length;
		StringBuffer buf = new StringBuffer(len);
		// Loop through each character lookin for newlines.
		for (int i = 0; i < len; i++) {
			// If we've found a Unix newline, add BR tag.
			if (chars[i] == '\n') {
				buf.append(chars, cur, i - cur).append(BR_TAG);
				cur = i + 1;
			}
			// If we've found a Windows newline, add BR tag.
			else if (chars[i] == '\r' && i < len - 1 && chars[i + 1] == '\n') {
				buf.append(chars, cur, i - cur).append(BR_TAG);
				i++;
				cur = i + 1;
			}
		}
		// Add whatever chars are left to buffer.
		buf.append(chars, cur, len - cur);
		return buf.toString();
	}

	public static String getTranslateStr(String sourceStr, String fieldStr) {
		//处理逻辑表达式的转化问题
		String[] sourceList;
		String resultStr = "";
		//dim i,j
		if (sourceStr.indexOf(" ") > 0) {
			boolean isOperator = true;
			sourceList = sourceStr.split(" ");
			//'--------------------------------------------------------
			//rem Response.Write "num:" & cstr(ubound(sourceList)) & "<br>"
			for (int i = 0; i < sourceList.length; i++) {
				String aSourceList = sourceList[i];
				if (aSourceList.equals("AND") || aSourceList.equals("&") || aSourceList.equals("和") || aSourceList.equals("与")) {
					resultStr = resultStr + " and ";
					isOperator = true;
				} else if (aSourceList.equals("OR") || aSourceList.equals("|") || aSourceList.equals("或")) {
					resultStr = resultStr + " or ";
					isOperator = true;
				} else if (aSourceList.equals("NOT") || aSourceList.equals("!") || aSourceList.equals("！") || aSourceList.equals("非")) {
					resultStr = resultStr + " not ";
					isOperator = true;
				} else if (aSourceList.equals("(") || aSourceList.equals("（") || aSourceList.equals("（")) {
					resultStr = resultStr + " ( ";
					isOperator = true;
				} else if (aSourceList.equals(")") || aSourceList.equals("）") || aSourceList.equals("）")) {
					resultStr = resultStr + " ) ";
					isOperator = true;
				} else {
					if (!"".equals(aSourceList)) {
						if (!isOperator) {
							resultStr = resultStr + " and ";
						}
						if (aSourceList.indexOf("%") > 0) {
							resultStr = resultStr + " " + fieldStr + " like '" + aSourceList.replaceAll("'", "''") + "' ";
						} else {
							resultStr = resultStr + " " + fieldStr + " like '%" + aSourceList.replaceAll("'", "''") + "%' ";
						}
						isOperator = false;
					}
				}
			}
			return resultStr;
		} else {
			if (sourceStr.indexOf("%") > 0) {
				resultStr = resultStr + " " + fieldStr + " like '" + sourceStr.replaceAll("'", "''") + "' ";

			} else {
				resultStr = resultStr + " " + fieldStr + " like '%" + sourceStr.replaceAll("'", "''") + "%' ";
			}
			return resultStr;
		}


	}

	/**
	 * 转换乱码字符
	 *
	 * @param str String
	 * @return String
	 */
	public static String unicode2gb(String str) {
		try {
			if (str != null){
				str = new String(str.getBytes(StandardCharsets.ISO_8859_1), "GB2312");}
			return str;
		}
		catch (Exception ex) {
			return "";
		}
	}


	/**
	 * public static String ReplaceIgnoreCase(String source, String oldString, String newString) {
	 * }
	 */


	/**
	 * <p>验证是否为合法的URL字符串</p>
	 * <p>合法的应该是：a-z、A-Z、-、_、0-9 </p>
	 *
	 * @param str String
	 * @return boolean
	 */

	public static boolean isUrlAllowString(String str) {
		boolean re = true;
		StringBuffer allstr = new StringBuffer();
		allstr.append("abcdefghijklmnopqrstuvwxyz");
		allstr.append("0123456789");
		allstr.append("-_");
		//System.out.println("StringUtils:allstr:" + allstr.toString());
		//System.out.println("StringUtils:str=" + str);

		if (str == null || str.equals("") || str.length() < 1){
			return false;
		}

		for (int i = 0; i < str.length(); i++) {
			//System.out.println("StringUtils:");
			if (allstr.indexOf(String.valueOf(str.charAt(i))) < 0) {
				re = false;
				break;
			}
		}
		return re;
	}

	/**
	 * <p>把对sql有做用的'号去了</p>
	 *
	 * @param url String
	 * @return String
	 */
	public static String moveSQLQuot(String url) {
		return url.replaceAll("'", "");
	}

	/**
	 * <p>取一个字符串中指定数量的子串，汉字为2个字符</p>
	 * pinke:2005
	 *
	 * @param s String
	 * @param i int
	 * @return String
	 */
	public static String cut(String s, int i) {
		if (s == null) {
			return s;
		}
		if (s.getBytes().length < 4 || s.getBytes().length < i - 3) {
			return s;
		}
		else {
			char[] cs = s.toCharArray();
			StringBuffer restr = new StringBuffer();
			int j = 0;
			int k = 0;
			while (j < i && k < cs.length) {
				j += (String.valueOf(cs[k])).getBytes().length;
				restr.append(cs[k++]);

			}
			return restr.toString().equals(s) ? restr.toString() : restr.append("...").toString();
			// return s.substring(0, i - 3) + "...";
		}
	}

	/**
	 * <p>取一个字符串中指定数量的子串，汉字为2个字符</p>
	 * pinke:2005
	 *
	 * @param s String
	 * @param i int
	 * @return String
	 */
	public static String cutSimple(String s, int i) {
		if (s == null || s.length() < 1) return s;
		else {
			char[] cs = s.toCharArray();
			StringBuffer restr = new StringBuffer();
			int j = 0;
			int k = 0;
			while (j < i && k < cs.length) {
				j += (String.valueOf(cs[k])).getBytes().length;
				restr.append(cs[k++]);

			}
			return restr.toString();
			// return s.substring(0, i - 3) + "...";
		}
	}

	/**
	 * <p>将文字根据给定每行显示字数（中文代表两字)进行加" "(空格)换行</p>
	 * <p>这个为了解决长英文不换行的问题，但会把一个英文单词分行两行不推荐使用</p>
	 * <p>可以用style解决
	 * <PRE>
	 * <style>
	 * table {
	 * table-layout: fixed;
	 * word-wrap:break-word;
	 * }
	 * div {
	 * word-wrap:break-word;
	 * }
	 * </style>
	 * </PRE>
	 *
	 * @param s   String
	 * @param num int
	 * @return String
	 */
	public static String rowBr(String s, int num) {
		if (s == null) {
			return s;
		}
		if (s.getBytes().length < num) {
			return s;
		}
		else {
			char[] cs = s.toCharArray();
			StringBuffer restr = new StringBuffer();
			StringBuffer tmp = new StringBuffer();
			for (int i = 0; i < cs.length; i++) {
				char c = cs[i];
				restr.append(c);
				//System.out.println("StringUtils:"+restr.toString().getBytes().length+"  --  "+num+"---"+(restr.toString().getBytes().length%num));

				tmp.append(c);
				if (tmp.toString().getBytes().length % num == 0) {
					restr.append(" ");
				}

			}
			return restr.toString();
			// return s.substring(0, i - 3) + "...";
		}
	}

	/**
	 * 判断字符串是否为NULL,则返回给定字符串
	 *
	 * @param strObj Sting
	 * @param want   String
	 * @return return
	 */
	public static Object ConvertNull(Object strObj, Object want) {
		return strObj == null || strObj.toString().equals("null") ? want : strObj;
	}

	/**
	 * 这是一个得到随机给定长度的字符串
	 *
	 * @param inStr String
	 * @param num   in
	 * @return String
	 */
	public static String getRandomString(String inStr, int num) {

		Random random;
		random = new Random(System.currentTimeMillis());

		String sRand = "";
		/*给定字符串中*/
		String sReturnString = "1,2,3,4,5,6,7,8,9,0,a,b,c,d,e,f,g,h,i,j,k,l,firstName,n,o,p,q,r,s,t,u,v,w,lastName,y,z,@,#,$,?,A,B,C,D,E,F,G,H,I,J,K,L,firstName,N,O,P,Q,R,S,T,U,V,W,lastName,Y,Z,";

		if (inStr != null && inStr.split(",").length > 1) {
			sReturnString = inStr;
		}

		for (int i = 0; i < num; i++) {

			int k = random.nextInt(sReturnString.length() / 2);
			//System.out.print("StringUtils:Random :" +k);
			sRand += sReturnString.split(",")[k];
		}

		return sRand;
	}


	/**
	 * 这是一个得到随机给定长度的字符串
	 *
	 * @param ext String
	 * @return String
	 */
	public static String getRandomFileNameString(String ext) {
		Calendar cal = Calendar.getInstance();
		StringBuffer tmpFileName = new StringBuffer();
		tmpFileName.append(cal.get(Calendar.YEAR));
		tmpFileName.append(cal.get(Calendar.MONTH + 1));
		tmpFileName.append(cal.get(Calendar.DATE));
		tmpFileName.append(cal.get(Calendar.HOUR));
		tmpFileName.append(cal.get(Calendar.MINUTE));
		tmpFileName.append(cal.get(Calendar.SECOND));
		tmpFileName.append(cal.get(Calendar.DECEMBER));
		tmpFileName.append(getRandomString(STRING_A_Z_a_z_0_9, 5));
		if (ext != null && !ext.equals("")) {
			tmpFileName.append(".");
			tmpFileName.append(ext);
		}
		return tmpFileName.toString();
	}

	/**
	 * 转换为int
	 *
	 * @param obj          Object
	 * @param defaultValue int
	 * @return int
	 */
	public static int getInt(Object obj, int defaultValue) {
        if (obj == null) {
        	return defaultValue;
		}
        try {
            if (obj instanceof Integer) {
            	return ((Integer) obj).intValue();
			}
            if (obj instanceof String){
            	return Integer.parseInt((String) obj);
			}
            return Integer.parseInt(obj.toString());
        } catch (Throwable e) {
            return defaultValue;
        }
	}

	/**
	 * 转换为long
	 *
	 * @param obj          Ojbect
	 * @param defaultValue long
	 * @return long
	 */
	public static long getLong(Object obj, long defaultValue) {
		   if (obj == null) {
		   	return defaultValue;
		   }
        try {
            if (obj instanceof Long) {
            	return ((Long) obj).longValue();
			}
            if (obj instanceof String){
            	return Long.parseLong((String) obj);
			}
            return Long.parseLong(obj.toString());
        } catch (Throwable e) {
            return defaultValue;
        }
	}


	public static String getSafeJS(String str) {
		if (str == null) {
			return str;
		}
		str = StringUtils.escapeHTMLTags(str);
		StringBuffer re = new StringBuffer(512);
		for (int i = 0; i < str.length(); i++) {
			char c = str.charAt(i);
			if (c == '\"') {
				c = '“';
			} else if (c == '\n') {
				c = '\u0000';
			} else if (c == '\r') {
				c = '\u0000';
			}
			re.append(c);
		}
		return re.toString().replaceAll("\n", "");
	}

	public static boolean isEmpty(String str) {
		return str == null || str.trim().length() == 0;
	}

	public static String toHTMLStr(String src) {
		return src == null ? "" : escapeHTMLTags(src);
	}

	public static String getStringConvert(String src, String fromEncode, String toEncode) {
		try {
			return new String(fromEncode == null ? src.getBytes() : src.getBytes(fromEncode), toEncode);
		} catch (Exception e) {
			return src;
		}
	}


	/**
	 * Produce a string in double quotes with backslash sequences in all the
	 * right places.
	 *
	 * @param string A String
	 * @return A String correctly formatted for insertion in a JSON message.
	 */
	public static String toJSONQuoteString(String string) {
		if (string == null || string.length() == 0) {
			return "\"\"";
		}
		if (string.equalsIgnoreCase("true")) {
			return "true";
		}
		if (string.equalsIgnoreCase("false")) {
			return "false";
		}

		char c;
		int i;
		int len = string.length();
		StringBuffer sb = new StringBuffer(len + 4);
		String t;

		sb.append('"');
		for (i = 0; i < len; i += 1) {
			c = string.charAt(i);
			switch (c) {
				case '\\':
				case '"':
				case '/':
					sb.append('\\');
					sb.append(c);
					break;
				case '\b':
					sb.append("\\b");
					break;
				case '\t':
					sb.append("\\t");
					break;
				case '\n':
					sb.append("\\n");
					break;
				case '\f':
					sb.append("\\f");
					break;
				case '\r':
					sb.append("\\r");
					break;
				default:
					if (c < ' ' || c >= 128) {
						t = "000" + Integer.toHexString(c);
						sb.append("\\u");
						sb.append(t.substring(t.length() - 4));
					} else {
						sb.append(c);
					}
			}
		}
		sb.append('"');
		return sb.toString();
	}

	public static boolean isNumber(String v) {
		try {
			return v.replaceAll("[0-9]", "").equals("");
		} catch (Exception e) {
			return false;
		}
	}

	public static String getDateStringByFormat(String format) {
		return getDateStringByFormat(format, new Date());
	}

	public static String getDateStringByFormat(String format, Date data) {
		return new SimpleDateFormat(format).format(data);
	}

	public static String getDateStringByFormat(String dateSrc, String srcFormat, String toFormat) {
		try {
			return new SimpleDateFormat(toFormat).format(new SimpleDateFormat(srcFormat).parse(dateSrc));
		} catch (Exception e) {
			return dateSrc;
		}
	}

	public static String arrayToString(String[] vs, String split) {
		if (vs == null){
			return null;
		}
		StringBuffer sb = new StringBuffer();
		if (split == null){
			split = ",";
		}

		for (int i = 0; i < vs.length; i++) {
			String v = vs[i];
			if (sb.length() > 0) {
				sb.append(split);
			}
			sb.append(v);
		}
		return sb.toString();

	}

	public static String toJSON(Object obj) {
		if (obj == null){
			return "null";
		}
		StringBuffer sb = new StringBuffer();
		if (obj instanceof String) {
			return StringUtils.toJSONQuoteString((String) obj);
//        }else if (obj instanceof Array) {
		} else if (obj instanceof List) {
			List collection = (List) obj;
			sb.append("[");
			for (Iterator iterator = collection.iterator(); iterator.hasNext();) {
				Object o = iterator.next();
				if (sb.length() > 1){
					sb.append(",");
				}
				sb.append(toJSON(o));
			}
			return sb.append("]").toString();
		} else if (obj instanceof Map) {
			Map firstName = (Map) obj;
			sb.append("{");
			Set keys = firstName.keySet();
			for (Iterator iterator = keys.iterator(); iterator.hasNext();) {
				String key = (String) iterator.next();
				if (sb.length() > 1){
					sb.append(",");
				}
				sb.append(StringUtils.toJSONQuoteString(key));
				sb.append(":");
				sb.append(toJSON(firstName.get(key)));
			}
			return sb.append("}").toString();
		} else if (obj instanceof Number) {
			return "" + obj;
		} else if (obj instanceof Timestamp) {
			return toJSON(StringUtils.getDateStringByFormat("yyyy-MM-dd HH:mm:ss", (Date) obj));
		} else if (obj instanceof Date) {
			return toJSON(StringUtils.getDateStringByFormat("yyyy-MM-dd HH:mm:ss", (Date) obj));
		} else if (obj instanceof Object[]) {
			Object[] collection = (Object[]) obj;
			sb.append("[");
			for (int i = 0; i < collection.length; i++) {
				Object o = collection[i];
				if (sb.length() > 1){
					sb.append(",");
				}
				sb.append(toJSON(o));
			}
			return sb.append("]").toString();
		} else { //default string
			return StringUtils.toJSONQuoteString("" + obj);
		}
	}

	/**
	 * Map to javascript json
	 *
	 * @param map map
	 * @return json String
	 */
	public String toJSONStr(Map map) {
		StringBuffer sb = new StringBuffer();
		int idx = 0;

		if (map == null){
			return "null";
		}
		if (map.size() == 0) {
			return "{}";
		}
		sb.append("{");
		Set keys = map.keySet();
		String val;
		for (Iterator iterator = keys.iterator(); iterator.hasNext();) {
			String key = (String) iterator.next();
			if (idx++ > 0) {
				sb.append(",");
			}
			sb.append(StringUtils.toJSONQuoteString(key));
			val = map.get(key) == null ? null : ("" + map.get(key)).trim();
			sb.append(StringUtils.toJSONQuoteString(val));
		}
		sb.append("}");
		return sb.toString();

	}

	/**
	 * List to javascript json
	 *
	 * @param list List
	 * @return json string
	 */
	public String toJSONString(List list) {
		if (list == null) {
			return "null";
		}
		if (list.size() == 0){
			return "[]";
		}
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < list.size(); i++) {
			Object o = list.get(i);
			if (i++ > 0) {
				sb.append(",");
			}
			if (o == null) {
				sb.append("null");
			} else if (o instanceof String) {
				sb.append(toJSONQuoteString((String) o));
			} else if (o instanceof Integer) {
				sb.append(o);
			} else if (o instanceof Map) {
				sb.append(toJSONStr((Map) o));
			} else if (o instanceof Boolean) {
				sb.append(o);
			} else if (o instanceof Date) {
				sb.append(toJSONQuoteString(getDateStringByFormat("yyyy-MM-dd HH:mm:ss", (Date) o)));
			} else {
				sb.append(toJSONQuoteString("" + o));
			}
		}
		return sb.toString();
	}

    public static String html2text(String str) {

        try {

//        str = str.replaceAll("<\\!\\-\\-.*?\\-\\->", "");
            String htmlStr = str;
            // 定义script的正则表达式{或<script[^>]*?>[\\s\\S]*?<\\/script>
            // 定义style的正则表达式{或<style[^>]*?>[\\s\\S]*?<\\/style>
            String regEx_style = "<[\\s]*?style[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?style[\\s]*?>";
            String regEx_html = "<[^>]+>"; // 定义HTML标签的正则表达式
            String regEx_html1 = "<[^>]+";

            String regEx_script = "<[\\s]*?script[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?script[\\s]*?>";
            Pattern p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
            Matcher m_script = p_script.matcher(htmlStr);
            htmlStr = m_script.replaceAll(""); // 过滤script标签


            Pattern p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
            Matcher m_style = p_style.matcher(htmlStr);
            htmlStr = m_style.replaceAll(""); // 过滤style标签

            Pattern p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
            Matcher m_html = p_html.matcher(htmlStr);
            htmlStr = m_html.replaceAll(""); // 过滤html标签

            Pattern p_html1 = Pattern.compile(regEx_html1, Pattern.CASE_INSENSITIVE);
            Matcher m_html1 = p_html1.matcher(htmlStr);
            htmlStr = m_html1.replaceAll(""); // 过滤html标签

            htmlStr = htmlStr.replaceAll("&nbsp;|&#160;|\r|\t|\n", "");
            //合并空白为一个
            StringTokenizer st = new StringTokenizer(htmlStr);
            StringBuffer sb = new StringBuffer();
            while (st.hasMoreTokens()) {
				String n = st.nextToken();
				if (!StringUtils.isEmpty(n)) {
					sb.append(n).append(" ");
				}
			}
            return sb.toString();
//            return htmlStr;

        } catch (Exception e) {
            System.err.println("Html2Text: " + e.getMessage());
        }
        return str;
    }

    public static String substrb(String src, int start, int end) {
		if(StringUtils.isEmpty(src)) {
			return null;
		}
		int max = Math.min(src.length(), end);
		int min = Math.min(src.length(), start);
		return src.substring(min, max);
	}
	/**
	 * 字符串替换，忽略大小写
	 * @param text
	 * @param repl
	 * @param with
	 * @return
	 */
	public static String replaceIgnoreCaseByRegex(String text, String repl, String with) {
		if(StringUtils.isEmpty(text) || StringUtils.isEmpty(repl) || StringUtils.isEmpty(with)) {
			return text;
		}
		return text.replaceAll("(?i)"+repl, with);
	}

	public static String rtrim(String a, String b) {
		if (a.length() == 0) {
			return a;
		}
		char[] chars = a.toCharArray();
		Integer index = 0;
		for (Integer i = chars.length - 1; i > 0; i--) {
			if (b.indexOf(chars[i]) < 0) {
				index = i;
				break;
			}
		}
		return a.substring(0, index + 1);
	}

	public static String ltrim(String a, String b) {
		char[] chars = a.toCharArray();
		int index = 0;
		for (int i = 0; i < chars.length; i++) {
			if (b.indexOf(chars[i]) < 0) {
				index = i;
				break;
			}
		}
		return a.substring(index);
	}

	/**
	 * 判断是否包含
	 * a: 1;2;3 b: 2
	 * @param a
	 * @param b
	 * @return
	 */
	public static boolean contains(String a, Object b) {
		return contains(a, b, ";");
	}

	/**
	 * 判断是否包含
	 * a: 1;2;3 b: 2 seperate: ";"
	 * @param a
	 * @param b
	 * @param seperate
	 * @return
	 */
	public static boolean contains(String a, Object b, String seperate) {
		return (seperate + a + seperate).contains(seperate + (b == null? "": b) + seperate);
	}

	public static String convertString(Object value, String defaultValue) {
		return Convert.convert(String.class, value, defaultValue);
	}

	public static String convertString(Object value) {
		return convertString(value, "");
	}
}