package com.apex.sdx.convert;

import com.apex.sdx.api.vo.khgl.JgkhxxhcsqVo;
import com.apex.sdx.core.converter.IMapping;
import com.apex.sdx.core.converter.MapStructConfig;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTgmsfcxsq;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTzdZhywsq;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025-02-17
 * @Description:
 */
@Mapper(config = MapStructConfig.class)
@Component
public interface JgkhxxhcsqVoMapping extends IMapping<JgkhxxhcsqVo, TOdsYgtCifTzdZhywsq> {
}
