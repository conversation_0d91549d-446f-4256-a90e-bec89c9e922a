package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.resp.common.Response;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.mybatis.entity.Tkhfxjsls;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.Thmdjyjgls;
import com.apex.sdx.core.mybatis.service.ThmdjyjglsService;
import com.apex.sdx.core.mybatis.mapper.ThmdjyjglsMapper;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class ThmdjyjglsServiceImpl extends ServiceImpl<ThmdjyjglsMapper, Thmdjyjgls>
    implements ThmdjyjglsService{

    @Override
    public Page<Thmdjyjgls> queryByCconditions(String khh, Integer ksrq, Integer jsrq, Integer jymdlx, Integer jydx, Integer jymdjg, boolean searchCount, int pagenum, int pagesize) {
        Page<Thmdjyjgls> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(searchCount);
        try {
            LambdaQueryWrapper<Thmdjyjgls> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Thmdjyjgls::getKhh, khh);
            wrapper.ge(ksrq != null, Thmdjyjgls::getRq, ksrq);
            wrapper.le(jsrq != null, Thmdjyjgls::getRq, jsrq);
            wrapper.eq(jymdlx != null, Thmdjyjgls::getJymdlx, jymdlx);
            wrapper.eq(jydx != null, Thmdjyjgls::getJydx, jydx);
            wrapper.eq(jymdjg != null, Thmdjyjgls::getJymdjg, jymdjg);
            wrapper.orderByDesc(Thmdjyjgls::getId);
            this.page(page, wrapper);
            return page;

        } catch (Exception e) {
            String note = String.format("黑名单校验结果流水查询失败[%s]", e.getMessage());
            log.error(note, e);
            throw new BusinessException(Response.buildErrorMsg(-601, note));
        }
    }
}




