package com.apex.sdx.core.adapter;

import org.apache.commons.lang3.StringUtils;

/**
 * mysql数据库sql函数适配器
 *
 * <AUTHOR>
 * @date 2023/3/29
 */
public class MysqlAdapter extends AbstractSqlAdapter {

    @Override
    public String sysdate() {
        return "now()";
    }

    @Override
    public String date_8_format() {
        return date_8_format(sysdate());
    }

    @Override
    public String date_8_format(String date) {
        return "date_format(" + date + ",'%Y%m%d')";
    }

    @Override
    public String date_10_format() {
        return date_10_format(sysdate());
    }

    @Override
    public String date_10_format(String date) {
        return "date_format(" + date + ",'%Y-%m-%d')";
    }

    @Override
    public String date_18_format() {
        return date_18_format(sysdate());
    }

    @Override
    public String date_18_format(String date) {
        return "date_format(" + date + ",'%Y-%m-%d %H:%i:%S')";
    }

    @Override
    public String time_8_format() {
        return "time_format(" + sysdate() + ", '%H%i%S')";
    }

    @Override
    public String time_10_format() {
        return "time_format(" + sysdate() + ", '%H:%i:%S')";
    }

    @Override
    public String to_date_8(String obj) {
        return "str_to_date(" + obj + ", '%Y%m%d')";
    }

    @Override
    public String to_date_10(String obj) {
        return "str_to_date(" + obj + ", '%Y-%m-%d')";
    }

    @Override
    public String to_date_18(String obj) {
        return "str_to_date(" + obj + ",'%Y-%m-%d %H:%i:%S')";
    }

    @Override
    public String nvl(String field,String fieldMpp, String defaultValue) {
        if (StringUtils.isBlank(defaultValue)) {
            //默认值为空时，设置为空字符串
            defaultValue = "''";
        }
        if (StringUtils.isNotBlank(fieldMpp)) {
            return String.format("ifnull(%s,%s) as %s", field, defaultValue, fieldMpp);
        } else {
            return String.format("ifnull(%s,%s)", defaultValue, field);
        }
    }

    @Override
    public String concat(String str1, String... str2) {
        String concat = "CONCAT(" + str1;
        for (String str : str2) {
            concat += (", " + str);
        }
        concat += ")";
        return concat;
    }
}
