package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName tic_zbcs
 */
@TableName(value ="tic_zbcs")
@Data
public class TicZbcs implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 
     */
    private Long fid;

    /**
     * 
     */
    private Long grade;

    /**
     * 
     */
    private Long type;

    /**
     * 
     */
    private String name;

    /**
     * 
     */
    private String fdncode;

    /**
     * 
     */
    private String idxCode;

    /**
     * 
     */
    private String idxCodeSrc;

    /**
     * 
     */
    private String idxNm;

    /**
     * 
     */
    private String idxDisplayName;

    /**
     * 
     */
    private Long idxCl;

    /**
     * 
     */
    private Long idxGrd;

    /**
     * 
     */
    private Long status;

    /**
     * 
     */
    private String descr;

    /**
     * 
     */
    private Integer prcs;

    /**
     * 
     */
    private Integer department;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TicZbcs other = (TicZbcs) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFid() == null ? other.getFid() == null : this.getFid().equals(other.getFid()))
            && (this.getGrade() == null ? other.getGrade() == null : this.getGrade().equals(other.getGrade()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getFdncode() == null ? other.getFdncode() == null : this.getFdncode().equals(other.getFdncode()))
            && (this.getIdxCode() == null ? other.getIdxCode() == null : this.getIdxCode().equals(other.getIdxCode()))
            && (this.getIdxCodeSrc() == null ? other.getIdxCodeSrc() == null : this.getIdxCodeSrc().equals(other.getIdxCodeSrc()))
            && (this.getIdxNm() == null ? other.getIdxNm() == null : this.getIdxNm().equals(other.getIdxNm()))
            && (this.getIdxDisplayName() == null ? other.getIdxDisplayName() == null : this.getIdxDisplayName().equals(other.getIdxDisplayName()))
            && (this.getIdxCl() == null ? other.getIdxCl() == null : this.getIdxCl().equals(other.getIdxCl()))
            && (this.getIdxGrd() == null ? other.getIdxGrd() == null : this.getIdxGrd().equals(other.getIdxGrd()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getDescr() == null ? other.getDescr() == null : this.getDescr().equals(other.getDescr()))
            && (this.getPrcs() == null ? other.getPrcs() == null : this.getPrcs().equals(other.getPrcs()))
            && (this.getDepartment() == null ? other.getDepartment() == null : this.getDepartment().equals(other.getDepartment()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFid() == null) ? 0 : getFid().hashCode());
        result = prime * result + ((getGrade() == null) ? 0 : getGrade().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getFdncode() == null) ? 0 : getFdncode().hashCode());
        result = prime * result + ((getIdxCode() == null) ? 0 : getIdxCode().hashCode());
        result = prime * result + ((getIdxCodeSrc() == null) ? 0 : getIdxCodeSrc().hashCode());
        result = prime * result + ((getIdxNm() == null) ? 0 : getIdxNm().hashCode());
        result = prime * result + ((getIdxDisplayName() == null) ? 0 : getIdxDisplayName().hashCode());
        result = prime * result + ((getIdxCl() == null) ? 0 : getIdxCl().hashCode());
        result = prime * result + ((getIdxGrd() == null) ? 0 : getIdxGrd().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getDescr() == null) ? 0 : getDescr().hashCode());
        result = prime * result + ((getPrcs() == null) ? 0 : getPrcs().hashCode());
        result = prime * result + ((getDepartment() == null) ? 0 : getDepartment().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", fid=").append(fid);
        sb.append(", grade=").append(grade);
        sb.append(", type=").append(type);
        sb.append(", name=").append(name);
        sb.append(", fdncode=").append(fdncode);
        sb.append(", idxCode=").append(idxCode);
        sb.append(", idxCodeSrc=").append(idxCodeSrc);
        sb.append(", idxNm=").append(idxNm);
        sb.append(", idxDisplayName=").append(idxDisplayName);
        sb.append(", idxCl=").append(idxCl);
        sb.append(", idxGrd=").append(idxGrd);
        sb.append(", status=").append(status);
        sb.append(", descr=").append(descr);
        sb.append(", prcs=").append(prcs);
        sb.append(", department=").append(department);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}