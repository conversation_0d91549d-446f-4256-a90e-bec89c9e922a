<template>
  <div class="dxsdx_body_color" style="height: 100%">
    <a-row class="bg-col">
      <a-col :span="15" style="height: 100%">
        <a-row class="login-illustration">
          <a-col :span="3">
            <img src="../assets/images/logo.png" width="120"/>
          </a-col>
          <a-col :span="21" class="system-title">适当性管理平台</a-col>
        </a-row>
        <div class="copyright">Copyright © 2004-2024 Apexsoft, All rights reserved.</div>
      </a-col>
      <a-col :span="9" style="background-color: white">
        <div class="tyyhzx_dl_nav">
          <div class="tyzhzx_dl_box">
            <div class="dl_title">欢迎登录</div>
            <a-form
              :model="formState"
              name="normal_login"
              @finish="onFinish"
              @submit="onSubmit"
              layout="vertical"
            >
              <a-form-item
                label="用户名称"
                name="account"
                class="yhm"
              >
                <a-input type="text" placeholder="请输入用户名称" v-model:value="formState.account">
                  <template #prefix>
                    <span class="iconfont icon-yonghu" style="font-size: 20px"></span>
                  </template>
                </a-input>
              </a-form-item>
              <a-form-item
                label="用户密码"
                name="password"
                class="mm"
              >
                <a-input type="password" placeholder="请输入用户密码"
                         autocomplete="off"
                         v-model:value="formState.password">
                  <template #prefix>
                    <span class="iconfont icon-mima" style="font-size: 20px"></span>
                  </template>
                </a-input>
              </a-form-item>
              <a-form-item style="margin-left: 50px;">
                <a-checkbox v-model:checked="formState.remember">
                  记住账号
                </a-checkbox>
              </a-form-item>
              <a-form-item>
                <div class="btn">
                  <div style="margin: -8px 0 8px 0; color: red; min-height: 22px;">{{ loginError }}</div>
                  <a-button type="primary" html-type="submit" :loading="loading" style="background-color: #B48A3B">
                    登 录
                  </a-button>
                  <!--                  <div class="qywxsm"><a>企业微信扫码</a></div>-->
                </div>
              </a-form-item>
            </a-form>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import {defineComponent} from "vue";
import "@/assets/css/uuc.css";
import "@/assets/css/iconfont.css";

export default defineComponent({
  methods: {
    async onFinish(values) {
      console.log(values);
      try {
        /*if (!createUserStore().userid) {
          await createUserStore().login(values.account, values.password);
        }*/
        // 跳转到首页
        this.$router.push({
          path: "/",
        });
      } catch (error) {
        this.loginError = error;
      }
      this.loading = false;
    },
    onSubmit() {
      this.loading = true;
    },
  },
  data() {
    return {
      formState: {
        account: "",
        password: "",
        remember: true,
      },
      loading: false,
      loginError: "",
    };
  },
  mounted() {

  },
});
</script>
<style scoped>
.dxsdx_body_color {
  background-color: white;
  height: 100%;
}

.login-illustration {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.system-title {
  font-weight: 500;
  font-size: 18px;
  color: #222222;
  padding-left: 15px;
}

.bg-col {
  height: 100%;
  background-image: url('../assets/images/dxsdxglpt_login_bg.png');
  background-size: cover;
  background-position: left bottom;
  background-repeat: no-repeat;
}

.copyright {
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 32px;
  opacity: 0.6;
  position: absolute;
  bottom: 10px;
  left: 10px;
}
</style>
