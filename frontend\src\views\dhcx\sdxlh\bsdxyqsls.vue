<template>
  <div class="sdxlx-tabs">
    <a-tabs v-model:activeKey="activeKey" type="card" size="small">
      <a-tab-pane key="1" tab="权限不适当">
        <div class="dxsdx_cxtj">
          <div class="cxtj_item">
            <span>开始日期：</span>
            <a-date-picker v-model:value="ksrq" style="width: 200px" format="YYYY/MM/DD" valueFormat="YYYYMMDD"
                           placeholder="开始日期"/>
          </div>
          <div class="cxtj_item">
            <span>结束日期：</span>
            <a-date-picker v-model:value="jsrq" style="width: 200px" format="YYYY/MM/DD" valueFormat="YYYYMMDD"
                           placeholder="结束日期"/>
          </div>
          <div class="cxtj_item">
            <span>创新业务类别：</span>
            <a-select v-model:value="cxywlb" style="width: 200px" :allowClear="true"
                      :options="dictArray?.SDX_CXYWLB" placeholder="请选择创新业务类别"
                      :field-names="{ label: 'note', value: 'ibm' }"
                      show-search
                      :filter-option="filterCxywlbOption"
                      option-filter-prop="children"></a-select>
          </div>
          <div class="cxtj_item">
            <a class="btn" @click="query">查询</a>
            <a class="btn fz" @click="reset">重置</a>
          </div>
        </div>
        <a-divider style="margin: 12px 0"/>
        <div>
          <a-table :scroll="{ x: 'max-content' }" sticky :columns="qxbsdColumns" :data-source="qxbsdData" :loading="loading">
          </a-table>
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" tab="产品不适当">
        <div class="dxsdx_cxtj">
          <div class="cxtj_item">
            <span>开始日期：</span>
            <a-date-picker v-model:value="ksrqcp" style="width: 200px" format="YYYY/MM/DD" valueFormat="YYYYMMDD"
                           placeholder="开始日期"/>
          </div>
          <div class="cxtj_item">
            <span>结束日期：</span>
            <a-date-picker v-model:value="jsrqcp" style="width: 200px" format="YYYY/MM/DD" valueFormat="YYYYMMDD"
                           placeholder="结束日期"/>
          </div>
          <div class="cxtj_item">
            <a class="btn" @click="query">查询</a>
            <a class="btn fz" @click="reset">重置</a>
          </div>
        </div>
        <a-divider style="margin: 12px 0"/>
        <div>
          <a-table :scroll="{ x: 'max-content' }" sticky :columns="cpbsdColumns" :data-source="cpbsdData" :loading="cploading">
          </a-table>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>
import {defineComponent} from "vue";
import {formatDate, getDcNote} from "@utils/bussinessUtils";
import {commonApi} from "@/api/common";
import {message} from "ant-design-vue";

export default defineComponent({
  name: "bsdxyqsls",
  inject: ["khh"],
  props: ['dictArray'],
  data() {
    return {
      activeKey: "1",
      ksrq: null,
      jsrq: null,
      ksrqcp: null,
      jsrqcp: null,
      cpdm: null,
      cxywlb: null,
      publicColumns: [
        {
          title: '签署日期', dataIndex: 'qsrq', key: 'qsrq', width: 150,
          customRender: ({text}) => {
            return formatDate(text);
          }
        },
        {
          title: '客户风险等级', dataIndex: 'fxcsnl', key: 'fxcsnl',
          customRender: ({text}) => {
            return getDcNote("SDX_FXCSNL", text, this.dictArray);
          }
        },
        {
          title: '客户投资期限', dataIndex: 'khtzqx', key: 'khtzqx',
          customRender: ({text}) => {
            return getDcNote("SDX_TZQX", text, this.dictArray);
          }
        },
        {
          title: '客户投资品种', dataIndex: 'khtzpz', key: 'khtzpz',width: 250,
          customRender: ({text}) => {
            return getDcNote("SDX_TZPZ", text, this.dictArray);
          }
        },
        {
          title: '客户预期收益', dataIndex: 'khyqsy', key: 'khyqsy',
          customRender: ({text}) => {
            return getDcNote("SDX_YQSY", text, this.dictArray);
          }
        },
        {
          title: '业务风险等级', dataIndex: 'ywfxdj', key: 'ywfxdj',
          customRender: ({text}) => {
            return getDcNote("SDX_YWFXDJ", text, this.dictArray);
          }
        },
        {
          title: '业务投资期限', dataIndex: 'ywtzqx', key: 'ywtzqx',
          customRender: ({text}) => {
            return getDcNote("SDX_TZQX", text, this.dictArray);
          }
        },
        {
          title: '业务投资品种', dataIndex: 'ywtzpz', key: 'ywtzpz',
          customRender: ({text}) => {
            return getDcNote("SDX_TZPZ", text, this.dictArray);
          }
        },
        {
          title: '业务预期收益', dataIndex: 'ywyqsy', key: 'ywyqsy',
          customRender: ({text}) => {
            return getDcNote("SDX_YQSY", text, this.dictArray);
          }
        },
        {title: '适当性不匹配类型', dataIndex: 'bsdlx', key: 'bsdlx',
          customRender: ({text}) => {
            return getDcNote("SDX_PPJG", text, this.dictArray);
          }
        },
      ],
      qxbsdColumns: [
        {
          title: '创新业务类别名称', dataIndex: 'cxywlb', key: 'cxywlb', fixed: true,
          customRender: ({text}) => {
            return getDcNote("SDX_CXYWLB", text, this.dictArray)
          }
        },
      ],
      qxbsdData: [],
      cpbsdColumns: [
        {
          title: '产品名称', dataIndex: 'cpmc', key: 'cpmc', fixed: true,
        },
/*
        {
          title: '产品名称', dataIndex: 'cpdm', key: 'cpdm', fixed: true,
          customRender: ({text}) => {
            return getDcNote("SDX_CXYWLB", text, this.dictArray)
          }
        },
*/
      ],
      cpbsdData: [],
      cploading:true,
      loading: true,
    }
  },
  watch: {
    activeKey(val) {
      if (this.cpbsdData.length > 0 && this.qxbsdData.length > 0) return;//已经查询过，不再重复查询，除非点击查询按钮
      this.query();
    }
  },
  methods: {
    filterCxywlbOption(input, option) {
      return (
          option.ibm.toLowerCase().indexOf(input.toLowerCase()) >= 0 || option.note.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    reset(){
      if(this.activeKey == 1){
        this.ksrq = null;
        this.jsrq = null;
        this.cxywlb = null;
      }else{
        this.ksrqcp = null;
        this.jsrqcp = null;
        this.cpdm = null;
      }
    },
    query() {
      if (this.activeKey == 1) {
        //查询权限不适当
        this.loading = true;
        commonApi.executeAMS(
            "sdx.query.ICxbsdxyqslsService",
            "qxbsdxyqslscx",
            {ksrq: this.ksrq, jsrq: this.jsrq, cxywlb: this.cxywlb,khh:this.khh},
        ).then((res) => {
          if (res.code < 0) {
            message.error(res.note);
          } else {
            this.qxbsdData = res.records || [];
          }
        }).finally(() => {
          this.loading = false;
        })
      } else {
        //查询产品不适当
        this.cploading = true;
        commonApi.executeAMS(
            "sdx.query.ICxbsdxyqslsService",
            "cpbsdxyqslscx",
            {ksrq: this.ksrqcp, jsrq: this.jsrqcp, cpdm: this.cpdm,khh:this.khh},
        ).then((res) => {
          if (res.code < 0) {
            message.error(res.note);
          } else {
            this.cpbsdData = res.records || [];
          }
        }).finally(() => {
          this.cploading = false;
        })
      }
    },
  },
  mounted() {
    this.query();
    this.qxbsdColumns = [...this.qxbsdColumns, ...this.publicColumns];
    this.cpbsdColumns = [...this.cpbsdColumns, ...this.publicColumns];
  },
});
</script>
<style scoped>
.sdxlx-tabs {
  padding: 0;
}

:deep(.ant-tabs-card .ant-tabs-tab) {
  background-color: #F4F5F7;
  border-radius: 4px !important;
  margin: 5px;
  border: unset;
}

:deep(.ant-tabs-card .ant-tabs-tab-active) {
  background-color: #B48A3B;
  color: #FFFFFF;
}

:deep(.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
  color: #FFFFFF !important;
}

:deep(.ant-tabs-tab-btn) {
  color: #333333;
}

:deep(.ant-tabs-tab:before) {
  display: inline-block;
  content: "";
  width: 1px;
  height: 24px;
  background-color: #eee;
  vertical-align: middle;
  margin-top: -2px;
  position: relative;
  left: -20px;
}

.dxsdx_cxtj {
  line-height: 32px;
  padding: 0 5px 3px 10px;
}

.dxsdx_cxtj .cxtj_item {
  white-space: nowrap;
  margin-bottom: 12px;
  font-size: 14px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 20px;
}

.dxsdx_cxtj .cxtj_item span {
  color: #888;
  display: inline-block;
  margin-right: 15px;
  vertical-align: middle;
}

.dxsdx_cxtj .cxtj_item input[type=text] {
  display: inline-block;
  vertical-align: middle;
  width: 340px;
  height: 32px;
  line-height: 30px;
  border: 1px solid #d6d6d6;
  border-radius: 4px;
  padding: 0 10px;
}

.dxsdx_cxtj .cxtj_item input[type=text]:focus {
  outline: 1px solid #d0ad6b;
}

a.btn {
  min-width: 80px;
  padding: 0 10px;
  margin-right: 10px;
  text-align: center;
  line-height: 32px;
  border-radius: 4px;
  background-color: #f6e5d1;
  color: #bf935f;
  cursor: pointer;
  display: inline-block;
  vertical-align: middle;
}

a.btn.fz {
  background-color: #fff;
  color: #777;
  border: 1px solid #d6d6d6;
  line-height: 30px;
}

a.btn:hover, a.btn.fz:Hover {
  background-color: #bf935f;
  color: #fff;
  border: none;
  line-height: 32px;
}

:deep(.ant-spin .ant-spin-dot-item){
  background-color: #bf935f;
}
</style>