<template>

  <modal-component
      :title="title"
      :open="modalVisible"
      width="1050px"
      :on-cancel="modalOk"
      style="top: 30px;"
  >
    <template #content>
      <div class="modal-container">
        <div class="cljg-item">
          <a-form layout='inline' style="padding: 15px 15px;background: #fff;border-radius: 8px 8px 0 0;"
                  v-if="showForm">
            <!--      适当性事件-->
            <a-form-item label="事件编码" name="sjbm" v-if="type=='sj'">
              <a-input v-model:value="modalSearch.sjbm" placeholder="请输入事件编码" :allowClear="true"></a-input>
            </a-form-item>

            <a-form-item label="日期范围" name="rqfw" v-if="type=='sj'">
              <a-range-picker v-model:value="modalSearch.rqfw" :format="dateFormat" valueFormat="YYYYMMDD"/>
            </a-form-item>

            <!--      业务不适当协议签署情况-->
            <a-form-item label="签署状态" name="qszt" v-if="type=='ywbsdxeqsqk'">
              <a-select style="width: 200px" v-model:value="modalSearch.qszt" :allowClear="true">
                <a-select-option value="0">未签署</a-select-option>
                <a-select-option value="1">已签署</a-select-option>
              </a-select>
            </a-form-item>

            <!--      权限适当性整体情况top5/业务不适当协议签署情况-->
            <a-form-item label="创新业务类别" name="cxywlb" v-if="type=='ztqk' || type=='ywbsdxeqsqk'">
<!--              <a-input v-model:value="modalSearch.cxywlb" placeholder="请输入创新业务类别" :allowClear="true"></a-input>-->
              <a-select style="width: 200px" v-model:value="modalSearch.cxywlb" :allowClear="true"
                        :options="dictArray?.SDX_CXYWLB" placeholder="请选择创新业务类别"
                        :field-names="{ label: 'note', value: 'ibm' }"
                        show-search
                        :filter-option="filterCxywlbOption"
                        option-filter-prop="children"></a-select>
            </a-form-item>

            <!--      账户状态-->
            <a-form-item label="账户情况" name="zhqk" v-if="type=='zhzt'">
              <a-select style="width: 200px" v-model:value="modalSearch.zhzt.zhqk" :allowClear="true">
                <a-select-option value="0">正常</a-select-option>
                <a-select-option value="1">异常</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="账户状态" name="zhzt" v-if="type=='zhzt'">
              <a-select style="width: 200px" v-model:value="modalSearch.zhzt.zhzt" :allowClear="true">
                <a-select-option value="0">正常</a-select-option>
                <a-select-option value="1">冻结</a-select-option>
                <a-select-option value="3">销户</a-select-option>
                <a-select-option value="6">小额休眠</a-select-option>
                <a-select-option value="7">不合格</a-select-option>
                <a-select-option value="9">公司不合格</a-select-option>
                <a-select-option value="99">开户锁定</a-select-option>
              </a-select>
            </a-form-item>

            <!--      反洗钱风险等级-->
            <a-form-item label="反洗钱风险等级" name="xqfxdj" v-if="type=='fxqfxdj'">
              <a-select style="width: 200px" v-model:value="modalSearch.fxqfxdj.xqfxdj" :allowClear="true">
                <a-select-option value="1">正常</a-select-option>
                <a-select-option value="2">关注</a-select-option>
                <a-select-option value="3">高风险</a-select-option>
              </a-select>
            </a-form-item>



            <!--      证件有效期-->
            <a-form-item label="账户情况" name="zhqk" v-if="type=='zjyxq'">
              <a-select style="width: 200px" v-model:value="modalSearch.zjyxq.zhqk" :allowClear="true">
                <a-select-option value="有效">有效</a-select-option>
                <a-select-option value="过期">过期</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="证件有效期" name="zjjzrq" v-if="type=='zjyxq'">
              <a-date-picker v-model:value="modalSearch.zjyxq.zjjzrq" :format="dateFormat" valueFormat="YYYYMMDD"/>
            </a-form-item>

            <!--      风险测评有效期-->
            <a-form-item label="账户情况" name="zhqk" v-if="type=='fxcpyxq'">
              <a-select style="width: 200px" v-model:value="modalSearch.fxcpyxq.zhqk" :allowClear="true">
                <a-select-option value="有效">有效</a-select-option>
                <a-select-option value="过期">过期</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="风险测评有效期" name="cpyxq" v-if="type=='fxcpyxq'">
              <a-date-picker v-model:value="modalSearch.fxcpyxq.cpyxq" :format="dateFormat" valueFormat="YYYYMMDD"/>
            </a-form-item>

            <a-form-item label="其他信息状态" name="qtxxzt" v-if="type=='qtxx'">
              <a-select style="width: 200px" v-model:value="modalSearch.qtxx.qtxxzt" :allowClear="true">
                <a-select-option value="0">正常</a-select-option>
                <a-select-option value="1">黑名单</a-select-option>
                <a-select-option value="2">疑似黑名单</a-select-option>
              </a-select>
            </a-form-item>


            <a-form-item label="客户号" name="khh">
              <a-input v-model:value="modalSearch.khh" placeholder="请输入客户号" :allowClear="true"></a-input>
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="onModalSearch" style="color: #B48A3B; background-color: #f6e5d1">查询
              </a-button>
            </a-form-item>
          </a-form>
          <div class="sdxsj-content">
            <a-table
                :columns="modalColumns"
                :data-source="modalList"
                :loading="modalLoading"
                :pagination="pagination"
                @change="handleTableChange"
            >
            </a-table>
          </div>
        </div>
      </div>
    </template>
  </modal-component>
</template>
<script>
import {defineComponent} from 'vue';
import ModalComponent from "@components/ModalComponent.vue";
import {commonApi} from "@/api/common";
import {message} from "ant-design-vue";
import {dictApi} from "@/api/xtgl/dict";
import {formatDate} from "@utils/bussinessUtils";

export default defineComponent({
  name: "index",
  components: {ModalComponent},
  props: ["open", "title", "type","lastTradingDay"],
  data() {
    return {
      dateFormat: 'YYYY/MM/DD',
      modalVisible: this.open,
      modalColumns: [],
      current: 1,
      pageSize: 5,
      total: 0,
      cpccsdxColumns: [
        /*  {
            title: '事件ID', dataIndex: 'sjid',
          },
          {
            title: '事件编码', dataIndex: 'sjbm',
          },*/
        {
          title: '事件名称', dataIndex: 'sjmc', align: 'left',
        },
        {
          title: '事件数量', dataIndex: 'sjsl', align: 'right',
        },
        {
          title: '处理数量', dataIndex: 'clsl', align: 'right',
        },
        {
          title: '处理占比',
          dataIndex: 'clzb',
          align: 'right',
          customRender: (param) => this.formatPercent(param.value),
        },
        {
          title: '待处理数量', dataIndex: 'dclsl', align: 'right',
        },
        {
          title: '待处理占比',
          dataIndex: 'dclzb',
          align: 'right',
          customRender: (param) => this.formatPercent(param.value),
        },
      ], //适当性事件情况模态框columns
      qxsdxztqkColumns: [
       /* {
          title: '创新业务类别', dataIndex: 'cxywlb', align: 'right',
        },*/
        {
          title: '创新业务名称', dataIndex: 'cxywmc', align: 'left',
        },
        {
          title: '开通数量', dataIndex: 'ktsl', align: 'right',
        },
        {
          title: '适当数量', dataIndex: 'sdsl', align: 'right',
        },
        {
          title: '适当占比',
          dataIndex: 'sdzb',
          align: 'right',
          customRender: (param) => this.formatPercent(param.value),
        },
        {
          title: '不适当数量', dataIndex: 'bsdsl', align: 'right',
        },
        {
          title: '不适当占比',
          dataIndex: 'bsdzb',
          align: 'right',
          customRender: (param) => this.formatPercent(param.value),
        },
        /*{
          title: '未签署不适当协议数量', dataIndex: 'wqsbsdxysl',
        },
        {
          title: '已签署不适当协议数量', dataIndex: 'yqsbsdxysl',
        },*/
      ], //权限适当性整体情况模态框columns
      ywbsdxeqsqkColumns: [
        {
          title: '客户号', dataIndex: 'khh', align: 'right',
        },
        /*{
          title: '创新业务类别', dataIndex: 'cxywlb', align: 'right',
        },*/
        {
          title: '创新业务类别', dataIndex: 'cxywmc', align: 'left',
        },
        {
          title: '适当性结果', dataIndex: 'sdxjg', align: 'left',
        },
        {
          title: '签署状态', dataIndex: 'qsbsdxy', align: 'left',
        },
      ],//业务不适当协议签署情况模态框columns
      zhsdxqkColumns: [
        {
          title: '客户号', dataIndex: 'khh',
        },
        {
          title: '客户名称', dataIndex: 'khmc',
        },
        {
          title: '账户情况', dataIndex: 'zhqk',
        },
      ],//账户适当性情况公共部分模态框columns
      zhztColumns: [
        {
          title: '账户状态', dataIndex: 'zhzt',
        },

      ], //账户状态模态框columns
      fxqfxdjColumns: [
        {
          title: '反洗钱风险等级', dataIndex: 'xqfxdj',
        },
        {
          title: '反洗钱设置日期', dataIndex: 'fxqszrq', customRender: (param) => formatDate(param.value),
        },
      ], //反洗钱风险等级模态框columns

      zjyxqColumns: [
        {
          title: '证件有效期', dataIndex: 'zjjzrq', customRender: (param) => formatDate(param.value),
        },
      ], //证件有效期模态框columns
      fxcpyxqColumns: [
        {
          title: '风险测评有效期', dataIndex: 'cpyxq', customRender: (param) => formatDate(param.value),
        },
      ], //风险测评有效模态框columns
      qtxxColumns: [
        {
          title: '校验名单结果', dataIndex: 'jymdjg',
        },
      ], //其他信息模态框columns
      modalList: [],
      modalSearch: {zhzt: {zhqk: '1'}, fxqfxdj: {}, zjyxq: {zhqk: '过期'}, fxcpyxq: {zhqk: '过期'}, qtxx: {}},
      modalLoading: true,
      dictArray:[],//字典数组
    };
  },
  computed: {
    pagination() {
      return {
        showQuickJumper: true,
        showSizeChanger: true,
        total: this.total,
        current: this.current,
        pageSize: this.pageSize,
        showTotal(total) {
          return "共 " + total + " 条";
        },
      }
    },
    showForm() {
      return this.type != 'sj' & this.type != 'ztqk';
    },
  },
  watch: {
    open(newVal) {
      this.modalVisible = newVal;
      if (newVal) {
        this.modalSearch={zhzt: {zhqk: '1'}, fxqfxdj: {}, zjyxq: {zhqk: '过期'}, fxcpyxq: {zhqk: '过期'}, qtxx: {}};
        if (this.type == 'sj') {
          this.modalColumns = this.cpccsdxColumns;
        } else if (this.type == 'ztqk') {
          this.modalColumns = this.qxsdxztqkColumns;
        } else if (this.type == 'ywbsdxeqsqk') {
          this.modalColumns = this.ywbsdxeqsqkColumns;
          this.getSjzd();
        } else if (this.type == 'zhzt') {
          this.modalColumns = [...this.zhsdxqkColumns, ...this.zhztColumns];
        } else if (this.type == 'fxqfxdj') {
          this.modalColumns = this.zhsdxqkColumns.concat(this.fxqfxdjColumns);

        } else if (this.type == 'zjyxq') {
          this.modalColumns = this.zhsdxqkColumns.concat(this.zjyxqColumns);
        } else if (this.type == 'fxcpyxq') {
          this.modalColumns = this.zhsdxqkColumns.concat(this.fxcpyxqColumns);
        } else if (this.type == 'qtxx') {
          this.modalColumns = this.zhsdxqkColumns.concat(this.qtxxColumns);
        }
        this.onModalSearch();
      }
    }
  },
  methods: {
    handleTableChange(page) {
      this.current = page.current;
      this.pageSize = page.pageSize;
      this.searchClick();
    },
    onModalSearch() {
      this.current = 1;
      this.pageSize = 10;
      this.searchClick();
    },
    //模态窗口查询事件
    searchClick() {
      this.modalLoading = true;
      if (this.type == 'sj') {
        this.getSdxsjArr();
      } else if (this.type == 'ztqk') {
        this.getSdxztqk();
      } else if (this.type == 'ywbsdxeqsqk') {
        this.getYwbsdxeqsqk();
      } else if (this.type == 'zhzt') {
        this.getZhzt();
      } else if (this.type == 'fxqfxdj') {
        this.getFxqfxdj();

      } else if (this.type == 'zjyxq') {
        this.getZjyxq();
      } else if (this.type == 'fxcpyxq') {
        this.getFxcpyxq();
      } else if (this.type == 'qtxx') {
        this.getQtxx();
      }
    },
    //模态窗口确定事件
    modalOk() {
      this.modalVisible = false;
      this.$emit("update:open", false);
    },

    //获取产品持仓适当性情况列表
    getSdxsjArr() {
      let param = {
        tjwd: "2",
        sjbm: this.modalSearch.sjbm,
        ksrq: this.modalSearch.rqfw ? this.modalSearch.rqfw[0] : '',
        jsrq: this.modalSearch.rqfw ? this.modalSearch.rqfw[1] : '',
      };
      this.getData("sdx.query.ISdxsjService", "sdxsjtj", param);
    },

    //权限适当性整体情况
    getSdxztqk() {
      this.getData("sdx.query.IYktywsdxService", "ywktSdxtj4cxywlb", {cxywlb: this.modalSearch.cxywlb,rq:this.lastTradingDay});
    },

    //业务不适当协议签署情况
    getYwbsdxeqsqk() {
      let param = {
        cxywlb: this.modalSearch.cxywlb,
        khh: this.modalSearch.khh,
        qsbsdxy: this.modalSearch.qszt,
        rq:this.lastTradingDay
      };
      this.getData("sdx.query.IYktywsdxService", "qtyktywBsdxmxtj", param);
    },

    //账户状态
    getZhzt() {
      let param = {
        zhqk: this.modalSearch.zhzt.zhqk,
        zhzt: this.modalSearch.zhzt.zhzt,
        khh: this.modalSearch.khh,
      };
      this.getData("sdx.khgl.IKhxxcxService", "zhztmxcx", param);
    },
    getFxqfxdj() {
      let param = {
        xqfxdj: this.modalSearch.fxqfxdj.xqfxdj,
        khh: this.modalSearch.khh,
      };
      this.getData("sdx.khgl.IKhxxcxService", "fxqfxdjmxcx", param);
    },

    getZjyxq() {
      let param = {
        zhqk: this.modalSearch.zjyxq.zhqk,
        zjjzrq: this.modalSearch.zjyxq.zjjzrq,
        khh: this.modalSearch.khh,
      };
      this.getData("sdx.khgl.IKhxxcxService", "zjyxqmxcx", param);
    },
    getFxcpyxq() {
      let param = {
        zhqk: this.modalSearch.fxcpyxq.zhqk,
        cpyxq: this.modalSearch.fxcpyxq.cpyxq,
        khh: this.modalSearch.khh,
      };
      this.getData("sdx.khgl.IKhxxcxService", "fxcpyxmxcx", param);
    },
    getQtxx() {
      let param = {
        qtxxzt: this.modalSearch.qtxx.qtxxzt,
        khh: this.modalSearch.khh,
      };
      this.getData("sdx.khgl.IKhxxcxService", "qtxxmxcx", param);
    },

    getData(serviceId, serviceMethod, param) {
      param.pagenum = this.current;
      param.pagesize = this.pageSize
      param.isSearchCount = true;
      commonApi.executeAMS(
          serviceId,
          serviceMethod,
          param,
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        } else {
          this.modalList = res.records || [];
          this.total = res.total;
        }
      }).finally(() => {
        this.modalLoading = false;
      })
    },

    getSjzd() {
      dictApi.cxsjzd({ fldm: "SDX_CXYWLB" }).then((res) => {
        if (res.code > 0) {
          this.dictArray = res.sjzd;
        }
      });
    },
    filterCxywlbOption(input, option){
      return (
          option.ibm.toLowerCase().indexOf(input.toLowerCase()) >= 0 || option.note.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    formatPercent(vaule) {
      return vaule ? parseFloat((vaule * 100).toString()).toFixed(2) + '%' : '0.00%';
    },
  },
  mounted() {

  }
});
</script>
<style>
/*修改模态框内联样式*/
.modalClass.ant-modal .ant-modal-body {
  background: rgb(236, 236, 236) !important;
  padding: 10px !important;
}

.modalClass .ant-table-wrapper .ant-table {
  border-radius: 0 !important;
}

.modalClass .ant-btn-primary:not(:disabled):hover {
  background-color: #B48A3B7a !important;
}

.modalClass .ant-btn-primary {
  background-color: #B48A3B !important;
}

.ant-table-wrapper .ant-table-tbody>tr>td{
  padding: 12px 16px;
}

.ant-table-wrapper .ant-table-pagination.ant-pagination {
  margin: 10px 0 0 0;
}
</style>