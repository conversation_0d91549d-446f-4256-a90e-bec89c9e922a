package com.apex.sdx.api.req.query;

import com.apex.sdx.api.req.common.PageRequest;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-03-06
 * @Description:
 */
@Getter
@Setter
public class HmdjyjglscxReq extends PageRequest {
    @LiveProperty(note = "客户号", index = 1)
    private String khh;

    @LiveProperty(note = "校验名单类型", index = 2)
    private Integer jymdlx;

    @LiveProperty(note = "校验对象", index = 3)
    private Integer jydx;

    @LiveProperty(note = "开始日期", index = 4)
    private Integer ksrq;

    @LiveProperty(note = "结束日期", index = 5)
    private Integer jsrq;

    @LiveProperty(note = "校验名单结果", index = 6)
    private Integer jymdjg;
}
