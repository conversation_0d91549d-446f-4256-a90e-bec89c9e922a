import { createStore, createLogger } from 'vuex'
import getters from './getters'
import user from './modules/user'
import dict from './modules/dict'
import createPersistedState from "vuex-persistedstate"
// Create a new store instance.

const store = createStore({
    modules: {
        user,
        dict
    },
    plugins: [
        createLogger(),
        createPersistedState({
        storage: window.sessionStorage,
        reducer(val) {
            return { // 只储存state中的user
                user: val.user
            }
        }
    })],
    getters
})
export default store
