package sdx.query;

import com.apex.sdx.api.req.query.TgcpsdxcxReq;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.query.TgcpsdxVo;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR>
 * @Date 2025/6/11 18:57
 * @Description: TODO
 */
public interface ICxtgcpsdxService {

    @LiveMethod(paramAsRequestBody = true, note = "投顾产品适当性查询")
    QueryResponse<TgcpsdxVo> tgcpsdxcx(TgcpsdxcxReq req) throws Exception;
}
