package com.apex.sdx.svc;

import com.apex.sdx.api.req.compute.KhzscsReq;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.resp.query.KhsdxfbRes;
import com.apex.sdx.api.resp.query.KhzsDateRes;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.service.TicYyzbService;
import com.apex.sdx.core.mybatis.service.TkhxxService;
import com.apexsoft.LiveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import sdx.query.ICxkhsdxfbService;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/9 14:01
 * @Description: TODO
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "客户适当性分布查询服务")
@RequiredArgsConstructor
public class CxkhsdxfbService implements ICxkhsdxfbService {

    private final TicYyzbService ticYyzbService;

    private final TkhxxService tkhxxService;

    @Override
    public QueryResponse<KhsdxfbRes> khzs(KhzscsReq req) throws Exception {
        QueryResponse<KhsdxfbRes> result = new QueryResponse<>(1, "查询成功");
        String type = req.getType();
        String rq = req.getRq();

        List<KhsdxfbRes> list = ticYyzbService.khzs(type, rq);
        result.setRecords(list);
        return result;
    }

    @Override
    public QueryResponse<KhzsDateRes> zkhsDate(KhzscsReq req) throws Exception {
        QueryResponse<KhzsDateRes> result = new QueryResponse<>(1, "查询成功");
        String type = req.getType();
        String rq = req.getRq();
        List<KhzsDateRes> list = ticYyzbService.zkhsDate(type, rq);
        result.setRecords(list);
        return result;
    }

    @Override
    public QueryResponse<KhsdxfbRes> getTzzflxx(KhzscsReq req) throws Exception {
        QueryResponse<KhsdxfbRes> result = new QueryResponse<>(1, "查询成功");
        String type = req.getType();
        List<KhsdxfbRes> list = tkhxxService.queryTzzflxx(type);
        result.setRecords(list);
        return result;
    }

    @Override
    public QueryResponse<KhsdxfbRes> getFxcsnlxx(KhzscsReq req) throws Exception {
        QueryResponse<KhsdxfbRes> result = new QueryResponse<>(1, "查询成功");
        String type = req.getType();
        List<KhsdxfbRes> list = tkhxxService.queryFxcsnl(type);
        result.setRecords(list);
        return result;
    }
    
    @Override
    public QueryResponse<KhsdxfbRes> getAbcTzzflxx(KhzscsReq req) throws Exception {
        QueryResponse<KhsdxfbRes> result = new QueryResponse<>(1, "查询成功");
        List<KhsdxfbRes> list = tkhxxService.queryAbcTzzflxx();
        result.setRecords(list);
        return result;
    }
}
