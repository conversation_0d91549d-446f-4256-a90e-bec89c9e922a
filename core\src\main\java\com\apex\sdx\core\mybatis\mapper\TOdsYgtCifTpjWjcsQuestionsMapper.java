package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.api.vo.khgl.DcwjxqVo;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjWjcsQuestions;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Entity com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjWjcsQuestions
 */
public interface TOdsYgtCifTpjWjcsQuestionsMapper extends BaseMapper<TOdsYgtCifTpjWjcsQuestions> {

    @Select("SELECT WJ.ID, " +
            "       WJ.TPJ_WJCS_ID, " +
            "       WJ.BH, " +
            "       TK.QTYPE, " +
            "       TK.QDESCRIBE QDESCRIBE, " +
            "       TK.SANSWER, " +
            "       TK.SJSYFF, " +
            "       TK.glkhsxyz, " +
            "       TK.gxgz, " +
            "       TK.dajx " +
            "FROM ods.t_ods_ygt_cif_tpj_wjcs_questions WJ, " +
            "     ods.t_ods_ygt_cif_tpj_tmcs TK " +
            "WHERE TK.ID = WJ.QID " +
            "  AND WJ.TPJ_WJCS_ID = #{id} " +
            "ORDER BY WJ.BH;")
    List<DcwjxqVo> selectQuestionByWjcsId(@Param("id") Long id);
}




