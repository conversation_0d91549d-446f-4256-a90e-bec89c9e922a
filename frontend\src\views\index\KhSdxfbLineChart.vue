<template>
  <div>
    <div :id="chartsId" style=" width:80%; height:180px;margin: 0 25px auto">
    </div>
  </div>

</template>
<script>
import {defineComponent} from 'vue';

let barCharts = [];
export default defineComponent({
  props: ["dataset", "title"],
  data() {
    return {
      labels: [],//名称
      values: [],//值
      min:0,//最小值
    }
  },
  methods: {
    initBarChart() {
      let el = document.getElementById(this.chartsId)
      barCharts[this.chartsId] = this.$echarts.init(el);
      this.setOption();
    },
    setOption() {
      let _this = this;
      this.labels = this.$props.dataset[0];
      this.values = this.$props.dataset[1];
      this.min = Math.min(this.values);
      let option = _this.getOption();
      barCharts[_this.chartsId].setOption(option)
    },
    getOption() {
      let _this = this;
      let option = {
        color: ['#FF7800'],
        grid: {
          left: '0%',
          right: '10%',
          bottom: '30%',
        //  containLabel: true
        },
        tooltip: {
          className: 'myTooltip', // 指定自定义类名
          trigger: 'axis',
          formatter: function (params) {
            return '<div style="padding: 10px;background-color: #D9E1F0;border-radius: 10px;">\n' +
                '            <span style="color: #454A55;">'+params[0].name.replace(/(\d{4})(\d{2})(\d{2})/, "$1-$2-$3")+'</span>\n' +
                '            <div style="padding: 0 0 0 10px;background-color: #fff;border-radius: 10px;">\n' +
                '              <span style="color: #454A55;">'+params[0].value+'</span>\n' +
                '            </div>\n' +
                '          </div>';
          }
       /*   axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'cross',
            label: {
              backgroundColor: 'rgba(255, 120, 0, 0.5)'
            }
          }*/
        },
        yAxis: {
          type: 'value',
          show:false,
          min:this.min-10,
        },
        xAxis: {
          type: 'category',
          data: this.labels,
          show:false,
        },
        series: this.getSeriesData(),
      };
      return option;
    },
    getSeriesData() {
      let data = [];
      let _this = this;
      data.push({
        type: 'line',
        data: this.values,
        areaStyle: {
          color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {offset: 0, color: '#fdf0e5'},
            {offset: 1, color: '#faf8f8'},
          ]),
        },
        lineStyle:{
          color:'#FF7800',
        },
        showSymbol: false,//是否显示拐点
        smooth: true,//平滑曲线
        stack: 'Total',//堆叠
      });
      return data;
    },

  },
  mounted() {
    if (this.$props.dataset && this.$props.dataset.length > 0) {
      this.initBarChart()
    }
  },
  computed: {
    chartsId() {
      return "id_" + this.title
    },
  },
  watch: {
    dataset(newValue, oldValue) {
      if (newValue && newValue.length > 0 && JSON.stringify(newValue) != JSON.stringify(oldValue)) {
        if (barCharts[this.chartsId]) {
          this.setOption();
        } else {
          this.initBarChart();
        }
      }
    }
  },
  unmounted() {
    this.removeListener();
    if (barCharts[this.chartsId]) {
      barCharts[this.chartsId].dispose();
      delete barCharts[this.chartsId];
    }
  },
});
</script>

<style scoped>
:deep(.myTooltip){
  padding: 0 !important;
  border-radius: 10px !important;
  border-width: 0px !important;
}
</style>