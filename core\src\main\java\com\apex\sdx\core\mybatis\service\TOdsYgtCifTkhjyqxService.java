package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.api.vo.khgl.KhjyqxVo;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhjyqx;
import com.apex.sdx.core.mybatis.entity.Tkhjyqx;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *
 */
public interface TOdsYgtCifTkhjyqxService extends IService<TOdsYgtCifTkhjyqx> {

    Page<KhjyqxVo> queryKhjyqx(String khh, String ywzh, String gdh, Integer jyqx, Integer zt, boolean searchCount, int pagesize, int pagenum);
}
