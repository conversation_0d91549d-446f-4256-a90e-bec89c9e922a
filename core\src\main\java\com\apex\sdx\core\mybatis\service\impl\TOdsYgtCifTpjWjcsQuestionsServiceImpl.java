package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.vo.khgl.DcwjxqVo;
import com.apex.sdx.core.exception.BusinessException;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjWjcsQuestions;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTpjWjcsQuestionsService;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTpjWjcsQuestionsMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class TOdsYgtCifTpjWjcsQuestionsServiceImpl extends ServiceImpl<TOdsYgtCifTpjWjcsQuestionsMapper, TOdsYgtCifTpjWjcsQuestions>
    implements TOdsYgtCifTpjWjcsQuestionsService{

    @Override
    public List<DcwjxqVo> getQuestionByWjcsId(Long id) {
        try {
            return this.baseMapper.selectQuestionByWjcsId(id);
        } catch (Exception e) {
            String note = String.format("查询答案串问题异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }
}




