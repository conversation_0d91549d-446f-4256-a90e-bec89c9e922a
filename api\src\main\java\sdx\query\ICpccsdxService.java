package sdx.query;

import com.apex.sdx.api.req.compute.QtcccptjReq;
import com.apex.sdx.api.req.query.CpccsdxcxReq;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.compute.QtcccptjVo;
import com.apex.sdx.api.vo.query.CpccsdxVo;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR>
 * @Date 2025-03-05
 * @Description:
 */
public interface ICpccsdxService {

    @LiveMethod(paramAsRequestBody = true, note = "产品持仓适当性查询")
    QueryResponse<CpccsdxVo> cpccsdxcx(CpccsdxcxReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "全体持仓产品适当性统计")
    QueryResponse<QtcccptjVo> qtcccpSdxtj(QtcccptjReq req) throws Exception;
}
