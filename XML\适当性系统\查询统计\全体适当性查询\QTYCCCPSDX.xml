<?xml version="1.0" encoding="GB2312" standalone="no"?>
<resource create-date="2025.03.21 13:47:10" creator="��Ҿ�" flag="0" name="QTYCCCPSDX" object-id="99df7089-5762-4a69-a1f7-a78e5c0fa450" package="�ʵ���ϵͳ.��ѯͳ��.ȫ���ʵ��Բ�ѯ" type="35" version="V3.4.0.0">
    <describe>ȫ���ѳֲֲ�Ʒ�ʵ���</describe>
    <attribute>264</attribute>
    <parameters>
        <column>
            <name>JZJY</name>
            <describe>���н���</describe>
            <type>1</type>
            <biz-type>200</biz-type>
            <length>10</length>
            <scale>0</scale>
            <control>1</control>
            <primary-property>0</primary-property>
            <nullable>true</nullable>
            <unique>false</unique>
            <enabled>false</enabled>
            <visible>false</visible>
            <option/>
            <ref-object/>
            <default>1000</default>
            <attribute>0</attribute>
            <order-direction>0</order-direction>
            <category/>
            <fire-event>0</fire-event>
            <check-type>0</check-type>
            <real-type>0</real-type>
        </column>
        <column>
            <name>OTC</name>
            <describe>OTC</describe>
            <type>1</type>
            <biz-type>200</biz-type>
            <length>10</length>
            <scale>0</scale>
            <control>1</control>
            <primary-property>0</primary-property>
            <nullable>true</nullable>
            <unique>false</unique>
            <enabled>false</enabled>
            <visible>false</visible>
            <option/>
            <ref-object/>
            <default>1003</default>
            <attribute>0</attribute>
            <order-direction>0</order-direction>
            <category/>
            <fire-event>0</fire-event>
            <check-type>0</check-type>
            <real-type>0</real-type>
        </column>
        <column>
            <name>TGFW</name>
            <describe>Ͷ�˷���</describe>
            <type>1</type>
            <biz-type>200</biz-type>
            <length>10</length>
            <scale>0</scale>
            <control>1</control>
            <primary-property>0</primary-property>
            <nullable>true</nullable>
            <unique>false</unique>
            <enabled>false</enabled>
            <visible>false</visible>
            <option/>
            <ref-object/>
            <default>1004</default>
            <attribute>0</attribute>
            <order-direction>0</order-direction>
            <category/>
            <fire-event>0</fire-event>
            <check-type>0</check-type>
            <real-type>0</real-type>
        </column>
        <column>
            <name>GMTG</name>
            <describe>��ļͶ��</describe>
            <type>1</type>
            <biz-type>200</biz-type>
            <length>10</length>
            <scale>0</scale>
            <control>1</control>
            <primary-property>0</primary-property>
            <nullable>true</nullable>
            <unique>false</unique>
            <enabled>false</enabled>
            <visible>false</visible>
            <option/>
            <ref-object/>
            <default>1005</default>
            <attribute>0</attribute>
            <order-direction>0</order-direction>
            <category/>
            <fire-event>0</fire-event>
            <check-type>0</check-type>
            <real-type>0</real-type>
        </column>
    </parameters>
    <items>
        <item object-name="xnQTYCCCPSDX_JZJY" reload="false">
            <describe>���н���</describe>
            <constraint>
                <parameter-map>
                    <item-parameter>SJLY</item-parameter>
                    <source-parameter>JZJY</source-parameter>
                </parameter-map>
            </constraint>
        </item>
        <item object-name="QTYCCCPSDX-OTC" reload="false">
            <describe>���⽻��</describe>
            <constraint>
                <parameter-map>
                    <item-parameter>SJLY</item-parameter>
                    <source-parameter>OTC</source-parameter>
                </parameter-map>
            </constraint>
        </item>
        <item object-name="xnQTYDYTGSDX_TGFW" reload="false">
            <describe>Ͷ�˷���</describe>
            <constraint>
                <parameter-map>
                    <item-parameter>SJLY</item-parameter>
                    <source-parameter>TGFW</source-parameter>
                </parameter-map>
            </constraint>
        </item>
        <item object-name="xnQTYDYTGSDX_GMTG" reload="false">
            <describe>��ļͶ��</describe>
            <constraint>
                <parameter-map>
                    <item-parameter>SJLY</item-parameter>
                    <source-parameter>GMTG</source-parameter>
                </parameter-map>
            </constraint>
        </item>
    </items>
    <display-options>
        <commands/>
        <relations/>
    </display-options>
    <properties>
        <property>
            <name>63</name>
            <value>true</value>
        </property>
    </properties>
</resource>
