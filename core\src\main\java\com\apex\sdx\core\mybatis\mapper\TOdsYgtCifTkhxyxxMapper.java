package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.api.vo.query.XyzhVo;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhxyxx;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
* <AUTHOR>
* @description 针对表【t_ods_ygt_cif_tkhxyxx】的数据库操作Mapper
* @createDate 2025-02-07 15:10:03
* @Entity com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhxyxx
*/
public interface TOdsYgtCifTkhxyxxMapper extends BaseMapper<TOdsYgtCifTkhxyxx> {

    @Select("SELECT a.xydj, " +
            "       b.djsm, " +
            "       b.sxxs, " +
            "       b.rzed   as htrzed, " +
            "       b.rqed   as htrqed, " +
            "       b.rzsxed as djrzsxed, " +
            "       b.rqsxed as djrqsxed, " +
            "       a.email, " +
            "       a.pjzf, " +
            "       a.ywzh, " +
            "       a.glywzh, " +
            "       c.rzsxed, " +
            "       c.rqsxed, " +
            "       c.sxzed, " +
            "       d.htbh, " +
            "       d.fzllmb, " +
            "       d.lxljfs, " +
            "       d.bz, " +
            "       d.htzt, " +
            "       d.ksrq, " +
            "       d.jsrq, " +
            "       d.htsx, " +
            "       a.khh " +
            "FROM ods.t_ods_ygt_cif_TKHXYXX a " +
            "         left join ods.t_ods_ygt_cif_txy_xydj b " +
            "                   on a.xydj = b.id " +
            "         left join ods.t_ods_ygt_cif_txy_sxxx c " +
            "                   on a.khh = c.khh " +
            "         left join ods.t_ods_ygt_cif_txy_htxx d " +
            "                   on a.khh = d.khh and d.htzt = 0 " +
            "where a.khh = #{khh} ")
    XyzhVo selectXyzhxx(@Param("khh") String khh);
}




