<template>
  <div id="root" class="sdxtc_nav">
    <div class="sdx_bus_con">
      <div v-if="gzlb === 0">
        <div style="margin-top: 16px;">
          <a-input v-model:value="sdxInfo.gzid" placeholder="规则id" type="hidden"/>
          <a-input v-model:value="sdxInfo.gzlb" placeholder="规则类别" type="hidden"/>
          <a-form ref="ruleForm" :labelCol="{ style: 'width: 100px' }" :model="sdxInfo" :rules="rules"
                  layout="inline">
            <a-row>
              <a-form-item label="规则名称" name="gzmc">
                <a-input v-model:value="sdxInfo.gzmc" :disabled="readOnly" class="inputClass" placeholder="规则名称">
                  <template #suffix>
                    <edit-outlined/>
                  </template>
                </a-input>
              </a-form-item>
              <a-form-item label="业务名称" name="cxywlb">
                <a-select v-model:value="sdxInfo.cxywlb" :disabled="readOnly" class="inputClass" placeholder="请选择业务名称">
                  <a-select-option
                      v-for="cxywlb in cxywlbList"
                      :key="cxywlb.ibm"
                      :value="cxywlb.ibm"
                  >{{ cxywlb.ibm }}-{{ cxywlb.note }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="是否启用" name="flag">
                <a-radio-group v-model:value="sdxInfo.flag" :disabled="readOnly">
                  <a-radio value="1">是</a-radio>
                  <a-radio value="0">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-row>
            <a-row>
              <a-form-item label="投资者分类" name="tzzfl">
                <a-select v-model:value="sdxInfo.tzzfl" :disabled="readOnly" class="inputClass" mode="multiple"
                          placeholder="请选择投资者分类" showArrow>
                  <a-select-option value="0">普通投资者</a-select-option>
                  <a-select-option value="1">专业投资者</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="交易所类型" name="jys">
                <a-select v-model:value="sdxInfo.jys" :disabled="readOnly" class="inputClass" mode="multiple"
                          placeholder="请选择交易所类型" showArrow>
                  <a-select-option
                      v-for="jys in jysList"
                      :key="jys.ibm"
                      :value="jys.ibm"
                  >{{ jys.note }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="客户类型" name="syfw">
                <a-select v-model:value="sdxInfo.syfw" :disabled="readOnly" class="inputClass" mode="multiple"
                          placeholder="请选择客户类型" showArrow>
                  <a-select-option
                      v-for="khlb in khlbList"
                      :key="khlb.ibm"
                      :value="khlb.ibm"
                  >{{ khlb.note }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-row>
          </a-form>
        </div>
      </div>
      <div v-if="gzlb !== 0">
        <div class="heard">
          <a-input v-model:value="sdxInfo.gzid" placeholder="规则 id" type="hidden"/>
          <a-form ref="ruleForm" :labelCol="{ style: 'width: 180px' }" :model="sdxInfo" :rules="rules"
                  layout="inline">
            <a-row>
              <a-form-item :label="gzmcLabel" name="gzmc">
                <a-input v-model:value="sdxInfo.gzmc" :disabled="readOnly" class="inputClass" placeholder="规则名称">
                  <template #suffix>
                    <edit-outlined/>
                  </template>
                </a-input>
              </a-form-item>
              <a-form-item v-if="sdxInfo.cpgzjb !== 0" label="产品代码" name="cpgzdmName">
                <a-input v-model:value="sdxInfo.cpgzdmName" allowClear class="inputClass"
                         placeholder="请选择产品代码" @click="dialogCpdmOpen">
                  <template #suffix>
                    <search-outlined/>
                  </template>
                </a-input>
              </a-form-item>
              <a-form-item v-if="sdxInfo.cpgzjb === 0" label="产品类型" name="cpgzdm">
                <a-select v-model:value="sdxInfo.cpgzdm" :disabled="readOnly" allowClear class="inputClass"
                          placeholder="请选择">
                  <a-select-option v-for="item in cplxData" v-show="item.disabled" :key="item.cplxbm"
                                   :value="item.cplxbm">
                    {{ item.cplxbm }}-{{ item.name }}
                  </a-select-option>
                </a-select>
                <a-button :disabled="readOnly" shape="circle" @click="dialogCplxOpen">
                  <template #icon>
                    <search-outlined/>
                  </template>
                </a-button>
              </a-form-item>
            </a-row>
            <a-row>
              <a-form-item label="投资者分类" name="tzzfl">
                <a-select v-model:value="sdxInfo.tzzfl" :disabled="readOnly" class="inputClass" mode="multiple"
                          placeholder="请选择投资者分类" showArrow>
                  <a-select-option value="0">普通投资者</a-select-option>
                  <a-select-option value="1">专业投资者</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="客户类型" name="syfw">
                <a-select v-model:value="sdxInfo.syfw" :disabled="readOnly" class="inputClass" mode="multiple"
                          placeholder="请选择客户类型" showArrow>
                  <a-select-option
                      v-for="khlb in khlbList"
                      :key="khlb.ibm"
                      :value="khlb.ibm"
                  >{{ khlb.note }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="是否启用" name="flag">
                <a-radio-group v-model:value="sdxInfo.flag" :disabled="readOnly">
                  <a-radio value="1">是</a-radio>
                  <a-radio value="0">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-row>
          </a-form>
        </div>
      </div>
      <div>
        <a-modal v-model:visible="cpdmVisible" cancelText="取消" okText="确定" title="产品代码" width="70%"
                 @ok="dialogCpdmClose">
          <a-form layout='inline'>
            <a-form-item>
              <a-input v-model:value="cpdm" class="inputClass" placeholder="请输入产品代码"></a-input>
            </a-form-item>
            <a-form-item>
              <a-input v-model:value="cpmc" class="inputClass" placeholder="请输入产品名称"></a-input>
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="searchClick">查询</a-button>
            </a-form-item>
          </a-form>
          <a-table
              :columns="cpdmColumns"
              :data-source="cpgzdmList"
              :pagination="{position:'bottom',showQuickJumper:true,showSizeChanger:true}"
              :rowKey="(record, index) => { return index }"
              :rowSelection="{ onChange: handleSelectionChangeCpdm,hideDefaultSelections : true,
                type: 'radio' }"
          />
        </a-modal>
      </div>
      <div>
        <a-modal v-model:visible="cplxVisible" cancelText="取消" okText="确定" title="产品类型" width="70%"
                 @ok="cplxVisible = false">
          <a-input v-model:value="filterCplxValue" :disabled="readOnly" placeholder="输入关键字进行过滤"></a-input>
          <a-tree v-model:selectedKeys="cplxSelectKeys" :expandedKeys="cplxExpandedKeys" :treeData="cplxList"
                  autoExpandParent @expand="onCplxExpand" @select="onCplxChange">
          </a-tree>
        </a-modal>
      </div>
    </div>
    <div class="sdx_dep">
      <div class="sdx_dep_right">
        <ul>
          <li>
            <p>参数代码</p>
            <p>
              <a-select v-model:value="curNode.curNode.csdm"
                        :disabled="curNode.curNode.jdgz == 'or' || curFlag || readOnly" allowClear
                        class="inputClass"
                        placeholder="请选择" @change="csdmChange($event)">
                <a-select-option
                    v-for="item in csdmList"
                    v-show="item.CSDM != 'EMPTY_NODE'"
                    :key="item.CSDM"
                    :value="item.CSDM"
                >{{ item.CSDM }}-{{ item.CSMC }}
                </a-select-option>
              </a-select>
            </p>
          </li>
          <li>
            <p>控件类型</p>
            <p>
              <a-select id="kjlx" v-model:value="curNode.curNode.kjlx" class="inputClass" disabled name="kjlx">
                <a-select-option
                    v-for="dict in kjlxList"
                    :key="dict.ibm"
                    :csmc="dict.note"
                    :value="dict.ibm"
                >{{ dict.note }}
                </a-select-option>
              </a-select>
            </p>
          </li>
          <li>
            <p>运算符</p>
            <p>
              <a-select ref="ysfSelect" v-model:value="curNode.curNode.ysf"
                        :disabled="curNode.curNode.jdgz == 'or' || curFlag || readOnly"
                        class="inputClass" @change="ysfChange">
                <a-select-option
                    v-for="dict in ysfList"
                    v-show="(curNode.curNode.kjlx=='3' && dict.ibm=='0')|| (curNode.curNode.kjlx=='1' && ';2;3;'.indexOf(';'+(dict.ibm||'')+';')>=0)|| (curNode.curNode.kjlx=='2' && ';8;9;'.indexOf(';'+(dict.ibm||'')+';')>=0)|| (curNode.curNode.kjlx=='0' && ';2;3;4;5;6;7;'.indexOf(';'+(dict.ibm||'')+';')>=0)"
                    :key="dict.ibm"
                    :csmc="dict.note"
                    :value="dict.ibm"
                >{{ dict.ibm }}-{{ dict.note }}
                </a-select-option>
              </a-select>
            </p>
          </li>
          <li>
            <p>参数值
              <a-button shape="circle" @click="dialogOpen">
                <template #icon>
                  <SearchOutlined/>
                </template>
              </a-button>
            </p>
            <p>
              <a-input v-if="curNode.curNode.kjlx != '1' && curNode.curNode.kjlx != '2'" id="cszId"
                       v-model:value="curNode.curNode.csz"
                       :disabled="curNode.curNode.jdgz == 'or' || curFlag || curNode.curNode.kjlx=='3' || readOnly"
                       class="inputClass" type="text"></a-input>
              <a-select v-if="curNode.curNode.kjlx == '1'" v-model:value="curNode.curNode.csz" allowClear
                        class="inputClass" placeholder="请选择参数值">
                <a-select-option v-for="item in cszsList" :key="item.value" :value="item.value">{{
                    item.title
                  }}
                </a-select-option>
              </a-select>
              <a-tree-select v-if="curNode.curNode.kjlx == '2'" v-model:value="curNode.curNode.cszs"
                             :treeData="cszsList" allowClear placeholder="请选择参数值" style="width: 280px" treeCheckable
                             @change="cszChange">
              </a-tree-select>
            </p>
          </li>
          <li>
            <p>是否支持人工审查</p>
            <p>
              <a-select v-model:value="curNode.curNode.rgsc"
                        :disabled="curNode.curNode.jdgz == 'or' || curFlag || readOnly"
                        class="inputClass">
                <a-select-option csmc="自动检查" value="0">自动检查</a-select-option>
                <a-select-option csmc="人工检查" value="1">人工检查</a-select-option>
              </a-select>
            </p>
          </li>
          <li>
            <a-button :class="{forAdisabledClass: readOnly}" type="primary" @click="removeCurNode">删除节点</a-button>
          </li>
        </ul>
        <a-modal v-model:visible="singleTableVisible" cancelText="取消" okText="确定" title="参数值" @ok="dialogClose">
          <a-input v-model:value="search" placeholder="输入关键字搜索"></a-input>
          <a-table
              :columns="cszColumns"
              :data-source="cszListFilter()"
              :pagination="{position:'bottom',showQuickJumper:true,showSizeChanger:true}"
              :rowKey="record => record.ibm"
              :rowSelection="{ selectedRowKeys: selectedCszSingleRowKeys,onChange: cszSingleHandleCurrentChange,hideDefaultSelections : true,
                type: 'radio' }"
          />
        </a-modal>
        <a-modal v-model:visible="multipleTableVisible" cancelText="取消" okText="确定" title="参数值"
                 @ok="dialogClose">
          <a-input v-model:value="search" placeholder="输入关键字搜索"></a-input>
          <a-table
              :columns="cszsColumns"
              :data-source="cszListFilter()"
              :pagination="{position:'bottom',showQuickJumper:true,showSizeChanger:true}"
              :rowKey="record => record.ibm1 +',' +record.ibm2"
              :rowSelection="{ selectedRowKeys: selectedCszMultipleRowKeys,onChange: multipleHandleSelectionChange }"
          />
        </a-modal>
      </div>
      <div class="sdx_dep_left">
        <div class="sdx_dep_btn">
          <a-space size="large">
            <a-button :class="{forAdisabledClass: readOnly}" type="primary" @click="addAndNode">子节点_与</a-button>
            <a-button :class="{forAdisabledClass: readOnly}" type="primary" @click="addOrNode">子节点_或</a-button>
            <a-button :class="{forAdisabledClass: readOnly}" type="primary" @click="addFzNode">子节点_分支</a-button>
          </a-space>
        </div>
        <div class="sdx_dep_content" @click="removeClassCur">
          <div class="zxtj_tc">
            <table id="id_condition_table" :key="keyData" border="0" cellpadding="0" cellspacing="0" width="100%">
              <tbody>
              <tr>
                <td :class="{'hide': sdxNodes.length<2}" class="parentLine" rowspan="3" width="1">
                  <div class="zxtj_left_bor"><a class="tj_gx big and">且</a></div>
                </td>
              </tr>
              <tr v-for="f_node in sdxNodes" :key="f_node.jdbh">
                <td valign="middle" width="20"><em class="parentSmallLine line"></em></td>
                <!-- 与节点 -->
                <td v-if="f_node.jdgz != 'or'" class="condition_td">
                  <div class="delete_tj" @click="removeNode(f_node)"></div>
                  <div class="tj_dl_model">
                    <div class="tj_model_con">
                      <div class="second_tj_con_li">
                        <ul class="second_ul">
                          <li class="second_li">
                            <em class="childSmallLine line hide"></em>
                            <div :class="{'cur': f_node.cur}" :jdbh="f_node.jdbh" class="jd_con"
                                 @click="addClassCur(f_node)">{{ !f_node.csmc ? '参数名称(无)' : f_node.csmc }}
                            </div>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </td>
                <!-- 或节点 -->
                <td v-if="f_node.jdgz == 'or'" class="condition_td">
                  <div class="delete_tj" @click="removeNode(f_node)"></div>
                  <div class="tj_dl_model">
                    <div class="tj_model_con" style="margin-left: 25px;">
                      <div class="second_tj_gx childLine">
                        <div class="tj_box" style="border-right: 0;height: 100px;">
                          <div :class="{'cur': f_node.cur}" :jdbh="f_node.jdbh" class="xnjd_con"
                               style="border: 2px dashed #aaa;" @click="addClassCur(f_node)">
                            {{ !f_node.csmc ? '虚拟节点' : f_node.csmc }}
                          </div>
                          <a class="tj_gx and" style="float: right;margin-right: 0;margin-top: -28px;">或</a>
                        </div>
                      </div>
                      <div class="second_tj_con_li" style="margin-left: 140px;">
                        <div class="xnline" style="height: 94px;"></div>
                        <ul class="second_ul">
                          <li v-for="s_node in f_node.childNodes" :key="s_node.jdbh" class="second_li">
                            <div class="third_tj_con_li">
                              <ul class="second_ul">
                                <li v-for="t_node in s_node.childNodes" v-show="!!s_node.childNodes" :key="t_node.jdbh"
                                    class="second_li">
                                  <em class="childSmallLine line"></em>
                                  <div :class="{'cur': t_node.cur}" :jdbh="t_node.jdbh" class="jd_con jd_click"
                                       @click="addClassCur(t_node)">{{ !t_node.csmc ? '参数名称(无)' : t_node.csmc }}
                                  </div>
                                </li>
                                <li v-if="!s_node.childNodes" class="second_li">
                                  <em class="childSmallLine line"></em>
                                  <div :class="{'cur': s_node.cur}" :jdbh="s_node.jdbh" class="jd_con jd_click"
                                       @click="addClassCur(s_node)">{{ !s_node.csmc ? '参数名称(无)' : s_node.csmc }}
                                  </div>
                                </li>
                              </ul>
                            </div>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="tc_btn">
      <a-button :class="{forAdisabledClass: readOnly}" type="primary" @click="saveSdxInfo">确认</a-button>
    </div>
  </div>

</template>

<script setup>
import '../../../assets/css/sdxConfig.css';
import $ from 'jquery';
import sdxConfig from "@/api/query/sdxConfig";
import {computed, getCurrentInstance, nextTick, onMounted, reactive, ref, watch} from 'vue';
import {EditOutlined, SearchOutlined} from '@ant-design/icons-vue';
import {message} from 'ant-design-vue';
import {useRoute} from "vue-router";
import _ from 'lodash';

EditOutlined;
SearchOutlined;//防止自动格式化代码被移除

let {proxy} = getCurrentInstance()
const route = useRoute();
let gzlb = ref(""); //gzlb='0'为true; 其余为false
let readOnly = ref(false); //只读标志
let copyFlag = ref(false); //复制标志
const cplxVisible = ref(false);
let cpdmVisible = ref(false);
let multipleTableVisible = ref(false);
let singleTableVisible = ref(false);
const cpdm = ref("");
const cpmc = ref("");
let filterCplxValue = ref("");
let curFlag = ref(true);  //选中节点标志
let xnjdbh = ref("0"); // 虚拟节点编号，用于新增节点
let keyData = ref("0");// 用于节点过深时，页面没发及时更新数据
const search = ref("");
let fqr = ref("");
let czzd = ref("");
let ysfList = reactive([{ibm: "1", note: "运算符1"}]); //ZRGZ_YSF2  运算符
let kjlxList = reactive([]); //ZRGZ_KJLX 控件类型
let cxywlbList = reactive([{ibm: "1", note: "业务名称1"}]); //创新业务类别列表
let jysList = reactive([]); //交易所类型列表
let cpdmList = reactive([{cpmc: "产品代码1", cpdm: "1"}, {cpmc: "产品代码2", cpdm: "2"}]); //产品代码列表
let khlbList = reactive([{ibm: "1", note: "客户类型1"}]); //客户类型列表
let cplxSelectKeys = ref([]); //产品类型树 选中节点
let cplxExpandedKeys = ref([]); //产品类型树 选中父节点
let cplxList = reactive([{fid: "-1", id: "0", cplxbm: "0", name: "顶层节点", grade: "1"}, {
  fid: "0",
  id: "1",
  cplxbm: "1",
  name: "子节点1",
  grade: "2"
}, {fid: "0", id: "2", cplxbm: "2", name: "子节点2", grade: "2"}
  , {fid: "1", id: "3", cplxbm: "3", name: "子节点1-1", grade: "3"}
  , {fid: "2", id: "4", cplxbm: "4", name: "子节点2-1", grade: "3"}]);
let headMap = reactive({ibm1: '问卷编码', ibm2: '测评等级', note1: '问卷名称', note2: '测评等级名称'});
let cplxData = reactive([{fid: "-1", id: "0", cplxbm: "0", name: "顶层节点", grade: "0"}, {
  fid: "0",
  id: "1",
  cplxbm: "1",
  name: "子节点1",
  grade: "1"
}, {fid: "0", id: "2", cplxbm: "2", name: "子节点2", grade: "2"}, {
  fid: "1",
  id: "3",
  cplxbm: "3",
  name: "子节点1-1",
  grade: "3"
}
  , {fid: "2", id: "4", cplxbm: "4", name: "子节点2-1", grade: "3"}]);
let cpgzdmList = reactive([]);
let cszCurrentRow = reactive([]);
const cszColumns = reactive([
  {
    title: '字典值',
    dataIndex: 'ibm',
  },
  {
    title: '字典说明',
    dataIndex: 'note',
  }]);
let cszsColumns = reactive([
  {
    title: '字典值1',
    dataIndex: 'ibm1',
  },
  {
    title: '字典值2',
    dataIndex: 'ibm2',
  },
  {
    title: '字典说明1',
    dataIndex: 'note1',
  },
  {
    title: '字典说明2',
    dataIndex: 'note2',
  }]);
const cpdmColumns = reactive([
  {
    title: '产品代码',
    dataIndex: 'cpdm',
  },
  {
    title: '产品名称',
    dataIndex: 'cpmc',
  }]);
let selectedCszSingleRowKeys = ref([]);
let selectedCszMultipleRowKeys = ref([]);
let cszList = reactive([{ibm: "2001126", ibm1: "2001", ibm2: "126", note: "福州", note1: "福建", note2: "福州"},
  {ibm: "2001127", ibm1: "2001", ibm2: "127", note: "厦门", note1: "福建", note2: "厦门"},
  /* {ibm: "00", ibm1: "", ibm2: "", note: "福建福州", note1: "", note2: ""},
   {ibm: "01", ibm1: "", ibm2: "", note: "福建厦门", note1: "", note2: ""}*/]);
let cszsList = reactive([{
  title: "福建", value: "2001", key: "2001", children:
      [{title: "福州", value: "2001,126", key: "126", children: []},
        {title: "厦门", value: "2001,127", key: "127", children: []}]
},
]);
let cszEnd = reactive([]);
let structureJson = reactive({
  childNodes: [],
  jdbh: 0,
  csdm: ""
});
let sdxRule = reactive({
  frlx: "",
  jys: "",
  flag: "",
  cxywlb: "",
  tzzfl: "",
  syfw: "",
  gzmc: "",
  cpgzdm: "",
  code: 0,
  note: "",
  gztj: {}
});
const gzid = ref("");
const sdxInfo = reactive({
  frlx: sdxRule.frlx || "",
  jys: sdxRule.jys ? sdxRule.jys.split(";") : undefined,
  flag: sdxRule.flag || "1",
  cxywlb: sdxRule.cxywlb,
  tzzfl: sdxRule.tzzfl ? sdxRule.tzzfl.split(";") : undefined,
  syfw: sdxRule.syfw ? sdxRule.syfw.split(";") : undefined,
  gzmc: !!sdxRule.gzmc || "",
  gzid: !!gzid.value || "AddNewRule",
  gzlb: gzlb.value,
  cpgzjb: 0,// 区分是1|产品代码的规则，还是0|产品类型的规则 cpgzjb='0'为true; cpgzjb='1'为false
  cpgzdm: !!sdxRule.cpgzdm || "",
  cpgzdmName: '',
});
let sdxNodes = reactive([]);
let curNode = reactive({
  curNode: {
    csdm: "",
    kjlx: "",
    gllx: "",
    gldx: "",
    csmc: "",
    csz: "",
    cszs: [],
    ysf: "",
    rgsc: "", ysfmc: "",
    jdbh: "",
    cur: false,
    jdgz: "",
    childNodes: []
  }
});
let cpdmCurrentRow = reactive([{cpmc: "", cpdm: ""}]);
let multipleSelection = reactive([]);
let csdmList = reactive([{CSMC: '客户证明文件类型查询', GLDX: 'SDX_ZMWJLX', GLLX: '0', KJLX: '1', CSDM: '1'}, {
  CSMC: '问卷测评等级查询',
  GLDX: 'VKHDCWJCPDJ',
  GLLX: '1',
  KJLX: '2',
  CSDM: '2'
}, {CSMC: '资产账户是否是主账户', GLDX: '', GLLX: '', KJLX: '3', CSDM: '3'}]);//zzsccsdmList
const rules = reactive({
  cxywlb: [{required: true, message: '请选择业务名称'}],
  flag: [{required: true, message: '请选择是否启用'}],
  tzzfl: [{required: true, message: '请选择投资者分类'}],
  gzmc: [{required: true, message: '请输入规则名称'}],
  cpgzdm: [{required: true, message: '请选择产品类型'}],
  cpgzdmName: [{required: true, message: '请选择产品代码'}],
  syfw: [{required: true, message: '请选择客户类型'}]
});

const initParentLineHeight = function () {
  $("#id_condition_table tr").eq(0).find("td").eq(0).attr("rowspan", $("#id_condition_table tr").length)
  if ($("#id_condition_table tr").length > 2) {
    $(".parentLine").removeClass("hide");
    $(".parentSmallLine").removeClass("hide");
  } else {
    $(".parentLine").addClass("hide");
    $(".parentSmallLine").addClass("hide");
  }
  var height = 0;
  var len = $(".condition_td").length;
  $(".condition_td").each(function (index) {//重新计算虚线的高度  所有td减去第一个高度和最后一个高度的二分之一
    if ((index == 0 || index == len - 1)) {
      height += $(this).height() / 2
    } else {
      height += $(this).height();
    }
    if (index == 0) {
      $(".parentLine").css("padding-top", $(this).height() / 2 + "px");
    } else if (index == len - 1) {
      $(".parentLine").css("padding-bottom", $(this).height() / 2 + "px");
    }
  });
  $(".zxtj_left_bor").height(height);
}
const buildData = function (data) {
  let warnMsg = [];
  data.forEach((item) => {
    if (!!item.childNodes && item.childNodes.length > 0) {
      buildData(item.childNodes);
    } else {
      if (!!item.cszs && item.cszs.length > 0 && !item.csz) {
        item.csz = item.cszs.join(";");
      }
      if (!!item.gllx && !!item.gldx) {
        item.ckzd = item.gllx + ":" + item.gldx;
      }
      //	item.ysfmc = $refs.ysfSelect.selected.label;
      item.gzlb = gzlb.value; // 后端需要这个参数，正常一个就好了，没必要每个节点都送
      if (item.kjlx != 3 && !item.csz) {
        if (item.csmc) {
          warnMsg.push("[" + item.csmc + "]节点参数值不能为空")
        } else {
          warnMsg.push("存在参数值为空的节点")
        }

      }
    }
  });
  return warnMsg;
};
const ruleForm = ref();
const saveSdxInfo = function () {
  ruleForm.value.validate().then(function () {
    let warnMsg = buildData(sdxNodes)
    if (!!warnMsg && warnMsg.length > 0) {
      message.warning(warnMsg.join(";"));
      return;
    }
    let sdxData = {
      czzd: "", fqqd: "", fqr: "",
      ...sdxInfo
    };
    sdxData.czzd = czzd.value;
    sdxData.fqqd = '1';
    sdxData.fqr = fqr.value;
    sdxData.tzzfl = !sdxData.tzzfl ? "" : sdxData.tzzfl.join(";");
    sdxData.syfw = !sdxData.syfw ? "" : sdxData.syfw.join(";");
    sdxData.jys = !sdxData.jys ? "" : sdxData.jys.join(";");

    sdxConfig.saveSdxConfig({
      ...sdxData,
      gztj: JSON.stringify({
        "jdbh": structureJson.jdbh || 0,
        "csdm": structureJson.csdm || "EMPTY_NODE",
        "childNodes": sdxNodes
      })
    }).then((retData) => {
      if (retData.code > 0) {
        let data = retData.records;
        sdxInfo.gzid = data[0].gzid;
        message.success(retData.note);
      } else {
        message.error(retData.note);
      }
    });
  });
};
const csdmChange = function (flag) {
  if (!curNode.curNode.csdm || 'EMPTY_NODE' == curNode.curNode.csdm) {
    return;
  }
  csdmList.forEach((item) => {
    if (curNode.curNode.csdm == item.CSDM) {
      curNode.curNode.kjlx = item.KJLX || '0';
      curNode.curNode.gllx = item.GLLX;
      curNode.curNode.gldx = item.GLDX;
      curNode.curNode.csmc = !item.CSMC ? "" : item.CSMC.split("-")[1];
    }
  });
  if (!flag) {
    curNode.curNode.csz = "";
    curNode.curNode.cszs = [];
    curNode.curNode.ysf = "";
  }
  if (!curNode.curNode.rgsc) {
    curNode.curNode.rgsc = "0";
  }
  if ((curNode.curNode.kjlx != 1 && curNode.curNode.kjlx != 2)
      || (curNode.curNode.gllx != 0 && curNode.curNode.gllx != 1)
      || !curNode.curNode.gldx) {

    return;
  }
  if (!curNode.curNode.cszs && !!curNode.curNode.csz) {
    curNode.curNode.cszs = [];
    let cszArr = curNode.curNode.csz.split(";");
    cszArr.forEach((csz) => {
      if (curNode.curNode.kjlx == '2') {
        curNode.curNode.cszs.push(csz.split(","));
      } else if (curNode.curNode.kjlx == '1') {
        curNode.curNode.cszs.push(csz);
      }
    });
  }

  cszsColumns = [
    {
      title: headMap.ibm1,
      dataIndex: 'ibm1',
    },
    {
      title: headMap.ibm2,
      dataIndex: 'ibm2',
    },
    {
      title: headMap.note1,
      dataIndex: 'note1',
    },
    {
      title: headMap.note2,
      dataIndex: 'note2',
    }];
  sdxConfig.findCsz(curNode.curNode.gllx, curNode.curNode.gldx).then((retData) => {
    if (retData.code > 0) {
      cszList = retData.records.cszList;
      cszsList = retData.records.cszsList;
      headMap = retData.records.headMap;
    } else {
      message.error(retData.note);
    }
  });

  keyData.value += 1;// 更新
  setParentLineHeight();
};
const ysfChange = function (ysf) {
  ysfList.forEach((item) => {
    if (item.ibm == ysf) {
      curNode.curNode.ysfmc = item.ibm + '-' + item.note;
      return false;
    }
  });
};
// 删除节点
const removeNode = function (node) {
  sdxNodes.forEach((nodeTmp, i) => {
    if (nodeTmp.jdbh == node.jdbh) {
      sdxNodes.splice(i, 1); // 从下标 i 开始, 删除 1 个元素
    }
  });
  curNode.curNode = {
    childNodes: [],
    csdm: "",
    csmc: "",
    csz: "",
    cszs: [],
    cur: false,
    gldx: "",
    gllx: "",
    jdbh: "",
    jdgz: "",
    kjlx: "",
    rgsc: "",
    ysf: "",
    ysfmc: ""
  };
  keyData.value += 1;
  setParentLineHeight();
};
// 删除选中节点
const removeCurNode = function () {
  let pnode = getCurParentNodes(sdxNodes, null);
  if (!pnode) {
    removeNode(curNode.curNode);
  } else if ((pnode.jdgz == 'or' && pnode.childNodes.length > 2)
      || (pnode.jdgz != 'or' && pnode.childNodes.length > 1)) {
    pnode.childNodes.forEach((nodeTmp, i) => {
      if (nodeTmp.jdbh == curNode.curNode.jdbh) {
        pnode.childNodes.splice(i, 1); // 从下标 i 开始, 删除 1 个元素
      }
    });
    curNode.curNode = pnode.childNodes[0];
    curNode.curNode.cur = true;
    setXnLineHeight();
  }
};
// 创建分支
const addFzNode = function () {
  // 新增子节点
  if (addChildNode()) {
    setXnLineHeight();
    return;
  }
  // 否则在一级节点增加与节点
  sdxNodes.push({
    //"csdm_tjmc": "节点名称",
    "jdbh": nextJdbh()
  })
  setParentLineHeight();
};
// 创建或节点
const addOrNode = function () {
  // 新增子节点
  if (addChildNode()) {
    setXnLineHeight();
    return;
  }
  // 否则在一级节点增加或节点
  sdxNodes.push({
    "csdm": "EMPTY_NODE",
    "jdgz": "or",
    "childNodes": [{
      //"csdm_tjmc": "节点名称",
      "jdbh": nextJdbh()
    },
      {
        //"csdm_tjmc": "节点名称",
        "jdbh": nextJdbh()
      }
    ]
  })
  setParentLineHeight();
};
// 添加叶子节点
const addChildNode = function () {
  // 如果当前节点是虚拟节点，在虚拟节点上创建或节点
  if (curNode.curNode.jdgz == 'or') {
    curNode.curNode.childNodes.push({
      //"csdm_tjmc": "节点名称",
      "jdbh": nextJdbh()
    })
    return true;
  }
  // 如果父节点有值，在虚拟节点上增加或节点
  let pnode = getCurParentNodes(sdxNodes, null);
  if (pnode) {
    if (pnode.jdgz == 'or') {
      pnode.childNodes.push({
        //"csdm_tjmc": "节点名称",
        "jdbh": nextJdbh()
      })
      return true;
    }
    let ppnode = getParentNodes(sdxNodes, pnode, null);
    if (!!ppnode && ppnode.jdgz == 'or') {
      ppnode.childNodes.push({
        //"csdm_tjmc": "节点名称",
        "jdbh": nextJdbh()
      })
      return true;
    }
  }
};
// 创建与节点
const addAndNode = function () {
  // 当前节点是虚拟节点时，不新增节点
  if (curNode.curNode.jdgz == 'or') {
    return;
  }
  // 父节点
  let pnode = getCurParentNodes(sdxNodes, null);
  // 如果没有父节点，为一级节点，直接新增一级节点
  if (!pnode) {
    sdxNodes.push({
      //"csdm_tjmc": '节点名称',
      "jdbh": nextJdbh()
    });
    setParentLineHeight();
  } else {
    // 有父节点，且是and节点
    if (pnode.jdgz == 'and') {
      pnode.childNodes.push({
        //"csdm_tjmc": '节点名称',
        "jdbh": nextJdbh()
      })
      keyData.value += 1;
      // this.$set(pnode.childNodes, 'childNodes', pnode.childNodes);
    } else if (pnode.jdgz == 'or') {
      // 父节点是或节点
      if (!curNode.curNode.childNodes) {
        // 当前节点改为虚拟几点，节点规则and
        let newNode = extend(curNode.curNode, {});
        curNode.curNode.childNodes = [newNode, {
          //"csdm_tjmc": '节点名称',
          "jdbh": nextJdbh()
        }];
        curNode.curNode.cur = false;
        curNode.curNode.csdm = "EMPTY_NODE";
        curNode.curNode.jdgz = "and";
        curNode.curNode.jdbh = nextJdbh();
        curNode.curNode = newNode;
      } else {
        curNode.curNode.childNodes.push({
          //"csdm_tjmc": '节点名称',
          "jdbh": nextJdbh()
        })
      }
    } else {
      pnode.childNodes.push({
        //"csdm_tjmc": '节点名称',
        "jdbh": nextJdbh()
      })
      keyData.value += 1;
    }
    setXnLineHeight();
  }
};
// 取消选中节点
const removeClassCur = function () {
  curNode.curNode = {
    childNodes: [],
    csdm: "",
    csmc: "",
    csz: "",
    cszs: [],
    cur: false,
    gldx: "",
    gllx: "",
    jdbh: "",
    jdgz: "",
    kjlx: "",
    rgsc: "",
    ysf: "",
    ysfmc: ""
  };
  removeSdxCur(sdxNodes)
  curFlag.value = true;
};
// 选中节点 增加选中样式
const addClassCur = function (node) {
      if (!!curNode.curNode.csdm && curNode.curNode.csdm == node.csdm) {
        return;
      }
      curNode.curNode = node;
      removeSdxCur(sdxNodes);
      node['cur'] = true;// this.$set(node, 'cur', true);
      event.stopPropagation();
      csdmChange(true);
      curFlag.value = false;
    }
// 修改json对象cur的值
;
const removeSdxCur = function (childNodes) {
  for (let i = 0; i < childNodes.length; i++) {
    let node = childNodes[i];
    if (node.cur) {
      node['cur'] = false;// this.$set(node, 'cur', false);
      break;
    }
    if (node.childNodes) {
      removeSdxCur(node.childNodes);
    }
  }
};
// 下个虚拟节点

const nextJdbh = function () {
  return "xn_" + xnjdbh.value++;
};
// 获取选中节点的父节点
const getCurParentNodes = function (childNodes, pnode) {
  return getParentNodes(childNodes, curNode.curNode, pnode)
};
// 获取父节点
const getParentNodes = function (childNodes, cnode, pnode) {
  // 获取cnode节点的父节点
  for (let i = 0; i < childNodes.length; i++) {
    let node = childNodes[i];
    if (node.jdbh == cnode.jdbh) {
      return pnode;
    }
    if (node.childNodes) {
      let nodesTmp = getParentNodes(node.childNodes, cnode, node);
      if (nodesTmp) {
        return nodesTmp;
      }
    }
  }
};
// 获取叶子节点数量
const getChildLength = function (childNodes) {
  let length = childNodes.length;
  for (let i = 0; i < childNodes.length; i++) {
    let node = childNodes[i];
    if (node.childNodes) {
      length = length + getChildLength(node.childNodes) - 1;
    }
  }
  return length;
};
// 获取虚拟节点线正确的高度
const getXnLineHeight = function (node) {
  let height = 0;
  // 当前节点是虚拟节点时
  node = !node ? curNode.curNode : node;
  if (node.jdgz == 'or') {
    height = getChildLength(node.childNodes) * 40
        + node.childNodes.length * 30
        - (node.childNodes.length - 1) * 8
        - 40;
  } else {
    let pnode = getParentNodes(sdxNodes, node, null);
    if (!pnode) {
      return;
    }
    height = getXnLineHeight(pnode)
  }
  return height;
};
// 设置虚拟记得高度
const setXnLineHeight = function () {
  proxy.$nextTick(function () {
    let height = getXnLineHeight(null);
    if (!height) {
      return;
    }
    let obj = $("div[jdbh='" + curNode.curNode.jdbh + "']").parents(".tj_model_con");
    let xnline = obj.find(".xnline");
    if (xnline.length > 0) {
      xnline.height(height);
    }
    setParentLineHeight();
  })
};
// 设置顶级节点高度，需要等页面渲染完再设置
const setParentLineHeight = function () {
  nextTick(function () {
    initParentLineHeight();
  })
};
// 深度拷贝
const extend = function (source, target) {
  // 拷贝对象，防止修改了原始obj
  let newObj = Object.assign({}, source);
  for (let key in target) {
    if ("object" != typeof source[key] || null === source[key] || Array.isArray(source[key])) {
      if (void 0 !== target[key]) {
        newObj[key] = target[key];
      }
    } else {
      newObj[key] = extend(source[key], target[key]);
    }
  }
  return newObj
};
const cszListFilter = function () {
  if (!cszList || cszList.length == 0) {
    return;
  }
  return cszList.filter(data => {
    let flag = false;
    if (search.value) {
      $.each(data, function (name, value) {
        flag = flag || value.toLowerCase().includes(search.value.toLowerCase())
      });
    }
    return !search.value || flag;
  })
};
const cszSingleHandleCurrentChange = function (selectedRowKeys, selectedRows) {
  cszCurrentRow = selectedRows;
  selectedCszSingleRowKeys.value = selectedRowKeys;
};
const multipleHandleSelectionChange = function (selectedRowKeys, selectedRows) {
  multipleSelection = selectedRows;
  selectedCszMultipleRowKeys.value = selectedRowKeys;
};
const cszChange = function (val) {
  // 是否与上次的类型相同
  let changeFlag = false;
  let changeItems = [];

  if (cszEnd.length == 0) {
    cszEnd = val;
  } else {
    if (cszEnd[0][1]) {//判断是否为多级节点
      curNode.curNode.cszs.forEach((item) => {
        if (item[0].split(",")[0] !== cszEnd[0].split(",")[0]) { // 一级标签不同
          changeFlag = true;
          changeItems.push(item);
        }
      });
    }
  }
  if (changeFlag) {
    curNode.curNode.cszs = changeItems;
  }

  cszEnd = curNode.curNode.cszs;
  if (curNode.curNode.cszs) {
    curNode.curNode.csz = curNode.curNode.cszs.join(";");
  }
};
const dialogOpen = function () {
  if (curNode.curNode.kjlx == '1') {
    singleTableVisible.value = true;
    if (curNode.curNode.csz) {
      selectedCszSingleRowKeys.value.push(curNode.curNode.csz);//根据已选择项，选中对应条目
    }
  } else if (curNode.curNode.kjlx == '2') {
    multipleTableVisible.value = true;
    selectedCszMultipleRowKeys.value = [];//打开前清空原有选择
    if (curNode.curNode.cszs) {
      for (let i = 0; i < curNode.curNode.cszs.length; i++) {
        selectedCszMultipleRowKeys.value.push(curNode.curNode.cszs[i]) //根据已选择项，选中对应条目
      }
    }
  }
};
const dialogCplxOpen = function () {
  cplxVisible.value = true;
  let key = '';
  try {
    cplxData.forEach((cplx) => {
      if (cplx.cplxbm == sdxInfo.cpgzdm) {
        key = cplx.cplxbm;
        throw new Error('End Loop')  //直接结束循环
      }
    });
  } catch (e) {
    if (e.message !== 'End Loop') throw e //非正常结束循环 抛出异常
  }
  cplxSelectKeys.value[0] = key;
  cplxExpandedKeys.value = [];//每次打开进行初始化
  getCplxExpanded(key);
};
//获取该节点的所有父节点
const getCplxExpanded = function (key) {
  try {
    cplxData.forEach((cplx) => {
      if (cplx.cplxbm == key) {
        let parentkey = cplx.fid;
        if (parentkey == -1) {
          throw new Error('End Loop')  //直接结束循环
        } else {
          cplxExpandedKeys.value.push(parentkey);
          getCplxExpanded(parentkey)
        }
      }
    });
  } catch (e) {
    if (e.message !== 'End Loop') throw e //非正常结束循环 抛出异常
  }
};
const splicing = function (ibm, split) {
  return ibm ? split + ibm : "";
};
const dialogClose = function () {
  if (curNode.curNode.kjlx == '1') {
    singleTableVisible.value = false;
    curNode.curNode.csz = cszCurrentRow[0].ibm
  } else if (curNode.curNode.kjlx == '2') {
    multipleTableVisible.value = false;
    curNode.curNode.cszs = [];
    multipleSelection.forEach((x) => {
      let csz = x.ibm1 + splicing(x.ibm2, ",");
      curNode.curNode.cszs.push(csz)
    });
  }
};
//jsontotree
const getListData = function () {
  let dataArray = [];
  cplxList.forEach(function (data) {
    let parentId = data.fid;
    if (parentId == -1) {
      let objTemp = {
        id: data.id,
        title: data.cplxbm + '-' + data.name,
        order: data.grade,
        parentId: parentId,
        key: data.cplxbm,
        disabled: true, //顶级父节点不可选
      }
      dataArray.push(objTemp);
    }
  });
  data2treeDG(cplxList, dataArray);
};
const data2treeDG = function (datas, dataArray) {
  for (let j = 0; j < dataArray.length; j++) {
    let dataArrayIndex = dataArray[j];
    let childrenArray = [];
    let Id = dataArrayIndex.id;
    for (let i = 0; i < datas.length; i++) {
      let data = datas[i];
      let parentId = data.fid;
      if (parentId == Id) {//判断是否为子节点
        let objTemp = {
          id: data.id,
          title: data.cplxbm + '-' + data.name,
          order: data.grade,
          parentId: parentId,
          key: data.cplxbm,
        }
        childrenArray.push(objTemp);
      }
    }
    dataArrayIndex.children = childrenArray;
    if (childrenArray.length > 0) {//有子节点则递归
      //	dataArrayIndex.disabled = true; //存在子节点，当前叶子节点不可选
      data2treeDG(datas, childrenArray)
    }
  }
  cplxList = dataArray;//TODO
  return dataArray;
};

//对下拉框数据初始化
const dataAddDisabled = function () {
  cplxData.forEach((item, i) => {
    if (item.fid == -1) {
      cplxData.splice(i, 1)
    }
  });
};

//选择节点
const onCplxChange = function (selectedKeys) {
  if (selectedKeys.length != 0) {
    sdxInfo.cpgzdm = selectedKeys[0]
  } else {
    sdxInfo.cpgzdm = "";
  }
};

const onCplxExpand = (keys, e) => {
  const tempKeys = ((e.node.parent ? e.node.parent.children : cplxList) || []).map(
      ({key}) => key,
  );
  if (e.expanded) {
    cplxExpandedKeys.value = _.difference(keys, tempKeys).concat(e.node.key);
  } else {
    cplxExpandedKeys.value = keys;
  }
};
const initPage = function () {
  if (copyFlag.value) {
    sdxInfo.frlx = '';
    sdxInfo.flag = '1';
    sdxInfo.cxywlb = '';
    sdxInfo.cpgzdm = '';
    sdxInfo.gzid = 'AddNewRule';
    sdxInfo.jys = '';

  }
  if (cpdmList) {
    cpdmList.forEach((item) => {
      if (item.cpdm == sdxInfo.cpgzdm) {
        sdxInfo.cpgzdmName = item.cpdm + '-' + item.cpmc;
      }
    });
  }
};
const dialogCpdmOpen = function () {
  cpdmVisible.value = true;
};
const dialogCpdmClose = function () {
  if (JSON.stringify(cpdmCurrentRow[0]) != '{}') {
    sdxInfo.cpgzdm = cpdmCurrentRow[0].cpdm;
    sdxInfo.cpgzdmName = cpdmCurrentRow[0].cpdm + '-' + cpdmCurrentRow[0].cpmc;

  }
  proxy.$refs.ruleForm.validateField('cpgzdmName');
  cpdmVisible.value = false;
};
const handleSelectionChangeCpdm = function (selectedRowKeys, selectedRows) {
  cpdmCurrentRow = selectedRows;
};
const searchClick = function () {
  findCpxxPage();
};
const findCpxxPage = function () {
  sdxConfig.queryCpxx(cpdm, cpmc).then((retData) => {
    if (retData.code > 0) {
      cpgzdmList = retData.records
    } else {
      message.error(retData.note || retData.message)
    }
  });
};

const initData = function () {
  gzid.value = route.query.gzid;
  gzlb.value = route.query.gzlb;
  sdxInfo.cpgzjb = route.query.cpgzjb;
  readOnly.value = !!route.query.readOnly || false;
  copyFlag.value = !!route.query.copyFlag || false;
  fqr.value = proxy.$store.getters.id || route.query.fqr;
  czzd.value = proxy.$store.getters.id || route.query.czzd;
  console.log("发起人：" + fqr.value)
  console.log("操作站点：" + czzd.value)
  Promise.resolve().then(function () {
    return new Promise(function (resolve, reject) {
      if(gzid.value){
        sdxConfig.querySdxConfig(gzid, gzlb, sdxInfo.cpgzjb).then((retData) => {
          if (retData.code > 0) {
            sdxRule = retData.records
            resolve();
          } else {
            reject(retData.note || retData.message)
          }
        });
      }else{
        resolve();
      }
    })
  }).then(function () {
    return new Promise(function (resolve, reject) {
      sdxConfig.queryXtdm("SDX_ZRGZ_KJLX").then((retData) => {
        if (retData.code > 0) {
          kjlxList = retData.records
          resolve();
        } else {
          reject(retData.note || retData.message)
        }
      });
    })
  }).then(function () {
    return new Promise(function (resolve, reject) {
      sdxConfig.queryXtdm("SDX_ZRGZ_YSF").then((retData) => {
        if (retData.code > 0) {
          ysfList = retData.records
          resolve();
        } else {
          reject(retData.note || retData.message)
        }
      });
    })
  }).then(function () {
    return new Promise(function (resolve, reject) {
      sdxConfig.queryXtdm("SDX_KHLB").then((retData) => {
        if (retData.code > 0) {
          khlbList = retData.records
          resolve();
        } else {
          reject(retData.note || retData.message)
        }
      });
    })
  }).then(function () {
    return new Promise(function (resolve, reject) {
      sdxConfig.queryZrcsdm("").then((retData) => {
        if (retData.code > 0) {
          csdmList = retData.records
          resolve();
        } else {
          reject(retData.note || retData.message)
        }
      });
    })
  }).then(function () {
    return new Promise(function (resolve, reject) {
      if (0 == gzlb.value) { //业务规则独有
        sdxConfig.queryXtdm("SDX_JYS").then((retData) => {
          if (retData.code > 0) {
            jysList = retData.records
            resolve();
          } else {
            reject(retData.note || retData.message)
          }
        });
      } else {
        resolve();
      }
    })
  }).then(function () {
    return new Promise(function (resolve, reject) {
      if (0 == gzlb.value) { //业务规则独有
        sdxConfig.queryXtdm("SDX_CXYWLB").then((retData) => {
          if (retData.code > 0) {
            cxywlbList = retData.records
            resolve();
          } else {
            reject(retData.note || retData.message)
          }
        });
      } else {
        resolve();
      }
    })
  }).then(function () {
    return new Promise(function (resolve, reject) {
      if (0 == sdxInfo.cpgzjb && 1 == gzlb.value) { //0 产品类型
        sdxConfig.queryCplx("").then((retData) => {
          if (retData.code > 0) {
            cplxList = retData.records
            cplxData = retData.records
            resolve();
          } else {
            reject(retData.note || retData.message)
          }
        });
      } else if (0 != sdxInfo.cpgzjb && 1 == gzlb.value) { // 1 产品代码
        sdxConfig.queryCpxx("", "").then((retData) => {
          if (retData.code > 0) {
            cpdmList = retData.records
            resolve();
          } else {
            reject(retData.note || retData.message)
          }
        });
      }
    })
  }).catch(function (err) {
    message.error(err);
  })

  if (JSON.stringify(sdxRule) != '{}' && sdxRule.code < 0) {
    message.error("适当性规则获取异常：" + sdxRule.note);
  }
  structureJson = sdxRule.gztj || {};
  if (typeof structureJson === 'string') {
    try {
      structureJson = JSON.parse(structureJson)
    } catch (e) {
      console.log(e);
    }
  }
  let childNodes = reactive(!structureJson ? [] : (structureJson.childNodes || []));

  // 标记默认或节点
  for (let i = 0; i < sdxNodes.length; i++) {
    let node = childNodes[i];
    if (!node.csz) {//csz为空，直接设置kjlx的值，防止初始化之后，当kjlx为3并且没有修改控件时，直接点击确定保存，会在buildData函数中判断为空，出现提示信息。（当kjlx为3时，不提示）
      csdmList.forEach((item) => {
        if (node.csdm == item.CSDM) {
          node.kjlx = item.KJLX || '0';
        }
      });

    }
    if (!!node.childNodes && node.childNodes.length > 0) {
      node.jdgz = 'or'
    }
  }

  sdxNodes = childNodes;// 协议列表

};
onMounted(function () {
  initParentLineHeight();
  initData();
  getListData();
  dataAddDisabled();
  initPage();
});

const gzmcLabel = computed(function () {
  let cpName = sdxInfo.cpgzjb == '0' ? "产品类型" : "产品代码";
  return "【" + cpName + "】规则名称";
});

//监听产品类型搜索框
watch(filterCplxValue, value => {
  cplxExpandedKeys.value = [];//每次打开进行初始化
  cplxData.map((item) => {
    if ((item.cplxbm + '-' + item.name).indexOf(value) > -1) {
      getCplxExpanded(item.cplxbm);
    }
  }).filter((item, i, self) => item && self.indexOf(item) === i);
});
</script>

<style scoped>
.inputClass {
  width: 280px;
}

.ant-form-item {
  margin-bottom: 24px;
}

</style>