package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName t_ods_ygt_cif_tpj_wjcs
 */
@TableName(value ="t_ods_ygt_cif_tpj_wjcs")
@Data
public class TOdsYgtCifTpjWjcs implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 
     */
    private Long sdxfl;

    /**
     * 
     */
    private String jrjg;

    /**
     * 
     */
    private String wjbm;

    /**
     * 
     */
    private String name;

    /**
     * 
     */
    private Long sdxlb;

    /**
     * 
     */
    private Long khlb;

    /**
     * 
     */
    private String wjbb;

    /**
     * 
     */
    private Long wjzt;

    /**
     * 
     */
    private Integer cpzq;

    /**
     * 
     */
    private Integer hgfs;

    /**
     * 
     */
    private Long tmscfs;

    /**
     * 
     */
    private Integer tgts;

    /**
     * 
     */
    private Integer mrcpcs;

    /**
     * 
     */
    private String zssp;

    /**
     * 
     */
    private Long zsspbxgk;

    /**
     * 
     */
    private Integer wjts;

    /**
     * 
     */
    private String dtgz;

    /**
     * 
     */
    private Integer mncpcs;

    /**
     * 
     */
    private Integer mycpcs;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TOdsYgtCifTpjWjcs other = (TOdsYgtCifTpjWjcs) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSdxfl() == null ? other.getSdxfl() == null : this.getSdxfl().equals(other.getSdxfl()))
            && (this.getJrjg() == null ? other.getJrjg() == null : this.getJrjg().equals(other.getJrjg()))
            && (this.getWjbm() == null ? other.getWjbm() == null : this.getWjbm().equals(other.getWjbm()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getSdxlb() == null ? other.getSdxlb() == null : this.getSdxlb().equals(other.getSdxlb()))
            && (this.getKhlb() == null ? other.getKhlb() == null : this.getKhlb().equals(other.getKhlb()))
            && (this.getWjbb() == null ? other.getWjbb() == null : this.getWjbb().equals(other.getWjbb()))
            && (this.getWjzt() == null ? other.getWjzt() == null : this.getWjzt().equals(other.getWjzt()))
            && (this.getCpzq() == null ? other.getCpzq() == null : this.getCpzq().equals(other.getCpzq()))
            && (this.getHgfs() == null ? other.getHgfs() == null : this.getHgfs().equals(other.getHgfs()))
            && (this.getTmscfs() == null ? other.getTmscfs() == null : this.getTmscfs().equals(other.getTmscfs()))
            && (this.getTgts() == null ? other.getTgts() == null : this.getTgts().equals(other.getTgts()))
            && (this.getMrcpcs() == null ? other.getMrcpcs() == null : this.getMrcpcs().equals(other.getMrcpcs()))
            && (this.getZssp() == null ? other.getZssp() == null : this.getZssp().equals(other.getZssp()))
            && (this.getZsspbxgk() == null ? other.getZsspbxgk() == null : this.getZsspbxgk().equals(other.getZsspbxgk()))
            && (this.getWjts() == null ? other.getWjts() == null : this.getWjts().equals(other.getWjts()))
            && (this.getDtgz() == null ? other.getDtgz() == null : this.getDtgz().equals(other.getDtgz()))
            && (this.getMncpcs() == null ? other.getMncpcs() == null : this.getMncpcs().equals(other.getMncpcs()))
            && (this.getMycpcs() == null ? other.getMycpcs() == null : this.getMycpcs().equals(other.getMycpcs()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSdxfl() == null) ? 0 : getSdxfl().hashCode());
        result = prime * result + ((getJrjg() == null) ? 0 : getJrjg().hashCode());
        result = prime * result + ((getWjbm() == null) ? 0 : getWjbm().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getSdxlb() == null) ? 0 : getSdxlb().hashCode());
        result = prime * result + ((getKhlb() == null) ? 0 : getKhlb().hashCode());
        result = prime * result + ((getWjbb() == null) ? 0 : getWjbb().hashCode());
        result = prime * result + ((getWjzt() == null) ? 0 : getWjzt().hashCode());
        result = prime * result + ((getCpzq() == null) ? 0 : getCpzq().hashCode());
        result = prime * result + ((getHgfs() == null) ? 0 : getHgfs().hashCode());
        result = prime * result + ((getTmscfs() == null) ? 0 : getTmscfs().hashCode());
        result = prime * result + ((getTgts() == null) ? 0 : getTgts().hashCode());
        result = prime * result + ((getMrcpcs() == null) ? 0 : getMrcpcs().hashCode());
        result = prime * result + ((getZssp() == null) ? 0 : getZssp().hashCode());
        result = prime * result + ((getZsspbxgk() == null) ? 0 : getZsspbxgk().hashCode());
        result = prime * result + ((getWjts() == null) ? 0 : getWjts().hashCode());
        result = prime * result + ((getDtgz() == null) ? 0 : getDtgz().hashCode());
        result = prime * result + ((getMncpcs() == null) ? 0 : getMncpcs().hashCode());
        result = prime * result + ((getMycpcs() == null) ? 0 : getMycpcs().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sdxfl=").append(sdxfl);
        sb.append(", jrjg=").append(jrjg);
        sb.append(", wjbm=").append(wjbm);
        sb.append(", name=").append(name);
        sb.append(", sdxlb=").append(sdxlb);
        sb.append(", khlb=").append(khlb);
        sb.append(", wjbb=").append(wjbb);
        sb.append(", wjzt=").append(wjzt);
        sb.append(", cpzq=").append(cpzq);
        sb.append(", hgfs=").append(hgfs);
        sb.append(", tmscfs=").append(tmscfs);
        sb.append(", tgts=").append(tgts);
        sb.append(", mrcpcs=").append(mrcpcs);
        sb.append(", zssp=").append(zssp);
        sb.append(", zsspbxgk=").append(zsspbxgk);
        sb.append(", wjts=").append(wjts);
        sb.append(", dtgz=").append(dtgz);
        sb.append(", mncpcs=").append(mncpcs);
        sb.append(", mycpcs=").append(mycpcs);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}