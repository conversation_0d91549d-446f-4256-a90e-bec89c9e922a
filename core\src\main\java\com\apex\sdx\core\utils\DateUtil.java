package com.apex.sdx.core.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 日期工具
 *
 * <AUTHOR>
 */
@Slf4j
public class DateUtil {

    public static final String F_DATE8 = "yyyyMMdd";

    public static final String F_DATE10 = "yyyy-MM-dd";

    public static final String F_TIME8 = "HH:mm:ss";

    public static final String F_DATE8_TIME8 = "yyyyMMdd HH:mm:ss";

    public static final String F_DATE10_TIME8 = "yyyy-MM-dd HH:mm:ss";

    /**
     * 根据日期格式获取当前日期
     *
     * @return
     */
    public static String getNowDate(String dateFormat) {
        return dateFormat(LocalDateTime.now(), dateFormat);
    }

    /**
     * 根据日期格式获取当前日期
     *
     * @return 当天 日期格式：yyyyMMdd
     */
    public static int getNowDateForInt() {
        String date = dateFormat(LocalDateTime.now(), DateUtil.F_DATE8);
        return Integer.valueOf(date);
    }

    /**
     * 获取当前时间，时间格式：HH:mm:ss
     *
     * @return
     */
    public static String getNowTime() {
        return dateFormat(LocalDateTime.now(), DateUtil.F_TIME8);
    }

    /**
     * 获取昨天的日期。
     *
     * @return 昨天的日期。格式：yyyy-MM-dd。
     */
    public static String getYestodayDate() {
        return dateFormat(LocalDateTime.now().minusDays(1), F_DATE10);
    }

    /**
     * 当前日期+时间yyyy-MM-dd HH:mm:ss
     *
     * @return
     */
    public static String getNowDateTime() {
        return dateFormat(LocalDateTime.now(), DateUtil.F_DATE10_TIME8);
    }

    /**
     * 当前年yyyy
     *
     * @return
     */
    public static String getNowYear() {
        return dateFormat(LocalDateTime.now(), "yyyy");
    }

    /**
     * 当前月份MM
     *
     * @return
     */
    public static String getNowMonth() {
        return dateFormat(LocalDateTime.now(), "MM");
    }

    /**
     * 当前天数dd
     *
     * @return
     */
    public static String getNowDay() {
        return dateFormat(LocalDateTime.now(), "dd");
    }

    /**
     * 当前年月yyyy-MM
     *
     * @return
     */
    public static String getNowYearMonth() {
        return dateFormat(LocalDateTime.now(), "yyyy-MM");
    }

    /**
     * 当前年月yyyyMM
     *
     * @return
     */
    public static String getNowYearMonth1() {
        return dateFormat(LocalDateTime.now(), "yyyyMM");
    }


    /**
     * 当前年月yyyyMM的前N个月
     *
     * @return
     */
    public static String getPreNowYearMonth(int month) {
        return dateFormat(LocalDateTime.now().minusMonths(month), F_DATE10);
    }

    /**
     * 获取当前周周一的日期 日期格式：yyyyMMdd
     *
     * @return
     */
    public static Integer getFirstDayOfWeek() {
        LocalDate now = LocalDate.now();
        LocalDate firstDayOfWeek = now.minusDays((now.getDayOfWeek().getValue() - DayOfWeek.MONDAY.getValue()));
        return localDateToInt(firstDayOfWeek);
    }

    /**
     * 获取当前月第一天的日期 日期格式：yyyyMMdd
     *
     * @return
     */
    public static Integer getFirstDayOfMonth() {
        return localDateToInt(LocalDate.now().withDayOfMonth(1));
    }

    /**
     * 指定日期时间格式转化
     *
     * @param date
     * @param dateFormat
     * @return
     */
    public static String dateFormat(LocalDateTime date, String dateFormat) {
        if (date != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
            try {
                return date.format(formatter);
            } catch (Exception e) {
                log.error(String.format("转换日期[%s]格式异常:%s", date, e.getMessage()), e);
                return null;
            }
        }
        return null;
    }
    public static String dateFormat(Date date, String dateFormat) {
        if (date != null) {
            SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
            try {
                return sdf.format(date);
            } catch (Exception e) {
                log.error(String.format("转换日期[%s]格式异常:%s", date, e.getMessage()), e);
                return null;
            }
        }
        return null;
    }
    public static String dateFormat(String date, String dateFormat) {
        if (!StringUtils.isEmpty(date)) {
            try {
                LocalDateTime localDate = LocalDateTime.parse(date, DateTimeFormatter.ofPattern(F_DATE8));
                return localDate.format(DateTimeFormatter.ofPattern(dateFormat));
            } catch (Exception e) {
                log.error(String.format("转换日期[%s]格式异常:%s", date, e.getMessage()), e);
                return null;
            }
        }
        return null;
    }
    /**
     * 获取与当天间隔了指定天数的日期。
     *
     * @param days 间隔天数。正整数代表向前，负整数代表向后。
     * @return 时间。格式：yyyy-MM-dd。
     */
    public static String getPreOrNextDate(int days, String dateFormat) {
        return dateFormat(LocalDateTime.now().plusDays(days), dateFormat);
    }

    /**
     * 获取与当天间隔了指定月份的日期。
     *
     * @param mouths 间隔月数。正整数代表向前，负整数代表向后。
     * @return 时间。默认格式：yyyy-MM-dd。
     */
    public static String getPreOrNextMouth(int mouths, String dateFormat) {
        dateFormat = dateFormat == null ? F_DATE10 : dateFormat;
        return dateFormat(LocalDateTime.now().plusMonths(mouths), dateFormat);
    }

    /**
     * 获取与当天间隔了指定年数的日期。
     *
     * @param years 间隔年数。正整数代表向前，负整数代表向后。
     * @return 时间。默认格式：yyyy-MM-dd。
     */
    public static String getPreOrNextYear(int years, String dateFormat) {
        dateFormat = dateFormat == null ? F_DATE10 : dateFormat;
        return dateFormat(LocalDateTime.now().plusYears(years), dateFormat);
    }

    /**
     * 获取某一个日期加减 天数之后的日期
     *
     * @param date 日期。
     * @param days 间隔天数.正整数代表向前，负整数代表向后。
     * @return 日期。默认格式：yyyy-MM-dd。
     */
    public static String getDateModifyDay(String date, int days, String dateFormat) {
        dateFormat = dateFormat == null ? "yyyy-MM-dd" : dateFormat;
        if (date != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
            try {
                LocalDate localDate = LocalDate.parse(date, formatter);
                return localDate.plusDays(days).format(formatter);
            } catch (Exception e) {
                log.error(String.format("转换日期[%s]格式异常:%s", date, e.getMessage()), e);
                return null;
            }
        }
        return null;
    }

    /**
     * 获取与指定日期间隔了指定月数的日期
     *
     * @param date 日期
     * @param mouths 间隔月数.正整数代表向前，负整数代表向后。
     * @param dateFormat 日期格式
     * @return 日期。默认格式：yyyy-MM-dd。
     */
    public static String getPreOrNextMouth(String date, int mouths, String dateFormat) {
        dateFormat = dateFormat == null ? "yyyy-MM-dd" : dateFormat;
        if (date != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
            try {
                LocalDate localDate = LocalDate.parse(date, formatter);
                return localDate.plusMonths(mouths).format(formatter);
            } catch (Exception e) {
                log.error(String.format("转换日期[%s]格式异常:%s", date, e.getMessage()), e);
                return null;
            }
        }
        return null;
    }

    /**
     *
     * @param dateTime 日期
     * @param sourceFormat 原日期格式
     * @param targetFormat 目标日期格式
     * @return
     */
    public static String formatDateTime(String dateTime, String sourceFormat, String targetFormat) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(targetFormat);
            LocalDateTime parse = LocalDateTime.parse(dateTime, DateTimeFormatter.ofPattern(sourceFormat));
            return formatter.format(parse);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dateTime;
    }

    /**
     * 根据格式转换毫秒时间
     *
     * @param millis   毫秒时间戳
     * @param dateFormat 日期格式
     * @return
     */
    public static String formatMillise(Long millis, String dateFormat) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
            Instant instant = Instant.ofEpochMilli(millis);
            LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
            return dateTime.format(formatter);
        } catch (Exception e) {
            return String.valueOf(millis);
        }
    }

    /**
     * 根据秒数转化时分秒格式
     * @param duration 秒数
     * @return 格式：XX时XX分XX秒
     */
    public static String getTimeStr(Long duration) {
        String str = "";
        if (duration != null && duration >= 0) {
            long hours = TimeUnit.MILLISECONDS.toHours(duration);
            long minutes = TimeUnit.MILLISECONDS.toMinutes(duration % (60 * 60 * 1000));
            long seconds = TimeUnit.MILLISECONDS.toSeconds(duration % (60 * 1000));
            if (hours > 0) {
                str = hours + "小时";
            }
            if (minutes > 0) {
                str = str + minutes + "分";
            }
            if (seconds > 0) {
                str = str + seconds + "秒";
            }
            if ("".equals(str)) {
                str = duration + "秒";
            }
            return str;
        } else {
            return null;
        }
    }

    /**
     * 将LocalDate日期格式转换为数值型日期格式
     *
     * @param date 待转换的日期
     * @return
     */
    public static Integer localDateToInt(LocalDate date) {
        if (date == null) {
            return null;
        }
        // 将LocalDate对象转换成字符串，格式为YYYYMMDD
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        try {
            String dateString = date.format(formatter);
            // 将字符串转换成整数
            return Integer.parseInt(dateString);
        } catch (Exception e) {
            log.error(String.format("转换日期格式异常:%s", e.getMessage()), e);
            return null;
        }
    }

    /**
     * 获取俩日期间隔天数
     *
     * @param one
     * @param two
     * @return
     */
    private static long daysBetween(LocalDate one, LocalDate two) {
        return ChronoUnit.DAYS.between(one, two);
    }

    /**
     * 获取俩日期间隔天数
     *
     * @param start
     * @param end
     * @param dateFormat
     * @return
     */
    public static int getDaysBetween(String start, String end, String dateFormat) {
        if (!StringUtils.isEmpty(start) && !StringUtils.isEmpty(end)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
            try {
                LocalDate startDate = LocalDate.parse(start, formatter);
                LocalDate endDate = LocalDate.parse(end, formatter);
                return (int) daysBetween(startDate, endDate);
            } catch (DateTimeParseException e) {
                log.error("日期转换异常:" + e.getMessage(), e);
            }
        }
        return 0;
    }

}
