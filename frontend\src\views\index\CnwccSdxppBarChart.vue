<template>
  <div>
    <div :id="chartsId" style=" width:98%; height:240px;left: 0%;position: relative;">
    </div>
  </div>

</template>
<script>
import {defineComponent} from 'vue';

let barCharts = [];
export default defineComponent({
  props: ["dataset", "dataset1", "title", "type"],
  data() {
    return {
      labels: [],//名称  ['场外产品持仓', '开放式基金持仓']
      values: [],//数值  [[10, 20], [30, 40]]
      sum: [],//总和  [40, 60]
      legend: ['适当', '不适当'],
      attackSourcesColor: [
        new this.$echarts.graphic.LinearGradient(0, 1, 1, 1, [
          {offset: 0, color: '#36A6FC'},
          //{offset: 1, color: '#00B42A'},
        ]),
        new this.$echarts.graphic.LinearGradient(0, 1, 1, 1, [
          {offset: 0, color: '#FFC730'},
          //{offset: 1, color: '#FF1919'},
        ]),
      ],
    }
  },
  methods: {
    initBarChart() {
      let el = document.getElementById(this.chartsId)
      barCharts[this.chartsId] = this.$echarts.init(el);
      this.setOption();
      // 添加窗口大小变化监听
      window.addEventListener('resize', this.handleResize);
    },
    setOption() {
      this.labels = this.$props.dataset[0];
      this.values = this.$props.dataset[1];
      let sdSum = 0;
      let bsdSUm = 0;
      this.values.forEach(item => {
        sdSum += item[0];
        bsdSUm += item[1];
      })
      this.sum = [sdSum, bsdSUm];

      let option = this.getOption();
      barCharts[this.chartsId].setOption(option);
      // 初始渲染后立即调整尺寸
      this.$nextTick(() => {
        barCharts[this.chartsId]?.resize();
      });
    },
    handleResize() {
      if (barCharts[this.chartsId]) {
        barCharts[this.chartsId].resize();
      }
    },
    getOption() {
      let _this = this;
      let option = {
        tooltip: {
          show: true,
          className: 'myTooltip', // 指定自定义类名
          trigger: 'axis',
          axisPointer: {// 坐标轴指示器，坐标轴触发有效
            type: 'shadow'
          },
          formatter: function (params) {
            let str = '<div style="width:200px;padding: 10px;background-color: #D9E1F0;border-radius: 10px;">' +
                '           <div style="margin-bottom: 5px"><span style="color: #454A55;">' + params[0].name + '</span></div>' +
                '          <div style="padding: 0 0 0 10px;background-color: #fff;border-radius: 10px;">';
            for (let i = 0; i < params.length; i++) {
              str += '            <p>' +
                  '              <span style="color: #454A55;width: 33%;display: inline-block;">' + params[i].seriesName + '</span>' +
                  '              <span style="color: #454A55;width: 33%;display: inline-block;">' + params[i].value + '</span>' +
                  '            </p>'
            }
            str += '           </div>' +
                '      </div>';
            return str;
          }
        },
        grid:
            {
              left: '25%',
              right: '10%',
              top: '5%',
              bottom: '35%'
            },
        legend: {
          show: true,
          data: this.legend,
          textStyle: {
            color: '#888888'
          },
          bottom: 20,
          left: 'center',
          icon: 'circle', // 设置图例为小圆点
          itemWidth: 11,  // 控制圆点宽度
          itemHeight: 11, // 控制圆点高度
          itemGap: 15,   // 图例项之间的间隔
        },
        yAxis: [
          {
            type: 'category',
            data: this.labels,
            axisLine: {
              show: false
            },
            splitLine: {
              show: false,
              interval: 0,
            },
            axisTick: {
              show: false  // 隐藏刻度线
            },
            axisLabel: {
              show: true,  // 显示刻度标签
              margin: 5,//刻度标签与轴线之间的距离。
              color: '#333333',
              formatter: function (value, index) {
                return '{a|' + value + '}   \n' + '{b|' + _this.sum[index] + '}   ';
              },
              rich: {
                a: {
                  fontSize: 14,
                  textAlign: 'left',
                  lineHeight: 30,
                },
                b: {
                  fontWeight: 'bold',
                  fontSize: 18,
                  lineHeight: 30,
                }
              },
            },
          },
        ],
        xAxis: [{
          type: 'value',
          boundaryGap: [0, 0.8]
        },
        ],
        series: [
          ...this.getSeriesData(),
        ]
      };
      return option;
    },
    getSeriesData() {
      let _this = this;
      let data = [];
      this.legend.forEach((item, index) => {
        data.push({
          type: 'bar',
          name: item,
          //legendHoverLink: false,
          //  stack: 'total',
          barWidth: '20%',
          // 调整柱状图之间的间隔
          barCategoryGap: '100%',
          // 调整同一系列中柱状图之间的间隔
          barGap: '50%',
          itemStyle: {
            color: _this.attackSourcesColor[index], // 设置柱条颜色
            borderRadius: [5, 5, 5, 5]
          },
          label: {
            show: true,
            position: 'right',
            fontWeight: 400,
            fontSize: 14,
            color: '#333333',
            lineHeight: 30,
            formatter: function (params) {
              return params.value;
            },
            offset: [0, 2],
          },
          markLine: {
            symbol: 'none', // 不显示符号
            silent: true, // 不触发事件
            lineStyle: {
              color: '#DDDDDD', // 横线颜色
              type: 'solid' // 实线
            },
            data: [
              [
                {
                  x: '25%',
                  y: '35%'
                },
                {
                  x: '100%',
                  y: '35%'
                }
              ]
            ]
          },
          data: _this.values[index],
        });
      });
      return data;
    },

  },
  mounted() {
    if (this.$props.dataset && this.$props.dataset.length > 0) {
      this.initBarChart()
    }
  },
  computed: {
    chartsId() {
      return "id_" + this.title
    },
  },
  watch: {
    dataset(newValue, oldValue) {
      if (newValue && newValue.length > 0 && JSON.stringify(newValue) != JSON.stringify(oldValue)) {
        if (barCharts[this.chartsId]) {
          this.setOption();
        } else {
          this.initBarChart();
        }
      }
    }
  },
  unmounted() {
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize);
    if (barCharts[this.chartsId]) {
      barCharts[this.chartsId].dispose();
      delete barCharts[this.chartsId];
    }
  },
});
</script>

<style scoped>

:deep(.myTooltip) {
  padding: 0 !important;
  border-radius: 10px !important;
  border-width: 0px !important;
}
</style>