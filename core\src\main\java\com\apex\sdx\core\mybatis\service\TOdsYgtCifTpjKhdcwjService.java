package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjKhdcwj;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *
 */
public interface TOdsYgtCifTpjKhdcwjService extends IService<TOdsYgtCifTpjKhdcwj> {

    /**
     *
     * @param sdxfl
     * @param sdxlb
     * @param sdxlbIbm
     * @param khlb
     * @param khh
     * @param zjlb
     * @param zjbh
     * @param khjc
     * @param cid
     * @return
     */
    Long getMaxIdByKhsdx(Integer sdxfl, String sdxlb, String sdxlbIbm, Integer khlb, String khh, Integer zjlb, String zjbh, String khjc, String cid);
}
