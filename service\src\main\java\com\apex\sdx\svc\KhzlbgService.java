package com.apex.sdx.svc;

import com.apex.sdx.api.req.khgl.KhzlbgmxcxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.vo.khgl.KhzlbgmxVo;
import com.apex.sdx.convert.KhzlbgmxVoMapping;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhzlxg;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhzlxgService;
import com.apexsoft.LiveService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.query.IKhzlbgService;

/**
 * <AUTHOR>
 * @Date 2025-02-13
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "客户资料变更查询服务")
public class KhzlbgService implements IKhzlbgService {

    @Autowired
    TOdsYgtCifTkhzlxgService tkhzlxgService;

    @Autowired
    KhzlbgmxVoMapping khzlbgmxVoMapping;

    @Override
    public QueryPageResponse<KhzlbgmxVo> khzlbgmxcx(KhzlbgmxcxReq req) throws Exception {
        Assert.notNull(req, KhzlbgmxcxReq::getKhh);
        QueryPageResponse<KhzlbgmxVo> result = new QueryPageResponse<>(1, "查询成功");
        String khh = req.getKhh();
        Integer ksrq = req.getKsrq();
        Integer jsrq = req.getJsrq();

        Page<TOdsYgtCifTkhzlxg> page = tkhzlxgService.queryByKhh(khh, ksrq, jsrq, req.isSearchCount(), req.getPagesize(), req.getPagenum());

        Page<KhzlbgmxVo> khzlbgmxVoPage = khzlbgmxVoMapping.pageConver(page);

        result.page(khzlbgmxVoPage);

        return result;
    }
}
