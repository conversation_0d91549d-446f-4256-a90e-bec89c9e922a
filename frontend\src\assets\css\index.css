@charset "utf-8";
/* CSS Document */
*{ margin:0; padding:0; box-sizing:border-box;font-family:"Microsoft Yahei",arial,"Hiragino Sans GB",sans-serif; font-size: 14px;}
html,body{height:100%; width:100%;}
li{ list-style:none;}
img ,a{ border:0; text-decoration:none;}
.clear:after { content:''; display:table; clear:both; height:0;}
.tc{ text-align:center;}
.tl{ text-align:left;}
.tr{ text-align:right;}
.l{ float:left;}
.r{ float:right;}
.hide{ display:none;}
:root {
    --primary-color: #4658FF;
    --apex-red-primary-color: #ca3e47;
    --black-gold-primary-color: #ecc9a4;
    --apex-red-primary-color-1: #FAF5E2;
}
/*滚动条1*/
.innerbox::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}
.innerbox::-webkit-scrollbar-thumb {
    border-radius: 5px;
    /* -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2); */
    background: rgba(20,116,222,1);
}
.innerbox::-webkit-scrollbar-track {
    /* -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2); */
    border-radius: 0;
    /* background: rgba(0,0,0,0.1); */
}
/*滚动条2*/
.innerbox2::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}
.innerbox2::-webkit-scrollbar-thumb {
    border-radius: 5px;
    /* -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2); */
    background: rgba(102,102,102,.6);
}
.innerbox2::-webkit-scrollbar-track {
    /* -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2); */
    border-radius: 0;
    /* background: rgba(0,0,0,0.1); */
}
input[type=text],select,input[type=password]{ font-family: "Microsoft Yahei", arial, "Hiragino Sans GB", sans-serif;}
::-ms-reveal{display:none;}
input[type=text]::-webkit-input-placeholder{color: #ccc;}
input[type=text]::-moz-input-placeholder{ color: #ccc;}
input[type=text]:-ms-input-placeholder {color: #ccc !important;}
textarea::-webkit-input-placeholder{color: #ccc;}
textarea::-moz-input-placeholder{ color: #ccc;}
textarea:-ms-input-placeholder {color: #ccc !important;}
/**/
.label_checkbox{line-height: 22px;}
.label_checkbox label.advice{margin-right: 6px;}
.advice{
    display: inline-block;
    background-repeat: no-repeat;
    background-position: center;
    vertical-align: middle;
    margin-top:0px;
}
.label_checkbox .advice{display:inline-block;vertical-align: middle;height: 18px;width: 18px !important;text-align: center;line-height: 18px;}
.label_radio .advice{display:inline-block;vertical-align: middle;height: 19px;width: 19px !important;line-height: 20px;text-align: center;}
.label_radio .radio-name,.label_checkbox .checkbox-name{text-align: left;font-size:12px;color:#666;line-height: 1.5;vertical-align: middle;}
input[type="radio"] + .advice{border:1px solid #aaa;border-radius: 10px;background-color:#fff;}
input[type="radio"]:checked + .advice{color:#fff;border:1px solid #458ae5; border-radius: 10px;background-color:#458ae5;}
input[type="radio"] + .advice:after{ content:" ";}
input[type="radio"]:checked + .advice:after{font-family: "iconfont" !important;content:"\e620";position: relative;font-size: 14px;top: -1px;font-weight: lighter;}

input[type="checkbox"] + .advice{border: 1px solid #aaa;border-radius: 4px;background-color:#fff;}
input[type="checkbox"]:checked + .advice{color:#fff;border:1px solid #458ae5;border-radius: 4px;background-color:#458ae5;}
input[type="checkbox"] + .advice:after{ content:" ";}
input[type="checkbox"]:checked + .advice:after{font-family: "iconfont" !important;content:"\e620";position: relative;left: 0;top: -1px;font-size: 14px;font-weight: lighter;line-height: 18px;height: 18px;text-align: center;width: 18px;}


input[type="radio"][disabled] + .advice{border:1px solid #458ae5;border-radius: 10px;background-color:#fff;opacity:.7;}
input[type="radio"][disabled]:checked + .advice{ color:#fff; border:1px solid #458ae5;border-radius:10px; background-color:#458ae5; opacity:.7;}
input[type="radio"][disabled] + .advice:after{ content:" ";}
input[type="radio"][disabled]:checked + .advice:after{ font-family: "iconfont" !important;content:"\e620";position: relative;font-size: 16px;top:  -1px; font-weight:lighter;}


input[type="checkbox"][disabled] + .advice{border:1px solid #d9d9d9;border-radius:2px; background-color:#fff; opacity:.7;}
input[type="checkbox"][disabled]:checked + .advice{line-height: 18px; color:#fff; border:1px solid #458ae5; border-radius:4px; background-color:#458ae5; opacity:.7;}
input[type="checkbox"][disabled] + .advice:after{ content:" "; }
input[type="checkbox"][disabled]:checked + .advice:after{ font-family: "iconfont" !important;content:"\e620";position: relative;left: 0px;top:  -1px;font-size: 14px; font-weight:lighter;}


input[type="radio"][disabled] + .advice + .radio-name{  color: #b8b8b8;}
input[type="radio"][disabled]:checked + .advice + .radio-name{  color: #b8b8b8;}

input[type="checkbox"][disabled] + .advice + .checkbox-name{ color: #b8b8b8;}
input[type="checkbox"][disabled]:checked + .advice + .checkbox-name { color: #b8b8b8;}

input[type="radio"]:checked.error + .advice{  color:#fff;border:1px solid #458ae5; border-radius:10px; background-color:#458ae5;}
input[type="checkbox"]:checked.error + .advice{ line-height: 20px; color:#fff; border:1px solid #458ae5;border-radius:4px; background-color:#458ae5;}
input[type="radio"]:checked.error + .advice:after{font-family: "iconfont" !important; content:"\e620";position: relative;font-size: 16px;top: -1px; font-weight:lighter;}
input[type="checkbox"]:checked.error + .advice:after{font-family: "iconfont" !important; content:"\e620";position: relative;left: 0px;top: -1px;font-size: 14px; font-weight:lighter;}


input[type="radio"].error + .advice{  color:#fff; border:1px solid #ff1919;border-radius:10px; background-color:#fff;}
input[type="checkbox"].error + .advice{ color:#fff; border:1px solid #ff1919;border-radius:4px; background-color:#fff;}
input[type="radio"].error + .advice:after{ content:" ";}
input[type="checkbox"].error + .advice:after{ content:"";}


input[type="radio"]:checked.error + .advice + .radio-name{  color: #ff1919;}
input[type="checkbox"]:checked.error + .advice + .checkbox-name{  color: #ff1919;}
input[type="radio"].error + .advice + .radio-name{  color: #ff1919;}
input[type="checkbox"].error + .advice + .checkbox-name{  color: #ff1919;}