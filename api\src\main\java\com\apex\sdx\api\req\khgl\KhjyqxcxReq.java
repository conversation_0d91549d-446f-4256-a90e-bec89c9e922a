package com.apex.sdx.api.req.khgl;

import com.apex.sdx.api.req.common.PageRequest;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025-01-22
 * @Description:
 */
@Setter
@Getter
public class KhjyqxcxReq extends PageRequest {
    @NotNull(message = "不能为空")
    @LiveProperty(note = "客户号", index = 1)
    private String khh;

    @LiveProperty(note = "业务账号", index = 2)
    private String ywzh;

    @LiveProperty(note = "股东号", index = 3)
    private String gdh;

    @LiveProperty(note = "交易权限", index = 4)
    private Integer jyqx;

    @LiveProperty(note = "状态", index = 5)
    private Integer zt;
}
