<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTkhjyqxMapper">

    <resultMap id="BaseResultMap" type="com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhjyqx">
        <result property="khh" column="KHH" jdbcType="VARCHAR"/>
        <result property="ywxt" column="YWXT" jdbcType="DECIMAL"/>
        <result property="ywzh" column="YWZH" jdbcType="VARCHAR"/>
        <result property="jys" column="JYS" jdbcType="DECIMAL"/>
        <result property="gdh" column="GDH" jdbcType="VARCHAR"/>
        <result property="jyqx" column="JYQX" jdbcType="DECIMAL"/>
        <result property="sx1" column="SX1" jdbcType="VARCHAR"/>
        <result property="sx2" column="SX2" jdbcType="VARCHAR"/>
        <result property="yyb" column="YYB" jdbcType="DECIMAL"/>
        <result property="zt" column="ZT" jdbcType="DECIMAL"/>
        <result property="ktrq" column="KTRQ" jdbcType="DECIMAL"/>
        <result property="gbrq" column="GBRQ" jdbcType="DECIMAL"/>
        <result property="ywqqid" column="YWQQID" jdbcType="DECIMAL"/>
        <result property="zqdm" column="ZQDM" jdbcType="VARCHAR"/>
        <result property="zqmc" column="ZQMC" jdbcType="VARCHAR"/>
        <result property="qxyxq" column="QXYXQ" jdbcType="DECIMAL"/>
        <result property="hmbs" column="HMBS" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        KHH
        ,YWXT,YWZH,
        JYS,GDH,JYQX,
        SX1,SX2,YYB,
        ZT,KTRQ,GBRQ,
        YWQQID,ZQDM,ZQMC,
        QXYXQ,HMBS
    </sql>
    <select id="queryKhjyqx" resultType="com.apex.sdx.api.vo.khgl.KhjyqxVo">
        SELECT a.*, b.lsrq lsrq, b.sdxjg, b.zy,v.XTMC ywxtmc
        FROM ods.t_ods_ygt_cif_tkhjyqx a
        LEFT JOIN (
        SELECT
        LISTAGG(rq, ';') WITHIN GROUP (ORDER BY rq, id) AS lsrq,
        LISTAGG(sdxjg, ';') WITHIN GROUP (ORDER BY rq, id) AS sdxjg,
        LISTAGG(zy, ';') WITHIN GROUP (ORDER BY rq, id) AS zy,
        khh,
        jys,
        jyqx,
        gdh
        FROM sdx.tyw_khjyqxls
        GROUP BY khh, jys, jyqx, gdh
        ) b ON a.khh = b.khh AND a.jys = b.jys AND a.jyqx = b.jyqx AND a.gdh = b.gdh
        left join sdx.TSYSTEM v on a.ywxt = v.XTBH
        <where>
            <if test="khh != null and khh != ''">
                AND a.khh = #{khh}
            </if>
            <if test="ywzh != null and ywzh != ''">
                AND a.ywzh = #{ywzh}
            </if>
            <if test="gdh != null and gdh != ''">
                AND a.gdh = #{gdh}
            </if>
            <if test="jyqx != null">
                AND a.jyqx = #{jyqx}
            </if>
            <if test="zt != null">
                AND a.zt = #{zt}
            </if>
        </where>
    </select>
</mapper>
