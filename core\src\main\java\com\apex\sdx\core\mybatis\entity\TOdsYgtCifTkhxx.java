package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * @TableName t_ods_ygt_cif_tkhxx
 */
@TableName(value ="t_ods_ygt_cif_tkhxx", schema = "ods")
@Data
public class TOdsYgtCifTkhxx implements Serializable {
    /**
     * 
     */
    @TableId
    private String khh;

    /**
     * 
     */
    private String khjc;

    /**
     * 
     */
    private String khmc;

    /**
     * 数据字典: GT_KHZT
     */
    private Integer khzt;

    /**
     * 
     */
    private Long yyb;

    /**
     * 
     */
    private Long khqz;

    /**
     * 0-正式客户,1-潜在客户
     */
    private Integer qzkh;

    /**
     * 数据字典: GT_KHLB
     */
    private Integer khlb;

    /**
     * 数据字典: KH_KHFS
     */
    private Integer khfs;

    /**
     * 
     */
    private Integer khrq;

    /**
     * 
     */
    private Integer xhrq;

    /**
     * 
     */
    private Integer hfrq;

    /**
     * 数据字典: GT_ZJLB
     */
    private Integer zjlb;

    /**
     * 
     */
    private String zjbh;

    /**
     * 
     */
    private Integer zjqsrq;

    /**
     * 30001231代表长期
     */
    private Integer zjjzrq;

    /**
     * 
     */
    private String zjdz;

    /**
     * 
     */
    private String zjdzyb;

    /**
     * 
     */
    private String zjfzjg;

    /**
     * 
     */
    private String zjzp;

    /**
     * 
     */
    private String dz;

    /**
     * 
     */
    private String yzbm;

    /**
     * 
     */
    private String dh;

    /**
     * 
     */
    private String sj;

    /**
     * 
     */
    private String cz;

    /**
     * 
     */
    private String email;

    /**
     * 
     */
    private Integer gj;

    /**
     * 
     */
    private Integer yhtqybz;

    /**
     * 
     */
    private Integer yhtqyrq;

    /**
     * 
     */
    private String ymth;

    /**
     * 
     */
    private String tbsm;

    /**
     * 
     */
    private Integer xydj;

    /**
     * 
     */
    private BigDecimal zxdf;

    /**
     * 
     */
    private Integer dkbz;

    /**
     * 
     */
    private Integer ssjmsf;

    /**
     * 
     */
    private String province;

    /**
     * 
     */
    private String city;

    /**
     * 
     */
    private String sec;

    /**
     * 
     */
    private String cid;

    /**
     * 
     */
    private String xss;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TOdsYgtCifTkhxx other = (TOdsYgtCifTkhxx) that;
        return (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
            && (this.getKhjc() == null ? other.getKhjc() == null : this.getKhjc().equals(other.getKhjc()))
            && (this.getKhmc() == null ? other.getKhmc() == null : this.getKhmc().equals(other.getKhmc()))
            && (this.getKhzt() == null ? other.getKhzt() == null : this.getKhzt().equals(other.getKhzt()))
            && (this.getYyb() == null ? other.getYyb() == null : this.getYyb().equals(other.getYyb()))
            && (this.getKhqz() == null ? other.getKhqz() == null : this.getKhqz().equals(other.getKhqz()))
            && (this.getQzkh() == null ? other.getQzkh() == null : this.getQzkh().equals(other.getQzkh()))
            && (this.getKhlb() == null ? other.getKhlb() == null : this.getKhlb().equals(other.getKhlb()))
            && (this.getKhfs() == null ? other.getKhfs() == null : this.getKhfs().equals(other.getKhfs()))
            && (this.getKhrq() == null ? other.getKhrq() == null : this.getKhrq().equals(other.getKhrq()))
            && (this.getXhrq() == null ? other.getXhrq() == null : this.getXhrq().equals(other.getXhrq()))
            && (this.getHfrq() == null ? other.getHfrq() == null : this.getHfrq().equals(other.getHfrq()))
            && (this.getZjlb() == null ? other.getZjlb() == null : this.getZjlb().equals(other.getZjlb()))
            && (this.getZjbh() == null ? other.getZjbh() == null : this.getZjbh().equals(other.getZjbh()))
            && (this.getZjqsrq() == null ? other.getZjqsrq() == null : this.getZjqsrq().equals(other.getZjqsrq()))
            && (this.getZjjzrq() == null ? other.getZjjzrq() == null : this.getZjjzrq().equals(other.getZjjzrq()))
            && (this.getZjdz() == null ? other.getZjdz() == null : this.getZjdz().equals(other.getZjdz()))
            && (this.getZjdzyb() == null ? other.getZjdzyb() == null : this.getZjdzyb().equals(other.getZjdzyb()))
            && (this.getZjfzjg() == null ? other.getZjfzjg() == null : this.getZjfzjg().equals(other.getZjfzjg()))
            && (this.getZjzp() == null ? other.getZjzp() == null : this.getZjzp().equals(other.getZjzp()))
            && (this.getDz() == null ? other.getDz() == null : this.getDz().equals(other.getDz()))
            && (this.getYzbm() == null ? other.getYzbm() == null : this.getYzbm().equals(other.getYzbm()))
            && (this.getDh() == null ? other.getDh() == null : this.getDh().equals(other.getDh()))
            && (this.getSj() == null ? other.getSj() == null : this.getSj().equals(other.getSj()))
            && (this.getCz() == null ? other.getCz() == null : this.getCz().equals(other.getCz()))
            && (this.getEmail() == null ? other.getEmail() == null : this.getEmail().equals(other.getEmail()))
            && (this.getGj() == null ? other.getGj() == null : this.getGj().equals(other.getGj()))
            && (this.getYhtqybz() == null ? other.getYhtqybz() == null : this.getYhtqybz().equals(other.getYhtqybz()))
            && (this.getYhtqyrq() == null ? other.getYhtqyrq() == null : this.getYhtqyrq().equals(other.getYhtqyrq()))
            && (this.getYmth() == null ? other.getYmth() == null : this.getYmth().equals(other.getYmth()))
            && (this.getTbsm() == null ? other.getTbsm() == null : this.getTbsm().equals(other.getTbsm()))
            && (this.getXydj() == null ? other.getXydj() == null : this.getXydj().equals(other.getXydj()))
            && (this.getZxdf() == null ? other.getZxdf() == null : this.getZxdf().equals(other.getZxdf()))
            && (this.getDkbz() == null ? other.getDkbz() == null : this.getDkbz().equals(other.getDkbz()))
            && (this.getSsjmsf() == null ? other.getSsjmsf() == null : this.getSsjmsf().equals(other.getSsjmsf()))
            && (this.getProvince() == null ? other.getProvince() == null : this.getProvince().equals(other.getProvince()))
            && (this.getCity() == null ? other.getCity() == null : this.getCity().equals(other.getCity()))
            && (this.getSec() == null ? other.getSec() == null : this.getSec().equals(other.getSec()))
            && (this.getCid() == null ? other.getCid() == null : this.getCid().equals(other.getCid()))
            && (this.getXss() == null ? other.getXss() == null : this.getXss().equals(other.getXss()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getKhjc() == null) ? 0 : getKhjc().hashCode());
        result = prime * result + ((getKhmc() == null) ? 0 : getKhmc().hashCode());
        result = prime * result + ((getKhzt() == null) ? 0 : getKhzt().hashCode());
        result = prime * result + ((getYyb() == null) ? 0 : getYyb().hashCode());
        result = prime * result + ((getKhqz() == null) ? 0 : getKhqz().hashCode());
        result = prime * result + ((getQzkh() == null) ? 0 : getQzkh().hashCode());
        result = prime * result + ((getKhlb() == null) ? 0 : getKhlb().hashCode());
        result = prime * result + ((getKhfs() == null) ? 0 : getKhfs().hashCode());
        result = prime * result + ((getKhrq() == null) ? 0 : getKhrq().hashCode());
        result = prime * result + ((getXhrq() == null) ? 0 : getXhrq().hashCode());
        result = prime * result + ((getHfrq() == null) ? 0 : getHfrq().hashCode());
        result = prime * result + ((getZjlb() == null) ? 0 : getZjlb().hashCode());
        result = prime * result + ((getZjbh() == null) ? 0 : getZjbh().hashCode());
        result = prime * result + ((getZjqsrq() == null) ? 0 : getZjqsrq().hashCode());
        result = prime * result + ((getZjjzrq() == null) ? 0 : getZjjzrq().hashCode());
        result = prime * result + ((getZjdz() == null) ? 0 : getZjdz().hashCode());
        result = prime * result + ((getZjdzyb() == null) ? 0 : getZjdzyb().hashCode());
        result = prime * result + ((getZjfzjg() == null) ? 0 : getZjfzjg().hashCode());
        result = prime * result + ((getZjzp() == null) ? 0 : getZjzp().hashCode());
        result = prime * result + ((getDz() == null) ? 0 : getDz().hashCode());
        result = prime * result + ((getYzbm() == null) ? 0 : getYzbm().hashCode());
        result = prime * result + ((getDh() == null) ? 0 : getDh().hashCode());
        result = prime * result + ((getSj() == null) ? 0 : getSj().hashCode());
        result = prime * result + ((getCz() == null) ? 0 : getCz().hashCode());
        result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
        result = prime * result + ((getGj() == null) ? 0 : getGj().hashCode());
        result = prime * result + ((getYhtqybz() == null) ? 0 : getYhtqybz().hashCode());
        result = prime * result + ((getYhtqyrq() == null) ? 0 : getYhtqyrq().hashCode());
        result = prime * result + ((getYmth() == null) ? 0 : getYmth().hashCode());
        result = prime * result + ((getTbsm() == null) ? 0 : getTbsm().hashCode());
        result = prime * result + ((getXydj() == null) ? 0 : getXydj().hashCode());
        result = prime * result + ((getZxdf() == null) ? 0 : getZxdf().hashCode());
        result = prime * result + ((getDkbz() == null) ? 0 : getDkbz().hashCode());
        result = prime * result + ((getSsjmsf() == null) ? 0 : getSsjmsf().hashCode());
        result = prime * result + ((getProvince() == null) ? 0 : getProvince().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getSec() == null) ? 0 : getSec().hashCode());
        result = prime * result + ((getCid() == null) ? 0 : getCid().hashCode());
        result = prime * result + ((getXss() == null) ? 0 : getXss().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", khh=").append(khh);
        sb.append(", khjc=").append(khjc);
        sb.append(", khmc=").append(khmc);
        sb.append(", khzt=").append(khzt);
        sb.append(", yyb=").append(yyb);
        sb.append(", khqz=").append(khqz);
        sb.append(", qzkh=").append(qzkh);
        sb.append(", khlb=").append(khlb);
        sb.append(", khfs=").append(khfs);
        sb.append(", khrq=").append(khrq);
        sb.append(", xhrq=").append(xhrq);
        sb.append(", hfrq=").append(hfrq);
        sb.append(", zjlb=").append(zjlb);
        sb.append(", zjbh=").append(zjbh);
        sb.append(", zjqsrq=").append(zjqsrq);
        sb.append(", zjjzrq=").append(zjjzrq);
        sb.append(", zjdz=").append(zjdz);
        sb.append(", zjdzyb=").append(zjdzyb);
        sb.append(", zjfzjg=").append(zjfzjg);
        sb.append(", zjzp=").append(zjzp);
        sb.append(", dz=").append(dz);
        sb.append(", yzbm=").append(yzbm);
        sb.append(", dh=").append(dh);
        sb.append(", sj=").append(sj);
        sb.append(", cz=").append(cz);
        sb.append(", email=").append(email);
        sb.append(", gj=").append(gj);
        sb.append(", yhtqybz=").append(yhtqybz);
        sb.append(", yhtqyrq=").append(yhtqyrq);
        sb.append(", ymth=").append(ymth);
        sb.append(", tbsm=").append(tbsm);
        sb.append(", xydj=").append(xydj);
        sb.append(", zxdf=").append(zxdf);
        sb.append(", dkbz=").append(dkbz);
        sb.append(", ssjmsf=").append(ssjmsf);
        sb.append(", province=").append(province);
        sb.append(", city=").append(city);
        sb.append(", sec=").append(sec);
        sb.append(", cid=").append(cid);
        sb.append(", xss=").append(xss);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}