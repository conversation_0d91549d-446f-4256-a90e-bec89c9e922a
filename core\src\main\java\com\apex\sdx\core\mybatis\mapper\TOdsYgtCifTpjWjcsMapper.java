package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjWjcs;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @Entity com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjWjcs
 */
public interface TOdsYgtCifTpjWjcsMapper extends BaseMapper<TOdsYgtCifTpjWjcs> {

    @Select("SELECT ID, NAME, WJBM " +
            "FROM ods.t_ods_ygt_cif_tpj_wjcs " +
            "WHERE ID IN (SELECT PAPERID FROM ods.t_ods_ygt_cif_tpj_khdcwj WHERE ID = #{khdcwjid}); ")
    TOdsYgtCifTpjWjcs selectWjcsByWjid(@Param("khdcwjid") Integer khdcwjid);
}




