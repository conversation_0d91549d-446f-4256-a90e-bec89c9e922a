package com.apex.sdx.api.req.query;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/6/11 18:59
 * @Description:
 */
@Getter
@Setter
public class TgcpsdxcxReq implements Serializable {

    @LiveProperty(note = "客户号", index = 1)
    private String khh;

    @LiveProperty(note = "数据类型", index = 2)
    private Integer sjlx;

    @LiveProperty(note = "是否只显示不适当", index = 3)
    private boolean onlysdx;

    @LiveProperty(note = "日期", index = 4)
    private String rq;
}
