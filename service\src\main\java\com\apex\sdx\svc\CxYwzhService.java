package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.KhhReq;
import com.apex.sdx.api.resp.query.CxywzhRes;
import com.apex.sdx.api.vo.khgl.YwzhVo;
import com.apex.sdx.core.Constants;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTywzh;
import com.apex.sdx.core.mybatis.entity.TsdxjgQtyktyw;
import com.apex.sdx.core.mybatis.entity.Vxtdm;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTywzhService;
import com.apex.sdx.core.mybatis.service.TsdxjgQtyktywService;
import com.apex.sdx.core.mybatis.service.VxtdmService;
import com.apexsoft.LiveService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.query.ICxYwzhService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/1/20 10:50
 * @Description: 查询业务账户信息
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "查询业务账户信息")
public class CxYwzhService implements ICxYwzhService {

    @Autowired
    private TOdsYgtCifTywzhService tOdsYgtCifTywzhService;

    @Autowired
    private VxtdmService vsjzdService;
    
    @Autowired
    private TsdxjgQtyktywService tsdxjgQtyktywService;
    
    @Override
    public CxywzhRes check(KhhReq req) {
        Assert.notNull(req, KhhReq::getKhh);
        return null;
    }

    @Override
    public CxywzhRes execute(KhhReq req) {
        List<TOdsYgtCifTywzh> list = tOdsYgtCifTywzhService.getYwzhjbxx(req.getKhh());
        CxywzhRes.CxywzhResBuilder<?, ?> builder = CxywzhRes.builder().code(1).note("查询成功");
        // 查询营业部和业务账户状态的映射
        Map<String, Vxtdm> yybMap = vsjzdService.querySjzd4Map("YYB");
        Map<String, Vxtdm> ywzhztMap = vsjzdService.querySjzd4Map("GT_YWZHZT");
        
        // 仅当rq参数不为空时才查询业务适当性结果
        Map<Integer, Long> ywxtSdxjgMap = new HashMap<>();
        if (req.getRq() != null) {
            ywxtSdxjgMap = this.getYwxtSdxjgMap(req.getKhh(), req.getRq());
        }
        
        // 遍历业务账户列表
        for (TOdsYgtCifTywzh tOdsYgtCifTywzh : list) {
            // 如果业务账户的业务系统为集中交易
            if(tOdsYgtCifTywzh.getYwxt() == Constants.YWZH_YWXT_JZJY) {
                YwzhVo ywzhVo = this.buildYwzhVo(tOdsYgtCifTywzh, yybMap, ywzhztMap);
                builder.jzjy(ywzhVo);
            } else if(tOdsYgtCifTywzh.getYwxt() == Constants.YWZH_YWXT_RZRQ) {
                // 如果业务账户的业务系统为融资融券
                YwzhVo ywzhVo = this.buildYwzhVo(tOdsYgtCifTywzh, yybMap, ywzhztMap);
                // 设置融资融券适当性结果
                ywzhVo.setSdxjg(ywxtSdxjgMap.get(Constants.YWZH_YWXT_RZRQ));
                builder.rzrq(ywzhVo);
            } else if(tOdsYgtCifTywzh.getYwxt() == Constants.YWZH_YWXT_GPQQ) {
                // 如果业务账户的业务系统为股票期权
                YwzhVo ywzhVo = this.buildYwzhVo(tOdsYgtCifTywzh, yybMap, ywzhztMap);
                // 设置股票期权适当性结果
                ywzhVo.setSdxjg(ywxtSdxjgMap.get(Constants.YWZH_YWXT_GPQQ));
                builder.gpqq(ywzhVo);
            } else if(tOdsYgtCifTywzh.getYwxt() == Constants.YWZH_YWXT_CWYW) {
                // 如果业务账户的业务系统为场外业务
                YwzhVo ywzhVo = this.buildYwzhVo(tOdsYgtCifTywzh, yybMap, ywzhztMap);
                builder.cwyw(ywzhVo);
            }
        }
        return builder.build();
    }

    /**
     * 获取业务系统对应的适当性结果映射
     * @param khh 客户号
     * @param rq 日期
     * @return 业务系统与适当性结果的映射
     */
    private Map<Integer, Long> getYwxtSdxjgMap(String khh, Integer rq) {
        Map<Integer, Long> ywxtSdxjgMap = new HashMap<>();
        
        // 查询融资融券的适当性结果
        LambdaQueryWrapper<TsdxjgQtyktyw> rzrqWrapper = new LambdaQueryWrapper<>();
        rzrqWrapper.eq(TsdxjgQtyktyw::getKhh, khh)
                .eq(TsdxjgQtyktyw::getCxywlb, 100)
                .eq(TsdxjgQtyktyw::getRq, rq)
                .orderByDesc(TsdxjgQtyktyw::getRq)
                .last("LIMIT 1");
        TsdxjgQtyktyw rzrqSdx = tsdxjgQtyktywService.getOne(rzrqWrapper);
        if (rzrqSdx != null) {
            ywxtSdxjgMap.put(Constants.YWZH_YWXT_RZRQ, rzrqSdx.getSdxjg());
        }
        
        // 查询股票期权的适当性结果
        LambdaQueryWrapper<TsdxjgQtyktyw> gpqqWrapper = new LambdaQueryWrapper<>();
        gpqqWrapper.eq(TsdxjgQtyktyw::getKhh, khh)
                .eq(TsdxjgQtyktyw::getCxywlb, 200)
                .eq(TsdxjgQtyktyw::getRq, rq)
                .orderByDesc(TsdxjgQtyktyw::getRq)
                .last("LIMIT 1");
        TsdxjgQtyktyw gpqqSdx = tsdxjgQtyktywService.getOne(gpqqWrapper);
        if (gpqqSdx != null) {
            ywxtSdxjgMap.put(Constants.YWZH_YWXT_GPQQ, gpqqSdx.getSdxjg());
        }
        
        return ywxtSdxjgMap;
    }

    private YwzhVo buildYwzhVo(TOdsYgtCifTywzh tOdsYgtCifTywzh, Map<String, Vxtdm> yybMap, Map<String, Vxtdm> ywzhztMap) {
        YwzhVo ywzhVo = new YwzhVo();
        ywzhVo.setYwzh(tOdsYgtCifTywzh.getYwzh());
        ywzhVo.setKhrq(tOdsYgtCifTywzh.getKhrq());
        String yyb = tOdsYgtCifTywzh.getYyb() + "";
        Vxtdm vxtdm = yybMap.get(yyb);
        ywzhVo.setYyb(vxtdm == null? yyb: vxtdm.getNote());
        String zhzt = tOdsYgtCifTywzh.getZhzt() + "";
        vxtdm = ywzhztMap.get(zhzt);
        ywzhVo.setZhzt(vxtdm == null? zhzt: vxtdm.getNote());
        return ywzhVo;
    }

}
