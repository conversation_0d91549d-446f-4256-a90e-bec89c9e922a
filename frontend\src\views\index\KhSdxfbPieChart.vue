<template>
  <div>
    <div :id="chartsId" style=" width:100%; height:180px;margin: 0 0px auto">
    </div>
  </div>

</template>
<script>
import {defineComponent} from 'vue';

let barCharts = [];
export default defineComponent({
  props: ["dataset", "title","colorList"],
  data() {
    return {
      data: [],//[{ value: 735, name: '不适当' }, { value: 310, name: '适当' }, { value: 135, name: '积极'}],
      max: 0,//总数
     // colorList: ['#F7AF52','#55C9FF'],
    }
  },
  methods: {
    initBarChart() {
      let el = document.getElementById(this.chartsId)
      barCharts[this.chartsId] = this.$echarts.init(el);
      this.setOption();
    },
    setOption() {
      let _this = this;
      _this.data = _this.$props.dataset;
      _this.data.forEach(item => {
        _this.max += item.value;
      });
      let option = _this.getOption();
      barCharts[_this.chartsId].setOption(option)

    },
    getOption() {
      let _this = this;
      let option = {
        tooltip: {
          show: true,
          trigger: 'item',
          className: 'myTooltip', // 指定自定义类名
          formatter: function (params) {
            /*const percent = (params.value / _this.max * 100).toFixed(0);
            return params.name + ': ' + params.value;*/
            /*let defaultText = params.marker + params.name + ':\t' + params.value;
            return defaultText + '\t' + params.percent + '%';*/
            return '<div style="width: 150px;padding: 10px;background-color: #D9E1F0;border-radius: 10px;">\n' +
                '            <span style="color: #454A55;">'+params.name+'</span>\n' +
                '            <div style="padding: 0 0 0 10px;background-color: #fff;border-radius: 10px;">\n' +
                '              <span style="color: #454A55;">'+params.value+'</span>\n' +
                '            </div>\n' +
                '          </div>';
          }
        },
        legend: {
          show: false,
          bottom: '0%',
          left: 'center',
        },
        title: {
          show: false,
          text: _this.$props.title + ' 总数量：' + _this.max,
          left: 'center',
          top: 0,
          textStyle: {
            color: '#333333',
            fontSize: 13,
          }
        },
        series: this.getSeriesData(),
      };
      return option;
    },
    getSeriesData() {
      let data = [];
      let _this = this;
      data.push({
        name: _this.$props.title,
        type: 'pie',
        minAngle: 10,//最小的扇区角度（0 ~ 360），用于防止某个值过小导致扇区太小影响交互。
        itemStyle: {
          color: function (params) {
            return {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
             /* colorStops: [{
                offset: 0, color: _this.colorList[params.dataIndex][0] // 起始颜色
              }, {
                offset: 1, color: _this.colorList[params.dataIndex][1] // 结束颜色
              }]*/
              colorStops: [{
                offset: 0, color: _this.$props.colorList[params.dataIndex] // 起始颜色
              }]
            };
          }
        },
        radius: ['40%', '60%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
          formatter: function (params) {
            const percent = (params.value / _this.max * 100).toFixed(0);
            return '{c|' + params.name + ' }' + '\n{a|' + params.value + ' }' + '{b|/' + percent + '%}';
          },
          rich: {
            a: {
              fontSize: '18px',
              color: '#333333',
            },
            b: {
              fontSize: '14px',
              color: '#999999',
            },
            c: {
              fontSize: '14px',
              color: '#333333',
            },
          },
        },
        emphasis: {
          label: {
            show: false,
            formatter: function (params) {
              const percent = (params.value / _this.max * 100).toFixed(0);
              return '{c|' + params.name + ' }' + '\n{a|' + params.value + ' }' + '{b|/' + percent + '%}';
            },
            rich: {
              a: {
                fontSize: '18px',
                color: '#333333',
              },
              b: {
                fontSize: '14px',
                color: '#999999',
              },
              c: {
                fontSize: '14px',
                color: '#333333',
              },
            },
          }
        },
        data: this.data,
      });
      return data;
    },

  },
  mounted() {
    if (this.$props.dataset && this.$props.dataset.length > 0) {
      this.initBarChart()
    }
  },
  computed: {
    chartsId() {
      return "id_" + this.title
    },
  },
  watch: {
    dataset(newValue, oldValue) {
      if (newValue && newValue.length > 0 && JSON.stringify(newValue) != JSON.stringify(oldValue)) {
        if (barCharts[this.chartsId]) {
          this.setOption();
        } else {
          this.initBarChart();
        }
      }
    }
  },
  unmounted() {
    if (barCharts[this.chartsId]) {
      barCharts[this.chartsId].dispose();
      delete barCharts[this.chartsId];
    }
  },
});
</script>

<style scoped>
:deep(.myTooltip){
  padding: 0 !important;
  border-radius: 10px !important;
  border-width: 0px !important;
}
</style>