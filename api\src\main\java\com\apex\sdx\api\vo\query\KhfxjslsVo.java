package com.apex.sdx.api.vo.query;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-03-05
 * @Description:
 */
@Getter
@Setter
public class KhfxjslsVo {

    @LiveProperty(note = "id", index = 1)
    private Long id;

    @LiveProperty(note = "客户号", index = 2)
    private String khh;

    @LiveProperty(note = "警示类别", index = 3)
    private Long jslb;

    @LiveProperty(note = "警示内容", index = 4)
    private String jsnr;

    @LiveProperty(note = "发起方", index = 5)
    private String sdata;

    @LiveProperty(note = "发起渠道", index = 6)
    private String fqqd;

    @LiveProperty(note = "操作日期", index = 7)
    private Integer czrq;

    @LiveProperty(note = "操作时间", index = 8)
    private String czsj;
}
