package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName t_ods_ygt_cif_tpj_wjcs_questions
 */
@TableName(value ="t_ods_ygt_cif_tpj_wjcs_questions")
@Data
public class TOdsYgtCifTpjWjcsQuestions implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 
     */
    private Integer bh;

    /**
     * 
     */
    private Long qid;

    /**
     * 
     */
    private Long jsfs;

    /**
     * 
     */
    private String category;

    /**
     * 
     */
    private String answer;

    /**
     * 
     */
    private Long tpjWjcsId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TOdsYgtCifTpjWjcsQuestions other = (TOdsYgtCifTpjWjcsQuestions) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBh() == null ? other.getBh() == null : this.getBh().equals(other.getBh()))
            && (this.getQid() == null ? other.getQid() == null : this.getQid().equals(other.getQid()))
            && (this.getJsfs() == null ? other.getJsfs() == null : this.getJsfs().equals(other.getJsfs()))
            && (this.getCategory() == null ? other.getCategory() == null : this.getCategory().equals(other.getCategory()))
            && (this.getAnswer() == null ? other.getAnswer() == null : this.getAnswer().equals(other.getAnswer()))
            && (this.getTpjWjcsId() == null ? other.getTpjWjcsId() == null : this.getTpjWjcsId().equals(other.getTpjWjcsId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBh() == null) ? 0 : getBh().hashCode());
        result = prime * result + ((getQid() == null) ? 0 : getQid().hashCode());
        result = prime * result + ((getJsfs() == null) ? 0 : getJsfs().hashCode());
        result = prime * result + ((getCategory() == null) ? 0 : getCategory().hashCode());
        result = prime * result + ((getAnswer() == null) ? 0 : getAnswer().hashCode());
        result = prime * result + ((getTpjWjcsId() == null) ? 0 : getTpjWjcsId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bh=").append(bh);
        sb.append(", qid=").append(qid);
        sb.append(", jsfs=").append(jsfs);
        sb.append(", category=").append(category);
        sb.append(", answer=").append(answer);
        sb.append(", tpjWjcsId=").append(tpjWjcsId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}