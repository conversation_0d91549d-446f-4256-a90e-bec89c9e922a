package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.api.resp.query.KhsdxfbRes;
import com.apex.sdx.api.resp.query.KhzsDateRes;
import com.apex.sdx.core.mybatis.entity.TicYyzb;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tic_yyzb(营运指标)】的数据库操作Mapper
 * @createDate 2025-05-09 14:14:08
 * @Entity com.apex.sdx.core.mybatis.entity.TicYyzb
 */
public interface TicYyzbMapper extends BaseMapper<TicYyzb> {

    List<KhsdxfbRes> khzs(@Param("type") String type, @Param("rq") String rq);

    List<KhzsDateRes> zkhsDate(@Param("type") String type, @Param("rq") String rq, @Param("preRq") String preRq);
}




