<template>
  <!-- 账户信息-->
  <div class="zhxx_top">
    <div class="zhxx_titlebox">
      <span class="zhxx_title">业务账户</span>
    </div>
    <a-table :columns="ywzh_col" :data-source="data" :pagination="false" style="margin-bottom: 30px">
    <!--      <a slot="action" slot-scope="text">action</a>-->
    </a-table>
    <div class="zhxx_titlebox">
      <span class="zhxx_title">资金账户</span>
    </div>
    <a-table :columns="columns" :data-source="data" :scroll="{ x: 1500, y: 300 }" :pagination="false"  style="margin-bottom: 30px">
      <!--      <a slot="action" slot-scope="text">action</a>-->
    </a-table>
    <div class="zhxx_titlebox">
      <span class="zhxx_title">股东账户</span>
    </div>
    <a-table :columns="columns" :data-source="data" :scroll="{ x: 1500, y: 300 }" :showSizeChanger="true">
      <!--      <a slot="action" slot-scope="text">action</a>-->
    </a-table>
    <div class="zhxx_titlebox">
      <span class="zhxx_title">银行账户</span>
    </div>
    <a-table :columns="columns" :data-source="data" :scroll="{ x: 1500, y: 300 }" :pagination="false"  style="margin-bottom: 30px">
      <!--      <a slot="action" slot-scope="text">action</a>-->
    </a-table>
    <div class="zhxx_titlebox">
      <span class="zhxx_title">理财账户</span>
    </div>
    <a-table :columns="columns" :data-source="data" :scroll="{ x: 1500, y: 300 }" :pagination="false">
      <!--      <a slot="action" slot-scope="text">action</a>-->
    </a-table>
  </div>
</template>

<script>
const columns = [
  { title: 'Full Name', width: 100, dataIndex: 'name', key: 'name', fixed: 'left' },
  { title: 'Age', width: 100, dataIndex: 'age', key: 'age', fixed: 'left' },
  { title: 'Column 1', dataIndex: 'address', key: '1', width: 150 },
  { title: 'Column 2', dataIndex: 'address', key: '2', width: 150 },
  { title: 'Column 3', dataIndex: 'address', key: '3', width: 150 },
  { title: 'Column 4', dataIndex: 'address', key: '4', width: 150 },
  { title: 'Column 5', dataIndex: 'address', key: '5', width: 150 },
  { title: 'Column 6', dataIndex: 'address', key: '6', width: 150 },
  { title: 'Column 7', dataIndex: 'address', key: '7', width: 150 },
  { title: 'Column 8', dataIndex: 'address', key: '8' },
];

const ywzh_col = [
  {title: '业务系统', width: 100, dataIndex: 'ywxt', key: 'ywxt', align: 'center'},
  {title: '业务账户', width: 100, dataIndex: 'ywzh', key: 'ywzh', align: 'center'},
  {title: '营业部', width: 100, dataIndex: 'yyb', key: 'yyb', align: 'center'},
  {title: '客户群组', width: 100, dataIndex: 'khqz', key: 'khqz', align: 'center'},
  {title: '账号状态', width: 100, dataIndex: 'zhzt', key: 'zhzt', align: 'center'},
  {title: '开户日期', width: 100, dataIndex: 'khrq', key: 'khrq', align: 'center'},
]

const data = [];
for (let i = 0; i < 10; i++) {
  data.push({
    key: i,
    name: `Edrward ${i}`,
    age: 32,
    address: `London Park no. ${i}`,
  });
}
export default {
  name: "dhcx_zhxx",
  props: ['khh'],
  data() {
    return {
      data,
      columns,
      ywzh_col,
    };
  },

  methods:{
    onSearch() {

    }
  }
}
</script>

<style scoped>
.zhxx_top{
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 12px !important;
  margin-top:-4px
}

.zhxx_title{
  font-size: 16px;
  color: #666;
}

.zhxx_title:before{
  width: 5px;
  height: 20px;
  margin-right: 12px;
  margin-left: 12px;
  color: transparent;
  background: #1890ff;
  border-radius: 5px;
  content: ":";
}

.zhxx_titlebox{
  border-bottom: 1px solid #e4e4e4;
  padding-bottom: 10px;
}
</style>
