package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName t_ods_ygt_cif_tkhjyqx
 */
@TableName(value ="t_ods_ygt_cif_tkhjyqx", schema = "ods")
@Data
public class TOdsYgtCifTkhjyqx implements Serializable {
    /**
     * 
     */
    private String khh;

    /**
     * 
     */
    private Integer ywxt;

    /**
     * 
     */
    private String ywzh;

    /**
     * 数据字典: GT_JYS
     */
    private Integer jys;

    /**
     * 
     */
    private String gdh;

    /**
     * 
     */
    private Long jyqx;

    /**
     * 
     */
    private String sx1;

    /**
     * 
     */
    private String sx2;

    /**
     * 
     */
    private Long yyb;

    /**
     * 0-正常;3-关闭
     */
    private Integer zt;

    /**
     * 
     */
    private Integer ktrq;

    /**
     * 
     */
    private Integer gbrq;

    /**
     * 
     */
    private Long ywqqid;

    /**
     * 
     */
    private String zqdm;

    /**
     * 
     */
    private String zqmc;

    /**
     * 
     */
    private Integer qxyxq;

    /**
     * 
     */
    private String hmbs;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TOdsYgtCifTkhjyqx other = (TOdsYgtCifTkhjyqx) that;
        return (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
            && (this.getYwxt() == null ? other.getYwxt() == null : this.getYwxt().equals(other.getYwxt()))
            && (this.getYwzh() == null ? other.getYwzh() == null : this.getYwzh().equals(other.getYwzh()))
            && (this.getJys() == null ? other.getJys() == null : this.getJys().equals(other.getJys()))
            && (this.getGdh() == null ? other.getGdh() == null : this.getGdh().equals(other.getGdh()))
            && (this.getJyqx() == null ? other.getJyqx() == null : this.getJyqx().equals(other.getJyqx()))
            && (this.getSx1() == null ? other.getSx1() == null : this.getSx1().equals(other.getSx1()))
            && (this.getSx2() == null ? other.getSx2() == null : this.getSx2().equals(other.getSx2()))
            && (this.getYyb() == null ? other.getYyb() == null : this.getYyb().equals(other.getYyb()))
            && (this.getZt() == null ? other.getZt() == null : this.getZt().equals(other.getZt()))
            && (this.getKtrq() == null ? other.getKtrq() == null : this.getKtrq().equals(other.getKtrq()))
            && (this.getGbrq() == null ? other.getGbrq() == null : this.getGbrq().equals(other.getGbrq()))
            && (this.getYwqqid() == null ? other.getYwqqid() == null : this.getYwqqid().equals(other.getYwqqid()))
            && (this.getZqdm() == null ? other.getZqdm() == null : this.getZqdm().equals(other.getZqdm()))
            && (this.getZqmc() == null ? other.getZqmc() == null : this.getZqmc().equals(other.getZqmc()))
            && (this.getQxyxq() == null ? other.getQxyxq() == null : this.getQxyxq().equals(other.getQxyxq()))
            && (this.getHmbs() == null ? other.getHmbs() == null : this.getHmbs().equals(other.getHmbs()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getYwxt() == null) ? 0 : getYwxt().hashCode());
        result = prime * result + ((getYwzh() == null) ? 0 : getYwzh().hashCode());
        result = prime * result + ((getJys() == null) ? 0 : getJys().hashCode());
        result = prime * result + ((getGdh() == null) ? 0 : getGdh().hashCode());
        result = prime * result + ((getJyqx() == null) ? 0 : getJyqx().hashCode());
        result = prime * result + ((getSx1() == null) ? 0 : getSx1().hashCode());
        result = prime * result + ((getSx2() == null) ? 0 : getSx2().hashCode());
        result = prime * result + ((getYyb() == null) ? 0 : getYyb().hashCode());
        result = prime * result + ((getZt() == null) ? 0 : getZt().hashCode());
        result = prime * result + ((getKtrq() == null) ? 0 : getKtrq().hashCode());
        result = prime * result + ((getGbrq() == null) ? 0 : getGbrq().hashCode());
        result = prime * result + ((getYwqqid() == null) ? 0 : getYwqqid().hashCode());
        result = prime * result + ((getZqdm() == null) ? 0 : getZqdm().hashCode());
        result = prime * result + ((getZqmc() == null) ? 0 : getZqmc().hashCode());
        result = prime * result + ((getQxyxq() == null) ? 0 : getQxyxq().hashCode());
        result = prime * result + ((getHmbs() == null) ? 0 : getHmbs().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", khh=").append(khh);
        sb.append(", ywxt=").append(ywxt);
        sb.append(", ywzh=").append(ywzh);
        sb.append(", jys=").append(jys);
        sb.append(", gdh=").append(gdh);
        sb.append(", jyqx=").append(jyqx);
        sb.append(", sx1=").append(sx1);
        sb.append(", sx2=").append(sx2);
        sb.append(", yyb=").append(yyb);
        sb.append(", zt=").append(zt);
        sb.append(", ktrq=").append(ktrq);
        sb.append(", gbrq=").append(gbrq);
        sb.append(", ywqqid=").append(ywqqid);
        sb.append(", zqdm=").append(zqdm);
        sb.append(", zqmc=").append(zqmc);
        sb.append(", qxyxq=").append(qxyxq);
        sb.append(", hmbs=").append(hmbs);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}