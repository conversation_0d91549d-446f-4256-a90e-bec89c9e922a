package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName t_ods_ygt_cif_tpj_khdcwj_answers
 */
@TableName(value ="t_ods_ygt_cif_tpj_khdcwj_answers")
@Data
public class TOdsYgtCifTpjKhdcwjAnswers implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 
     */
    private Long paperqid;

    /**
     * 
     */
    private String answer;

    /**
     * 
     */
    private Long tpjKhdcwjId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TOdsYgtCifTpjKhdcwjAnswers other = (TOdsYgtCifTpjKhdcwjAnswers) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getPaperqid() == null ? other.getPaperqid() == null : this.getPaperqid().equals(other.getPaperqid()))
            && (this.getAnswer() == null ? other.getAnswer() == null : this.getAnswer().equals(other.getAnswer()))
            && (this.getTpjKhdcwjId() == null ? other.getTpjKhdcwjId() == null : this.getTpjKhdcwjId().equals(other.getTpjKhdcwjId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPaperqid() == null) ? 0 : getPaperqid().hashCode());
        result = prime * result + ((getAnswer() == null) ? 0 : getAnswer().hashCode());
        result = prime * result + ((getTpjKhdcwjId() == null) ? 0 : getTpjKhdcwjId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", paperqid=").append(paperqid);
        sb.append(", answer=").append(answer);
        sb.append(", tpjKhdcwjId=").append(tpjKhdcwjId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}