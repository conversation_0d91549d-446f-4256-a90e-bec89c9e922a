package sdx.query;

import com.apex.sdx.api.req.khgl.KhzlbgmxcxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.vo.khgl.KhzlbgmxVo;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR>
 * @Date 2025-02-13
 * @Description:
 */
public interface IKhzlbgService {
    @LiveMethod(paramAsRequestBody = true, note = "客户资料变更明细查询")
    QueryPageResponse<KhzlbgmxVo> khzlbgmxcx(KhzlbgmxcxReq req) throws Exception;;
}
