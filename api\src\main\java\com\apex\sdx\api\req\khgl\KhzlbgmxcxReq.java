package com.apex.sdx.api.req.khgl;

import com.apex.sdx.api.req.common.PageRequest;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025-02-13
 * @Description:
 */
@Getter
@Setter
public class KhzlbgmxcxReq extends PageRequest {
    @NotNull(message = "不能为空")
    @LiveProperty(note = "客户号", index = 1)
    private String khh;

    @LiveProperty(note = "开始日期", index = 2)
    private Integer ksrq;

    @LiveProperty(note = "结束日期", index = 3)
    private Integer jsrq;
}
