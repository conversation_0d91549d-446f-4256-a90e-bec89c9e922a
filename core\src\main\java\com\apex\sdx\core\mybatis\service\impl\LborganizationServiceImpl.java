package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.core.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.Lborganization;
import com.apex.sdx.core.mybatis.service.LborganizationService;
import com.apex.sdx.core.mybatis.mapper.LborganizationMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class LborganizationServiceImpl extends ServiceImpl<LborganizationMapper, Lborganization>
        implements LborganizationService {

    @Override
    public List<Lborganization> queryYybList(String orgcode) {
        LambdaQueryWrapper<Lborganization> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(Lborganization::getId, Lborganization::getName, Lborganization::getFid, Lborganization::getGrade, Lborganization::getType, Lborganization::getFdncode, Lborganization::getOrgcode, Lborganization::getOrgtype);
        wrapper.eq(orgcode != null, Lborganization::getOrgcode, orgcode);

        try {
            return this.list(wrapper);
        } catch (Exception e) {
            String note = String.format("查询营业部列表失败：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }
}




