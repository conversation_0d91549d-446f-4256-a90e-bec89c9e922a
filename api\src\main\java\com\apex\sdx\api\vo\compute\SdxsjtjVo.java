package com.apex.sdx.api.vo.compute;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> wu<PERSON><PERSON><PERSON>
 * @create 2025/4/28 13:50
 */
@Setter
@Getter
public class SdxsjtjVo {


    @LiveProperty(note = "事件编码", index = 1)
    private String sjbm;

    @LiveProperty(note = "事件名称", index = 2)
    private String sjmc;

    @LiveProperty(note = "事件ID", index = 3)
    private Integer sjid;

    @LiveProperty(note = "事件数量", index = 4)
    private Integer sjsl;

    @LiveProperty(note = "处理数量", index = 5)
    private Integer clsl;

    @LiveProperty(note = "处理占比", index = 6)
    private double clzb;

    @LiveProperty(note = "待处理数量", index = 7)
    private Integer dclsl;

    @LiveProperty(note = "待处理占比", index = 8)
    private double dclzb;
}
