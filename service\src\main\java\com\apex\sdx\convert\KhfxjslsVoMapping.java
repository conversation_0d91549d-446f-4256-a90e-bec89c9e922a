package com.apex.sdx.convert;

import com.apex.sdx.api.vo.query.KhfxjslsVo;
import com.apex.sdx.core.converter.IMapping;
import com.apex.sdx.core.converter.MapStructConfig;
import com.apex.sdx.core.mybatis.entity.Tkhfxjsls;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025-03-05
 * @Description:
 */
@Mapper(config = MapStructConfig.class)
@Component
public interface KhfxjslsVoMapping extends IMapping<KhfxjslsVo, Tkhfxjsls> {
}
