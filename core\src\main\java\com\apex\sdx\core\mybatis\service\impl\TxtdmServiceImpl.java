package com.apex.sdx.core.mybatis.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.mybatis.entity.Txtdm;
import com.apex.sdx.core.mybatis.mapper.TxtdmMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.service.TxtdmService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Service
public class TxtdmServiceImpl extends ServiceImpl<TxtdmMapper, Txtdm>
        implements TxtdmService {

    @Resource
    TxtdmService txtdmService;

    @Override
    @Cacheable(cacheNames = "dict_xtdm", key = "#fldm", unless = "#result.size == 0")
    public List<Txtdm> queryDict(String fldm) {
        return baseMapper.queryDict(fldm);
    }

    @Override
    public Map<String, Object> getDictMap(String fldm, String keyField, String valueField) {
        Map<String, Object> mapDict = new HashMap<>();
        List<Txtdm> txtdms = txtdmService.queryDict(fldm);
        for (Txtdm obj : txtdms) {
            Map<String, Object> map = BeanUtil.beanToMap(obj);
            mapDict.put(map.get(keyField).toString(), map.get(valueField).toString());
        }
        return mapDict;
    }

    @Override
    public String getNoteByIbm(String fldm, String ibm) {
        List<Txtdm> txtdms = txtdmService.queryDict(fldm);
        Txtdm txtdm = txtdms.stream().filter(xtdm -> xtdm.getIbm().equals(ibm)).findFirst().orElse(null);
        return txtdm == null ? "" : txtdm.getNote();
    }

    @CacheEvict(cacheNames = "dict_xtdm", allEntries = true)
    @Override
    public void refreshAll() {

    }

    @Override
    public String getIbmByCbm(String fldm, String cbm) {
        QueryWrapper<Txtdm> wrapper = new QueryWrapper<>();
        wrapper.eq("cbm", cbm);
        wrapper.eq("fldm", fldm);
        try {
            Txtdm xtdm = this.getOne(wrapper);
            return xtdm == null ? "" : xtdm.getIbm().toString();
        }catch (Exception e){
            String note = String.format("获取ibm失败:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-605, note);
        }
    }
}




