package com.apex.sdx.api.req.query;

import com.apex.sdx.api.req.common.PageRequest;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-02-14
 * @Description:
 */
@Getter
@Setter
public class GmsfzyzsqcxReq extends PageRequest {

    @LiveProperty(note = "客户姓名", index = 1)
    private String khxm;

    @LiveProperty(note = "证件类别", index = 2)
    private Integer zjlb;

    @LiveProperty(note = "证件编号", index = 3)
    private String zjbh;

    @LiveProperty(note = "开始日期", index = 4)
    private Integer ksrq;

    @LiveProperty(note = "结束日期", index = 5)
    private Integer jsrq;

    @LiveProperty(note = "姓名核查结果", index = 6)
    private String xmhcjg;

    @LiveProperty(note = "证件核查结果", index = 7)
    private String zjhcjg;

    @LiveProperty(note = "人像比对结果", index = 8)
    private String rxbdjg;

    @LiveProperty(note = "查询类型", index = 9)
    private Integer cxlx;

    @LiveProperty(note = "操作类别", index = 10)
    private Integer czlb;

}
