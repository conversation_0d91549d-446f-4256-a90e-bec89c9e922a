package com.apex.sdx.convert;

import com.apex.sdx.api.vo.xtcs.XtdmVo;
import com.apex.sdx.core.converter.IMapping;
import com.apex.sdx.core.converter.MapStructConfig;
import com.apex.sdx.core.mybatis.entity.Vxtdm;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

/**
 * 操作明细对象转换器
 *
 * <AUTHOR>
 * @date 2023/3/27
 */
@Mapper(config = MapStructConfig.class)
@Component
public interface XtdmVoMapping extends IMapping<XtdmVo, Vxtdm> {

}
