<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.TLdTradingCalendarMapper">

    <resultMap id="BaseResultMap" type="com.apex.sdx.core.mybatis.entity.TLdTradingCalendar">
            <id property="zrr" column="ZRR" />
            <id property="jyr" column="JYR" />
            <result property="nd" column="ND" />
            <result property="jd" column="JD" />
            <result property="yfXh" column="YF_XH" />
            <result property="xq" column="XQ" />
            <result property="ny" column="NY" />
            <result property="nz" column="NZ" />
            <result property="xqXh" column="XQ_XH" />
    </resultMap>

    <sql id="Base_Column_List">
        ZRR,JYR,ND,JD,YF_XH,XQ,
        NY,NZ,XQ_XH
    </sql>
</mapper>
