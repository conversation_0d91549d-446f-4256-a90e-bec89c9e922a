package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.api.vo.compute.QtyktywtjVo;
import com.apex.sdx.core.mybatis.entity.TsdxjgQtyktyw;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;


/**
 *
 */
public interface TsdxjgQtyktywService extends IService<TsdxjgQtyktyw> {

    /**
     *
     * @param khh
     * @param onlysdx
     * @param rq
     * @param isSearchCount
     * @param pagesize
     * @param pagenum
     * @return
     */
    Page<TsdxjgQtyktyw> queryPageByKhhAndSdxjg(String khh, boolean onlysdx, String rq, boolean isSearchCount, int pagesize, int pagenum);

    /**
     * 统计
     * @param khh
     * @param cxywlb
     * @param rq
     * @return
     */
    Page<QtyktywtjVo> compute4cxywlb(String khh, String cxywlb, String rq, boolean isSearchCount, int pagesize, int pagenum);

    /**
     * 统计业务不适当协议签署情况
     * @param rq
     * @return
     */
    List<QtyktywtjVo> computeByBsdx(String rq);

    /**
     * 统计业务不适当协议签署明细情况
     * @param khh
     * @param cxywlb
     * @param qsbsdxy
     * @param rq
     * @param isSearchCount
     * @param pagesize
     * @param pagenum
     * @return
     */
    Page<QtyktywtjVo> computemxBySdx(String khh, String cxywlb, String qsbsdxy, String rq, boolean isSearchCount, int pagesize, int pagenum);

    /**
     * 单客户业务适当性统计
     * @param khh
     * @param rq
     * @return
     */
    List<QtyktywtjVo> compute(String khh, String rq);
}
