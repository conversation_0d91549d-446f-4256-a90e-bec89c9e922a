<template>
  <div class="dxsdx_zhsdx_nav">
    <div class="dxsdx_khqjgk_title">证明文件</div>
    <!--    <div class="dxsdx_cxtj">
          <div class="cxtj_item"><span>申请日期</span><input type="text" placeholder="开始日期 - 结束日期"/></div>
          <div class="cxtj_item"><a class="btn">查询</a><a class="btn fz">重置</a></div>
        </div>-->
    <div class="dxsdx_zmwj_nav">
      <div class="zmwj_left">
        <div class="zmwj_left_wjlx">
          <p>证明文件列表</p>
          <ul class="innerbox2">
            <li v-for="(item, index) in zmwjList"
                :key="index"
                :class="{ cur: activeIndex === index }"
                @click="setActive(index, item)"
            >
              {{ item.zmwj }}
            </li>
          </ul>
        </div>
      </div>
      <div class="zmwj_right">
        <div class="zmwj_show">
          <a-carousel arrows style="height: 100%">
            <template #prevArrow>
              <div class="custom-slick-arrow" style="left: 10px; z-index: 1">
                <div class="show_pic_reg"></div>
              </div>
            </template>
            <template #nextArrow>
              <div class="custom-slick-arrow" style="right: 10px">
                <div class="show_pic_next"></div>
              </div>
            </template>
            <div
                v-for="(image, index) in imageList"
                :key="'base64-'+index"
                class="base64-item"
                style="height: 650px; display: flex; align-items: center; justify-content: center;"
            >
              <a-image
                  v-if="image.status === 1"
                  :src="`data:${image.contentType};base64,${image.base64Image}`"
                  :alt="`Base64图片 ${index + 1}`"
              />
              <a-image
                  v-else
                  :width="400"
                  :height="400"
                  :src="`data:image/png;base64,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`"
              />
            </div>
          </a-carousel>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {defineComponent, inject} from "vue";
import {commonApi} from "@/api/common";
import {message} from "ant-design-vue";

export default defineComponent({
  name: "khzmcl",
  inject: ["khh"],
  data() {
    return {
      data: [],
      activeIndex: 0,
      zmwjList: [],
      tokenList: [],
      imageList:[],
    };
  },
  mounted() {
    this.getZmwjyxsj();
  },
  methods: {
    getZmwjyxsj() {
      this.loading = true;
      commonApi.executeAMS(
          "sdx.query.IZmwjcxService",
          "zmwjyxsjcx",
          {
            khh: this.khh,
          },
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.data = res.records || [];
        this.zmwjList = this.filterUniqueById(this.data);

        // 如果存在数据则默认显示第一项
        if (this.zmwjList && this.zmwjList.length > 0) {
          this.setActive(0, this.zmwjList[0]);
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    getImageList(){
      console.log(this.tokenList);
      commonApi.executeAMS(
          "sdx.query.IZmwjcxService",
          "getBatchImages",
          {
            token: this.tokenList,
          },
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.imageList = res.records || [];
      }).finally(() => {
        this.loading = false;
      });
    },
    setActive(index, zmwj) {
      this.activeIndex = index;
      this.tokenList = this.data.filter(item => item.zmwjid === zmwj.zmwjid).map(item => item.filepath);
      this.getImageList();
    },
    filterUniqueById(list) {
      return list.filter((item, index, self) =>
          index === self.findIndex(t => t.zmwjid === item.zmwjid)
      );
    }
  },
});
</script>
<style scoped>
.dxsdx_zhsdx_nav {
  height: calc(100%);
  border-radius: 8px;
  background-color: #fff;
  overflow-y: auto;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.dxsdx_khqjgk_title {
  line-height: 52px;
  margin: 0 10px;
  border-bottom: 1px solid #eee;
  padding-left: 8px;
  font-size: 14px;
}

.dxsdx_khqjgk_title:before {
  display: inline-block;
  vertical-align: middle;
  content: "";
  width: 4px;
  height: 18px;
  margin-top: -2px;
  border-radius: 2px;
  background-color: #b48a3b;
  margin-right: 10px;
}

.dxsdx_cxtj {
  line-height: 32px;
  padding: 15px 5px 3px 10px;
}

.dxsdx_cxtj .cxtj_item {
  white-space: nowrap;
  margin-bottom: 12px;
  font-size: 14px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 20px;
}

.dxsdx_cxtj .cxtj_item span {
  color: #888;
  display: inline-block;
  margin-right: 15px;
  vertical-align: middle;
}

.dxsdx_cxtj .cxtj_item input[type=text] {
  display: inline-block;
  vertical-align: middle;
  width: 340px;
  height: 32px;
  line-height: 30px;
  border: 1px solid #d6d6d6;
  border-radius: 4px;
  padding: 0 10px;
}

.dxsdx_cxtj .cxtj_item input[type=text]:focus {
  outline: 1px solid #d0ad6b;
}

a.btn {
  min-width: 80px;
  padding: 0 10px;
  margin-right: 10px;
  text-align: center;
  line-height: 32px;
  border-radius: 4px;
  background-color: #f6e5d1;
  color: #bf935f;
  cursor: pointer;
  display: inline-block;
  vertical-align: middle;
}

a.btn.fz {
  background-color: #fff;
  color: #777;
  border: 1px solid #d6d6d6;
  line-height: 30px;
}

a.btn:hover, a.btn.fz:Hover {
  background-color: #bf935f;
  color: #fff;
  border: none;
  line-height: 32px;
}

.dxsdx_zmwj_nav {
  width: calc(100%);
  height: calc(100% - 70px);
  padding: 0 10px;
}

.zmwj_left {
  width: 285px;
  display: inline-block;
  vertical-align: top;
  height: calc(100%);
  background-color: #fff;
  border: 1px solid #e7eaec;
  border-radius: 8px;
}

.zmwj_left_wjlx {
  height: calc(100%);
}

.zmwj_left_wjlx p {
  line-height: 50px;
  border-bottom: 1px solid #e7eaec;
  color: #333;
  padding-left: 20px;
  font-size: 14px;
}

.zmwj_left_wjlx ul {
  height: calc(100% - 51px);
  display: inline-block;
  overflow-y: auto;
  width: 100%;
}

.zmwj_left_wjlx ul li {
  margin: 8px 8px;
  padding-left: 12px;
  padding-top: 6px;
  padding-bottom: 6px;
  line-height: 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.zmwj_left_wjlx ul li:hover {
  background-color: #f8f9fa;
}

.zmwj_left_wjlx ul li.cur {
  background-color: #fbf4ec;
  color: #b48a3b;
}

.zmwj_right {
  height: calc(100%);
  width: calc(100% - 300px);
  display: inline-block;
  vertical-align: top;
  margin-left: 15px;
  border: 1px solid #e7eaec;
  border-radius: 8px;
}

.zmwj_right_li {
  width: 270px;
  height: calc(100%);
  display: inline-block;
  vertical-align: top;
  background-color: #fdfdfd;
  border-right: 1px solid #e7eaec;
  border-radius: 8px 0 0 8px;
}

.zmwj_right_li ul {
  height: calc(100% - 30px);
  overflow-y: auto;
}

.zmwj_right_li ul li {
  cursor: pointer;
  margin: 8px 5px;
  border-radius: 4px;
  border: 1px solid #eee;
  background-color: #fff;
  padding: 10px 5px 10px 15px;
  line-height: 20px;
  font-size: 14px;
  color: #333;
}

.zmwj_right_li ul li:hover {
  background-color: #f8f9fa;
}

.zmwj_right_li ul li.cur, .zmwj_right_li ul li.cur:hover {
  border: 1px solid #f6e5d1;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.zmwj_right_li ul li p:nth-child(2) {
  margin-top: 5px;
}

.zmwj_right_li ul li p:nth-child(2) span {
  color: #aaa;
  display: inline-block;
  font-size: 12px;
  margin-right: 10px;
}

.fy {
  padding: 5px 0 5px 5px;
  line-height: 30px;
  font-size: 14px;
  color: #333;
}

.fy.small {
  padding: 3px 0 3px 5px;
  line-height: 20px;
  font-size: 12px;
}

.fy span {
  display: inline-block;
  vertical-align: middle;
  margin-right: 5px;
  color: #666;
}

.fy span:nth-child(2) {
  float: right;
}

.fy a {
  display: inline-block;
  vertical-align: middle;
  height: 30px;
  width: 30px;
  color: #888;
  text-align: center;
  border: 1px solid #eee;
  margin: 0 6px;
  line-height: 30px;
  border-radius: 2px;
  cursor: pointer;
}

.fy.small a {
  height: 20px;
  width: 20px;
  line-height: 18px;
  margin: 0 4px;
}

.fy a.reg_page:before {
  content: "\e647";
  display: inline-block;
  transform: rotate(180deg);
  vertical-align: middle;
  font-size: 14px;
  font-family: "iconfont" !important;
}

.fy a.next_page:before {
  content: "\e647";
  display: inline-block;
  vertical-align: middle;
  font-size: 16px;
  font-family: "iconfont" !important;
}

.fy a:hover {
  color: #b48a3b;
}

.fy a.cur {
  background-color: #fbf4ec;
  color: #b48a3b;
}

.zmwj_show {
  display: inline-block;
  vertical-align: top;
  position: relative;
  width: calc(100%);
  height: calc(100%);
  border-radius: 0 8px 8px 0;
  background-color: #f8f9fa;
}

.zmwj_show .zmwj_show_title {
  line-height: 50px;
  padding: 5px;
  font-size: 18px;
  text-align: center;
}

.zmwj_show .zmwj_show_pic {
  height: calc(100% - 120px);
  overflow: hidden;
  width: calc(100%);
  text-align: center;
}

.zmwj_show .show_pic_reg {
  color: #aaa;
  cursor: pointer;
  height: 40px;
  line-height: 40px;
}

.zmwj_show .show_pic_reg:before {
  content: "\e892";
  font-size: 30px;
  font-family: "iconfont" !important;
}

.zmwj_show .show_pic_reg:hover {
  color: #b48a3b;
}

.zmwj_show .show_pic_next {
  color: #aaa;
  cursor: pointer;
  height: 40px;
  line-height: 40px;
}

.zmwj_show .show_pic_next:before {
  content: "\e73d";
  font-size: 30px;
  font-family: "iconfont" !important;
}

.zmwj_show .show_pic_next:hover {
  color: #b48a3b;
}

.zmwj_show_cz {
  line-height: 50px;
  padding: 5px;
  text-align: center;
}

.zmwj_show_cz a {
  display: inline-block;
  height: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  margin: 6px 22px;
  cursor: pointer;
  color: #888;
  font-size: 24px;
}

.zmwj_show_cz a:before {
  font-family: "iconfont" !important;
}

.zmwj_show_cz a.pic_fd:before {
  content: "\e643";
}

.zmwj_show_cz a.pic_sx:before {
  content: "\e644";
}

.zmwj_show_cz a.pic_xz:before {
  content: "\e944";
}

.zmwj_show_cz a.pic_dy:before {
  content: "\e765";
}

.zmwj_show_cz a:hover {
  color: #b48a3b;
}

:deep(.ant-carousel) {
  height: 100%;
}

:deep(.ant-carousel .slick-slider) {
  height: 100%;
  text-align: center;
  padding: 0 40px;
}


:deep(.slick-list) {
  height: 100%;
}

:deep(.ant-carousel .slick-track) {
  height: 100%;
}

:deep(.ant-carousel .slick-dots li.slick-active button) {
  background: #b48a3b;
}

:deep(.ant-carousel .slick-dots li button) {
  background: #8f99a8;
}

.base64-item {
  width: 100%;
  height: 100%;
  padding: 60px 20px 0 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>