<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.TsdxSjMapper">

    <resultMap id="BaseResultMap" type="com.apex.sdx.core.mybatis.entity.TsdxSj">
        <id property="id" column="ID" jdbcType="DECIMAL"/>
        <result property="scrq" column="SCRQ" jdbcType="DECIMAL"/>
        <result property="scsj" column="SCSJ" jdbcType="VARCHAR"/>
        <result property="khh" column="KHH" jdbcType="VARCHAR"/>
        <result property="yyb" column="YYB" jdbcType="DECIMAL"/>
        <result property="sjlx" column="SJLX" jdbcType="DECIMAL"/>
        <result property="sjbm" column="SJBM" jdbcType="VARCHAR"/>
        <result property="sjxq" column="SJXQ" jdbcType="VARCHAR"/>
        <result property="tzfs" column="TZFS" jdbcType="DECIMAL"/>
        <result property="tzrq" column="TZRQ" jdbcType="DECIMAL"/>
        <result property="tzsj" column="TZSJ" jdbcType="VARCHAR"/>
        <result property="tzzt" column="TZZT" jdbcType="DECIMAL"/>
        <result property="clzt" column="CLZT" jdbcType="DECIMAL"/>
        <result property="tznr" column="TZNR" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,SCRQ,SCSJ,
        KHH,YYB,SJLX,
        SJBM,SJXQ,TZFS,
        TZRQ,TZSJ,TZZT,
        CLZT,TZNR
    </sql>


    <select id="query4sdxsj" resultType="com.apex.sdx.api.vo.query.SdxsjVo">
        select
            A.ID,
            A.SCRQ,
            A.SCSJ,
            A.KHH,
            A.YYB,
            A.SJXQ,
            A.CLZT,
            B.SJMC
        from
            SDX.TSDX_SJ A
        left join SDX.TSDX_SJCS B on
            A.SJID = B.ID
        <where>
            a.khh = #{khh}
        <if test="ksrq!= null and ksrq!=''">
            AND SCRQ &gt;= #{ksrq}
        </if>
        <if test="jsrq!= null and jsrq!=''">
            AND SCRQ &lt;= #{jsrq}
        </if>
        <if test="clzt!= null and clzt != ''">
            AND a.CLZT = #{clzt}
        </if>
        </where>
        order by
            A.SCRQ,
            A.SCSJ desc
    </select>
    <select id="querySdxsjDesc" resultType="com.apex.sdx.api.vo.query.SdxsjDescVo">
        select sjys.sjlx,
               sjys.ysdm,
               sjys.ysmc,
               sjys.fysdm,
               sjys.bzsm,
               sj.sjxq
        from tsdx_sjcs_sjys sjys
                 left join tsdx_sjcs sjcs on
            sjcs.id = sjys.tsdx_sjcs_id
                 left join tsdx_sj sj on
            sjys.tsdx_sjcs_id = sj.sjid
        where sj.id = #{sjid}
        order by sjys.px
    </select>
    <select id="compute" resultType="com.apex.sdx.api.vo.compute.SdxsjtjVo">
        SELECT ifnull(sjsl,0)                    AS sjsl
        , ifnull(clsl,0)                         AS clsl
        , ifnull(clsl / sjsl,0)                  AS clzb
        , ifnull(dclsl,0)                        AS dclsl
        , ifnull(dclsl / sjsl,0)                 AS dclzb
        , ROW_NUMBER() OVER (ORDER BY sjsl desc) AS px
        FROM (SELECT COUNT(1)                                  AS sjsl
                    ,SUM(CASE WHEN CLZT = 1 THEN 1 ELSE 0 END) AS clsl
                    ,SUM(CASE WHEN CLZT = 0 THEN 1 ELSE 0 END) AS dclsl
            FROM SDX.TSDX_SJ
            <where>
            <!-- 其他条件 -->
            <if test="khh!= null and khh != ''">
                AND khh = #{khh}
            </if>
            <if test="ksrq!= null and ksrq!=''">
                AND SCRQ &gt;= #{ksrq}
            </if>
            <if test="jsrq!= null and jsrq!=''">
                AND SCRQ &lt;= #{jsrq}
            </if>
            </where>
            ) AS sdxsj
        <where>
            <!-- 其他条件 -->
            <if test="sjbm!= null and sjbm!= ''">
                AND sjbm = #{sjbm}
            </if>
        </where>
    </select>

    <select id="compute4Sdxsj" resultType="com.apex.sdx.api.vo.compute.SdxsjtjVo">
        SELECT ts.id                             AS sjid
        , sjbm
        , sjmc
        , ifnull(sjsl,0)                         AS sjsl
        , ifnull(clsl,0)                         AS clsl
        , ifnull(clsl / sjsl,0)                  AS clzb
        , ifnull(dclsl,0)                        AS dclsl
        , ifnull(dclsl / sjsl,0)                 AS dclzb
        FROM (SELECT sjid
                ,COUNT(1)                                  AS sjsl
                ,SUM(CASE WHEN CLZT = 1 THEN 1 ELSE 0 END) AS clsl
                ,SUM(CASE WHEN CLZT = 0 THEN 1 ELSE 0 END) AS dclsl
            FROM SDX.TSDX_SJ
            <where>
            <!-- 其他条件 -->
            <if test="khh!= null and khh != ''">
                AND khh = #{khh}
            </if>
            <if test="ksrq!= null and ksrq!=''">
                AND SCRQ &gt;= #{ksrq}
            </if>
            <if test="jsrq!= null and jsrq!=''">
                AND SCRQ &lt;= #{jsrq}
            </if>
            </where>
            GROUP BY sjid) AS sdxsj
        RIGHT JOIN tsdx_sjcs ts on sdxsj.sjid = ts.ID
        <where>
            <!-- 其他条件 -->
            <if test="sjbm!= null and sjbm!= ''">
                AND sjbm = #{sjbm}
            </if>
        </where>
        ORDER BY dclsl desc
    </select>
</mapper>
