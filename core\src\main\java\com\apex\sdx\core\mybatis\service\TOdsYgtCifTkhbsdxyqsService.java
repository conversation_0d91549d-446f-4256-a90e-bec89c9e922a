package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.api.vo.query.QxbsdxyqslscxVo;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhbsdxyqs;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_ods_ygt_cif_tkhbsdxyqs】的数据库操作Service
 * @createDate 2025-06-06 16:59:26
 */
public interface TOdsYgtCifTkhbsdxyqsService extends IService<TOdsYgtCifTkhbsdxyqs> {

    List<QxbsdxyqslscxVo> qxbsdxyqslscx(String khh,String ksrq, String jsrq, String cxywlb);

}
