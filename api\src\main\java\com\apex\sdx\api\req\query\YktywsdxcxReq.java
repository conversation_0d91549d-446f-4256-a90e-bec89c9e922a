package com.apex.sdx.api.req.query;

import com.apex.sdx.api.req.common.PageRequest;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-03-05
 * @Description:
 */
@Setter
@Getter
public class YktywsdxcxReq extends PageRequest {
    @LiveProperty(note = "客户号", index = 1)
    private String khh;

    @LiveProperty(note = "是否只显示不适当", index = 2)
    private boolean onlysdx;
    
    @LiveProperty(note = "日期", index = 3)
    private String rq;
}
