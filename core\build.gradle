plugins {
    id 'java'
    //springBoot插件
    id 'org.springframework.boot' version "${springBootVersion}" apply false
    //spring依赖管理插件
    id 'io.spring.dependency-management' version "${springDependencyManagement}"
    id 'java-library'
}

dependencyManagement {
    imports {
        mavenBom org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        mavenBom "com.apexsoft:live-spring-boot2-bom:${liveVersion}"
    }
    resolutionStrategy{
        cacheChangingModulesFor 0, 'seconds'
    }
}
compileJava {
    options.compilerArgs << '-parameters'
}
compileTestJava{
    options.compilerArgs << '-parameters'
}
group = 'com.apexsoft'
version = '1.0.0'
//依赖
dependencies {
    api "org.springframework.boot:spring-boot-starter-log4j2"
    //基础
    api "com.apexsoft:live-spring-boot-starter:${liveVersion}"
    api "com.apexsoft:live-livebos-session-shared-starter:${liveVersion}"
    //服务监听
    implementation "com.apexsoft:live-spring-boot-actuator-starter:${liveVersion}"
    // 切面
    implementation "org.springframework.boot:spring-boot-starter-aop"
    //定时任务
    api "com.apexsoft:live-xxl-job-executor-starter:${liveVersion}"
    //fastjson包
    api group: 'com.alibaba', name: 'fastjson', version: '1.2.76'
    api group: 'org.apache.httpcomponents', name: 'httpclient', version: '4.5.13'
    api group: 'org.dom4j', name: 'dom4j', version: '2.1.3'
    api group: 'commons-fileupload', name: 'commons-fileupload', version: '1.4'
    api group: 'org.apache.commons', name: 'commons-lang3', version: '3.12.0'
    api group: 'mysql', name: 'mysql-connector-java', version: '8.0.22'
    api group: 'commons-io', name: 'commons-io', version: '2.5'
    api fileTree(dir: '../libs', includes: ['*.jar'])
    //dbf包
    //api "com.github.albfernandez:javadbf:1.13.2"
    //Redisson包
    implementation('org.redisson:redisson:3.25.2'){
        // 与livebos-session-common冲突，排查esotericsoftware
        exclude(group:"com.esotericsoftware", module:"kryo")
    }
    //lombok
    compileOnly 'org.projectlombok:lombok:1.18.24'
    annotationProcessor 'org.projectlombok:lombok:1.18.24'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    implementation "org.mapstruct:mapstruct:1.5.3.Final"
    annotationProcessor "org.mapstruct:mapstruct-processor:1.5.3.Final"
    testAnnotationProcessor "org.mapstruct:mapstruct-processor:1.5.3.Final"
    //gateway依赖包
    api "com.apexsoft.live-gateway:client:2.9.2-RC6"
    api "com.squareup.okhttp3:okhttp:3.14.2"
    api "com.squareup.okio:okio:1.17.2"
    api "com.google.code.gson:gson:2.8.6"
    api(project(":api"))
}
//单元测试
test {
    useJUnitPlatform()
}

//依赖缓存时间
configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}
configurations {
    all*.exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    all*.exclude group:"org.slf4j",module:"slf4j-log4j12"
}
jar {
    baseName 'sdx-core'
    version ''
}