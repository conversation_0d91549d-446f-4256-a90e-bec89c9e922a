package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.core.adapter.ISqlAdapter;
import com.apex.sdx.core.exception.BusinessException;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjKhdcwj;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTpjKhdcwjService;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTpjKhdcwjMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class TOdsYgtCifTpjKhdcwjServiceImpl extends ServiceImpl<TOdsYgtCifTpjKhdcwjMapper, TOdsYgtCifTpjKhdcwj>
    implements TOdsYgtCifTpjKhdcwjService{

    @Autowired
    ISqlAdapter sqlAdapter;

    @Override
    public Long getMaxIdByKhsdx(Integer sdxfl, String sdxlbCbm, String sdxlbIbm, Integer khlb, String khh, Integer zjlb, String zjbh, String khjc, String cid) {
        try {
            String maxSql= sqlAdapter.max("b.id", "");
            return this.baseMapper.selectMaxIdByKhsdx(maxSql, sdxfl, sdxlbCbm, sdxlbIbm, khlb, khh, zjlb, zjbh, khjc, cid);
        } catch (Exception e) {
            String note = String.format("查询问卷ID异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }
}




