<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.YwxtsdxMapper">

    <resultMap id="YwxtsdxTjVoMap" type="com.apex.sdx.api.vo.sdx.YwxtsdxTjVo">
        <result property="ywxt" column="ywxt" jdbcType="INTEGER"/>
        <result property="total" column="total" jdbcType="INTEGER"/>
        <result property="sdNum" column="sd_num" jdbcType="INTEGER"/>
        <result property="sdRate" column="sd_rate" jdbcType="DOUBLE"/>
        <result property="bsdNum" column="bsd_num" jdbcType="INTEGER"/>
        <result property="bsdRate" column="bsd_rate" jdbcType="DOUBLE"/>
        <result property="btgNum" column="btg_num" jdbcType="INTEGER"/>
        <result property="btgRate" column="btg_rate" jdbcType="DOUBLE"/>
    </resultMap>

    <!-- 统计业务系统适当性情况 -->
    <select id="tjYwxtSdxByRq" resultMap="YwxtsdxTjVoMap">
        SELECT
            CASE t.cxywlb
                WHEN 100 THEN 1001
                WHEN 200 THEN 1002
                ELSE 0
            END AS ywxt,
            COUNT(1) AS total,
            SUM(CASE WHEN t.sdxjg = 1 THEN 1 ELSE 0 END) AS sd_num,
            ROUND(SUM(CASE WHEN t.sdxjg = 1 THEN 1 ELSE 0 END) * 1.0 / COUNT(1), 4) AS sd_rate,
            SUM(CASE WHEN t.sdxjg = 0 THEN 1 ELSE 0 END) AS bsd_num,
            ROUND(SUM(CASE WHEN t.sdxjg = 0 THEN 1 ELSE 0 END) * 1.0 / COUNT(1), 4) AS bsd_rate,
            SUM(CASE WHEN t.sdxjg = -1 THEN 1 ELSE 0 END) AS btg_num,
            ROUND(SUM(CASE WHEN t.sdxjg = -1 THEN 1 ELSE 0 END) * 1.0 / COUNT(1), 4) AS btg_rate
        FROM
            sdx.tsdxjg_qtyktyw t
        WHERE
            t.rq = #{rq}
        AND
            t.cxywlb IN (100, 200)
        GROUP BY
            t.cxywlb
        ORDER BY
            t.cxywlb
    </select>
</mapper> 