package com.apex.sdx.api.vo.compute;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> wu<PERSON><PERSON><PERSON>
 * @create 2025/5/8 14:10
 */
@Setter
@Getter
public class QtcccptjVo {

    @LiveProperty(note = "业务系统", index = 1)
    private Integer ywxt;

    @LiveProperty(note = "客户数量", index = 2)
    private Integer khsl;

    @LiveProperty(note = "持仓数量", index = 3)
    private Integer ccsl;

    @LiveProperty(note = "适当数量", index = 4)
    private Integer sdsl;

    @LiveProperty(note = "适当占比", index = 5)
    private Double sdzb;

    @LiveProperty(note = "不适当数量", index = 6)
    private Integer bsdsl;

    @LiveProperty(note = "不适当占比", index = 7)
    private Double bsdzb;

    @LiveProperty(note = "日期", index = 8)
    private String rq;

}
