<template>
  <div>
    <div :id="chartsId" style=" width:calc(100% - 50px); height:180px;margin: 0 25px auto">
    </div>
  </div>

</template>
<script>
import {defineComponent} from 'vue';

let barCharts = [];
export default defineComponent({
  props: ["dataset", "dataset1", "title","legend", "hideProfessional"],
  data() {
    return {
      labels: [],//名称
      labels1: [],//名称
      values: [],//数量
      values1: [],//数量
      zb:[],//占比
      zb1:[],//占比
      max: 0,
      max1: 0,
      //legend: ['C1-保守型', 'C2-相对保守型', 'C3-稳健型', 'C4-相对积极型', 'C5-积极型'],
      gridTop: '15%',
      gridHeight: '50%',
      colors: ['#F42F65', '#FDA51F', '#2BBD88', '#2987C4', '#944EA7','#36A6FC'],
    }
  },
  methods: {
    initBarChart() {
      let el = document.getElementById(this.chartsId)
      barCharts[this.chartsId] = this.$echarts.init(el);
      this.setOption();

      // 添加窗口大小变化监听
      window.addEventListener('resize', this.handleResize);
    },
    setOption() {
      this.labels = this.$props.dataset[0];
      this.values = this.$props.dataset[1];
      this.zb = this.$props.dataset[2];
      this.max = this.values.reduce((total, num) => total + parseInt(num), 0);
      this.labels1 = this.$props.dataset1[0];
      this.values1 = this.$props.dataset1[1];
      this.zb1 = this.$props.dataset1[2];
      this.max1 = this.values1.reduce((total, num) => total + parseInt(num), 0);
      let option = this.getOption();
      barCharts[this.chartsId].setOption(option);

      // 初始渲染后立即调整尺寸
      this.$nextTick(() => {
        barCharts[this.chartsId]?.resize();
      });

    },
    handleResize() {
      if (barCharts[this.chartsId]) {
        barCharts[this.chartsId].resize();
      }
    },
    getOption() {
      let _this = this;
      let option = {
        /* grid: {
           x: '20%',
           y: 0,
           x2: '15%',
           y2: 0,
           //containLabel: true
         },*/
        grid: this.hideProfessional ? [
          { // 只显示第一个图表区域（普通投资者）
            top: this.gridTop,
            height: this.gridHeight,
            left: '10%',
            right: '10%'
          }
        ] : [
          { // 第一个图表区域
            top: this.gridTop,
            height: this.gridHeight,
            left: '10%',
            right: '55%'
          },
          { // 第二个图表区域
            top: this.gridTop,
            height: this.gridHeight,
            left: '60%',
            right: '5%'
          }
        ],
        textStyle: {
          color: '#888888'
        },
        legend: {
          data: _this.legend,
          textStyle: {
            color: '#888888'
          },
          bottom: 20,
          left: 'center',
          icon: 'circle', // 设置图例为小圆点
          itemWidth: 10,  // 控制圆点宽度
          itemHeight: 10, // 控制圆点高度
          itemGap: 20,   // 图例项之间的间隔
        },
        tooltip: {
          show: true,
          trigger: 'item',
          className: 'myTooltip', // 指定自定义类名
          formatter: function (p) {
            const zb = _this.zb.concat(_this.zb1);
            return '<div style="width:160px;padding: 10px;background-color: rgba(217,225,240,.7);border-radius: 10px;">' +
                '            <div style="margin-bottom: 5px"><span style="color: #454A55;">'+p.seriesName+'</span></div>' +
                '            <div style="padding: 0 0 0 10px;background-color: #fff;border-radius: 10px;">' +
                '            <p>' +
                '              <span style="color: #454A55;width: 40%;display: inline-block;">数量:</span>' +
                '              <span style="color: #454A55;width: 50%;display: inline-block;">'+p.value+'</span>' +
                '            </p>' +
                '            <p>' +
                '              <span style="color: #454A55;width: 40%;display: inline-block;">占比:</span>' +
                '              <span style="color: #454A55;width: 50%;display: inline-block;">'+parseFloat(zb[p.componentIndex]).toFixed(2)+'%</span>' +
                '            </p>' +
                '            </div>' +
                '          </div>';
          }
        },
        yAxis: this.hideProfessional ? [{
          type: 'category',
          gridIndex: 0,
          data: _this.labels,
          nameLocation: 'middle',
          axisLine: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false  // 隐藏刻度线
          },
          axisLabel: {
            show: true,  // 显示刻度标签
            formatter: function (value) {
              return value.length > 5 ? value.substr(0, 5) + '\n' + value.substr(5) : value;
            },
          },
        }, {
            type: 'category',
            axisTick: 'none',
            axisLine: 'none',
            show: true,
            gridIndex: 0,
            axisLabel: {
              color: '#888888',
              fontSize: '12',
              verticalAlign: 'middle',
              align: 'right',
              padding: [0, 0, 0, 50],
            },
            data: [this.max],
            offset: 50 // 适度向右偏移，确保数值完整显示且不会太远
          }
        ] : [{
          type: 'category',
          gridIndex: 0,
          data: _this.labels,
          nameLocation: 'middle',
          axisLine: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false  // 隐藏刻度线
          },
          axisLabel: {
            show: true,  // 显示刻度标签
            formatter: function (value) {
              return value.length > 5 ? value.substr(0, 5) + '\n' + value.substr(5) : value;
            },
          },
        }, { // 第二个图表的y轴
          type: 'category',
          gridIndex: 1,
          data: _this.labels1,
          nameLocation: 'middle',
          axisLine: {show: false},
          splitLine: {show: false},
          axisTick: {show: false},
          axisLabel: {
            formatter: function (value) {
              return value.length > 5 ? value.substr(0, 5) + '\n' + value.substr(5) : value;
            }
          }
        },
          {
            type: 'category',
            axisTick: 'none',
            axisLine: 'none',
            show: true,
            gridIndex: 0,
            axisLabel: {
              color: '#888888',
              fontSize: '12',
              verticalAlign: 'middle',
              align: 'right',
              padding: [0, 0, 0, 50],
            },
            data: [this.max],
            offset: 50 // 适度向右偏移，确保数值完整显示且不会太远
          }, {
            type: 'category',
            axisTick: 'none',
            axisLine: 'none',
            show: true,
            gridIndex: 1,
            axisLabel: {
              color: '#888888',
              fontSize: '12',
              verticalAlign: 'middle',
              align: 'right',
              padding: [0, 0, 0, 50],
            },
            data: [this.max1],
            offset: 50 // 适度向右偏移，确保数值完整显示且不会太远
          }
        ],
        xAxis: this.hideProfessional ? [
          {
            type: 'value',
            gridIndex: 0,
            min: 0,
            max: this.max,
            axisLabel: {
              show: false,  // 显示刻度标签
            },
            axisLine: {
              show: false  // 隐藏轴线
            },
            axisTick: {
              show: false  // 隐藏刻度线
            },
            splitLine: {
              show: false  // 隐藏网格线
            }
          }
        ] : [
          {
            type: 'value',
            gridIndex: 0,
            min: 0,
            max: this.max,
            axisLabel: {
              show: false,  // 显示刻度标签
            },
            axisLine: {
              show: false  // 隐藏轴线
            },
            axisTick: {
              show: false  // 隐藏刻度线
            },
            splitLine: {
              show: false  // 隐藏网格线
            }
          },
          { // 第二个图表的x轴
            type: 'value',
            gridIndex: 1,
            min: 0,
            max: this.max1,
            axisLabel: {show: false},
            axisLine: {show: false},
            axisTick: {show: false},
            splitLine: {show: false}
          }
        ],
        series: this.hideProfessional ? [
          // 只显示第一组数据系列 (普通投资者)
          ...this.getSeriesData(0, this.values, this.max,this.zb)
        ] : [
          // 第一组数据系列 (第一个图表)
          ...this.getSeriesData(0, this.values, this.max,this.zb),
          // 第二组数据系列 (第二个图表)
          ...this.getSeriesData(1, this.values1, this.max1,this.zb1)
        ]
      };
      return option;
    },
    getSeriesData(chartIndex, values, max,zb) {
      let data = [];
      let _this = this;

      values.forEach((item, index) => {
        data.push({
          type: 'bar',
          stack: chartIndex === 0 ? '1' : '2', // 设置堆叠层，确保两组数据在同一堆叠层中显示（可选）
          name: this.legend[index],
          legendHoverLink: true,
          barMinHeight: 5,// 设置柱条最小高度为1，防止高度过小导致显示问题
          barWidth: 15,
          xAxisIndex: chartIndex,
          yAxisIndex: chartIndex,
          gridIndex: chartIndex,
          itemStyle: {
            color: this.colors[index % this.colors.length], // 设置柱条颜色
            borderRadius: [2, 2, 2, 2], // 设置柱条圆角
            borderWidth: 1,
            borderColor: '#fff', // 设置边框颜色为白色
          },
          label: {
            show: false,
            position: index % 2 == 0 ? 'top' : 'bottom',
           // position: 'top',
            align: 'left',
            verticalAlign: 'middle',
            distance: 25,
            formatter: function (params) {
              //const percent = (params.value / max * 100).toFixed(0);
              //const name = params.seriesName.split('-')[0];
              const percent = parseFloat(zb[index]).toFixed(2);
              const name = params.seriesName;
              return '{a|' + name + ' }' + '\n{a|' + params.value + ' ' + percent + '%}';
            },
            rich: {
              a: {
                fontSize: '12px',
                // 获取对应系列的颜色
                color: this.colors[index % this.colors.length],
              },
            },
          },
          labelLine: {
            show: false, // 显示引线
            lineStyle: {
              dashOffset: 10,
              length: 50, // 引线长度
              length2: 50, // 引线另一端的长度，相对于柱状图的位置偏移量
              color: this.colors[index % this.colors.length],
            },
          },
          data: [item],
        });
      });
      return data;
    },

  },
  mounted() {
    if (this.$props.dataset && this.$props.dataset.length > 0) {
      this.initBarChart()
    }
  },
  computed: {
    chartsId() {
      return "id_" + this.title
    },
  },
  watch: {
    dataset(newValue, oldValue) {
      if (newValue && newValue.length > 0 && JSON.stringify(newValue) != JSON.stringify(oldValue)) {
        if (barCharts[this.chartsId]) {
          this.setOption();
        } else {
          this.initBarChart();
        }
      }
    },
    dataset1(newValue, oldValue) {
      if (newValue && newValue.length > 0 && JSON.stringify(newValue) != JSON.stringify(oldValue)) {
        if (barCharts[this.chartsId]) {
          this.setOption();
        } else {
          this.initBarChart();
        }
      }
    },
  },
  unmounted() {
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize);

    if (barCharts[this.chartsId]) {
      barCharts[this.chartsId].dispose();
      delete barCharts[this.chartsId];
    }
  },
});
</script>

<style scoped>
:deep(.myTooltip){
  padding: 0 !important;
  border-radius: 10px !important;
  border-width: 0px !important;
}
</style>