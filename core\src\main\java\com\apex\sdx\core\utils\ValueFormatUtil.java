package com.apex.sdx.core.utils;

import com.alibaba.fastjson.JSONObject;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/19 10:55
 */
public class ValueFormatUtil {

    /**
     * 保留?位有效数字
     *
     * @param list
     */
    public static void value2Format(List<JSONObject> list) {
        for (JSONObject obj : list) {
            formatValue(obj);
        }
    }

    public static void formatValue(JSONObject obj) {
        try {
            BigDecimal value = JSONUtil.optBigDecimal(obj, "RESULT");
            if (value == null) {
                return;
            }
            Integer prcs = obj.getInteger("PRCS");
            if (prcs != null && prcs >= 0) {
                String format = "0";
                if (prcs > 0) {
                    format += ".";
                    for (int i = 0; i < prcs; i++) {
                        format += "0";
                    }
                }
                DecimalFormat prcsFormat = new DecimalFormat(format);
                obj.put("RESULT", prcsFormat.format(value));
            }
        } catch (Exception e) {
        }
    }
    /**
     * 保留?位有效数字,小数点后面为0，不补0
     *
     * @param list
     */
    public static void value2FormatNoZero(List<JSONObject> list) {
        for (JSONObject obj : list) {
            String value = obj.getString("RESULT");
            // 精确值
            Integer prcs = obj.getInteger("PRCS");
            try {
                if (prcs != null && prcs >= 0) {
                    String format = "#";
                    if (prcs > 0) {
                        format += ".";
                        for (int i = 0; i < prcs; i++) {
                            format += "#";
                        }
                    }
                    BigDecimal result = new BigDecimal(value);
                    DecimalFormat prcsFormat = new DecimalFormat(format);
                    obj.put("RESULT", prcsFormat.format(result));
                }
            } catch (Exception e) {
                obj.put("RESULT", value);
            }
        }
    }
}
