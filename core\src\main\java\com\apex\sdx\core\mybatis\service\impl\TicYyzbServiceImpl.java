package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.resp.query.KhsdxfbRes;
import com.apex.sdx.api.resp.query.KhzsDateRes;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.mybatis.entity.TicYyzb;
import com.apex.sdx.core.mybatis.mapper.TicYyzbMapper;
import com.apex.sdx.core.mybatis.service.TicYyzbService;
import com.apex.sdx.core.utils.DateUtil;
import com.apex.sdx.core.utils.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tic_yyzb(营运指标)】的数据库操作Service实现
 * @createDate 2025-05-09 14:14:08
 */
@Service
public class TicYyzbServiceImpl extends ServiceImpl<TicYyzbMapper, TicYyzb>
        implements TicYyzbService {

    @Override
    public List<KhsdxfbRes> khzs(String type, String rq) {
        try {
            return this.baseMapper.khzs(type, rq);
        } catch (Exception e) {
            String note = String.format("客户总数查询异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }

    @Override
    public List<KhzsDateRes> zkhsDate(String type, String rq) {
        try {
            if(StringUtils.isEmpty(rq)) {
                rq = DateUtil.getNowDate(DateUtil.F_DATE8);
            }
            String preRq = DateUtil.getPreOrNextMouth(rq, -1, DateUtil.F_DATE8);
            return this.baseMapper.zkhsDate(type, rq, preRq);
        } catch (Exception e) {
            String note = String.format("最近一个月客户总数查询异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }
}




