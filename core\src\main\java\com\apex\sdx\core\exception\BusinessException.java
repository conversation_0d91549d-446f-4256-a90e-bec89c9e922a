package com.apex.sdx.core.exception;

import com.apex.sdx.core.result.ResponseEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BusinessException extends RuntimeException{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	//状态码
    private Integer code = -1;

    //错误消息
    private String note;

	public BusinessException(ResponseEnum responseEnum) {
		super(responseEnum.getNote());
		this.code = responseEnum.getCode();
		this.note = responseEnum.getNote();
	}

	public BusinessException(Integer code, String note) {
		super(note);
		this.code = code;
		this.note = note;
	}

	public BusinessException(Integer code, String note, Throwable cause) {
		super(note, cause);
		this.code = code;
		this.note = note;
	}

	public BusinessException(String note) {
		super(note);
		this.note = note;
	}
}
