package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.api.vo.khgl.KhjyqxVo;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhjyqx;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【t_ods_ygt_cif_tkhjyqx】的数据库操作Mapper
* @createDate 2025-06-10 14:38:48
* @Entity com.apex.sdx.core.mybatis.domain.TOdsYgtCifTkhjyqx
*/
public interface TOdsYgtCifTkhjyqxMapper extends BaseMapper<TOdsYgtCifTkhjyqx> {

    Page<KhjyqxVo> queryKhjyqx(Page<KhjyqxVo> page, @Param("khh") String khh, @Param("ywzh")  String ywzh, @Param("gdh") String gdh, @Param("jyqx") Integer jyqx, @Param("zt") Integer zt);
}




