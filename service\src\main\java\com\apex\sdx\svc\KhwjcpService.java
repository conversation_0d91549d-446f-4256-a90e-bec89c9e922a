package com.apex.sdx.svc;

import com.apex.sdx.api.req.khgl.DcwjxqcxReq;
import com.apex.sdx.api.req.khgl.KhwjcplscxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.khgl.DcwjxqVo;
import com.apex.sdx.api.vo.khgl.KhwjcplsVo;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhxx;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjKhdcwj;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjWjcs;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjWjcsQuestions;
import com.apex.sdx.core.mybatis.service.*;
import com.apexsoft.LiveService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.query.IKhwjcpService;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Jingliang
 * @Date 2025-01-23
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "客户调查问卷查询")
public class KhwjcpService implements IKhwjcpService {

    @Autowired
    TOdsYgtCifTkhsdxService tkhsdxService;

    @Autowired
    TOdsYgtCifTkhxxService tkhxxService;

    @Autowired
    TxtdmService txtdmService;

    @Autowired
    TOdsYgtCifTpjKhdcwjService tpjKhdcwjService;

    @Autowired
    TOdsYgtCifTpjWjcsService tpjWjcsService;

    @Autowired
    TOdsYgtCifTpjKhdcwjAnswersService tpjKhdcwjAnswersService;

    @Autowired
    TOdsYgtCifTpjWjcsQuestionsService tpjWjcsQuestionsService;

    @Override
    public QueryPageResponse<KhwjcplsVo> khwjcplscx(KhwjcplscxReq req) throws Exception {
        Assert.notNull(req, KhwjcplscxReq::getKhh);
        QueryPageResponse<KhwjcplsVo> result = new QueryPageResponse<>(1, "查询成功");
        String khh = req.getKhh();

        TOdsYgtCifTkhxx tkhxx = tkhxxService.getKhxxByKhh(req.getKhh(), false);
        // 获取调查问卷适当性信息
        Page<KhwjcplsVo> page = tkhsdxService.getWjsdxByKhh(khh, req.getKsrq(), req.getJsrq(), req.isSearchCount(), req.getPagesize(), req.getPagenum());
        List<KhwjcplsVo> khdjwjmxVos = page.getRecords();
        // 获取问卷id
        for (KhwjcplsVo khdjwjmxVo : khdjwjmxVos) {
            // sdxlb翻译cbm
            String sdxlbIbm = txtdmService.getIbmByCbm("SDX_SDXLB", khdjwjmxVo.getSdxlb());
            // 查询最新wjid
            Long wjid = tpjKhdcwjService.getMaxIdByKhsdx(khdjwjmxVo.getSdxfl(), khdjwjmxVo.getSdxlb(), sdxlbIbm,
                    tkhxx.getKhlb(), khh, tkhxx.getZjlb(), tkhxx.getZjbh(), tkhxx.getKhjc(), tkhxx.getCid());
            khdjwjmxVo.setWjid(wjid);

            // 获取问卷参数
            if(wjid != null){
                TOdsYgtCifTpjWjcs tpjWjcs = tpjWjcsService.getWjcsByWjid(wjid.intValue());
                if (tpjWjcs != null) {
                    khdjwjmxVo.setDcwjmc(tpjWjcs.getName());
                }
            }

            Integer zscptgts = null;
            TOdsYgtCifTpjKhdcwj tpjKhdcwj = tpjKhdcwjService.getById(wjid);
            if (tpjKhdcwj != null && tpjKhdcwj.getZscptgts() != null) {
                zscptgts = tpjKhdcwj.getZscptgts();
            }
            khdjwjmxVo.setTgts(zscptgts);
        }
        result.page(page);

        return result;
    }

    @Override
    public QueryResponse<DcwjxqVo> dcwjxqcx(DcwjxqcxReq req) throws Exception {
        Assert.notNull(req, DcwjxqcxReq::getKhh, DcwjxqcxReq::getKhdcwjid);
        QueryResponse<DcwjxqVo> result = new QueryResponse<>(1, "查询成功");
        String khh = req.getKhh();
        Integer khdcwjid = req.getKhdcwjid();
        // 获取客户信息
        TOdsYgtCifTkhxx khxx = tkhxxService.getKhxxByKhh(khh, false);
        if (khxx == null) {
            result.setNote("客户信息不存在,查询失败!");
            return result;
        }

        // 获取问卷参数
        TOdsYgtCifTpjWjcs tpjWjcs = tpjWjcsService.getWjcsByWjid(khdcwjid);

        // 获取问卷答案
        String dac = tpjKhdcwjAnswersService.getDacByWjid(khdcwjid);
        // 解析答案到 Map<问题ID, 答案>
        Map<Integer, String> answerMap = Arrays.stream(dac.split(";"))
                .filter(entry -> !entry.isEmpty())
                .map(entry -> entry.split("\\|"))
                .filter(parts -> parts.length == 2)
                .collect(Collectors.toMap(
                        parts -> Integer.parseInt(parts[0]),
                        parts -> parts[1],
                        (oldVal, newVal) -> oldVal));

        // 获取问卷问题
        List<DcwjxqVo> tpjWjcsQuestions = tpjWjcsQuestionsService.getQuestionByWjcsId(tpjWjcs.getId());

        // 随机问卷做展示时,只展示有做的题目,过滤并填充答案
        List<DcwjxqVo> answeredQuestions = tpjWjcsQuestions.stream()
                .filter(item -> answerMap.containsKey(item.getId()))
                .peek(item -> item.setDa(answerMap.get(item.getId())))
                .peek(item -> item.setName(tpjWjcs.getName()))
                .collect(Collectors.toList());

        result.setRecords(answeredQuestions);

        return result;
    }
}
