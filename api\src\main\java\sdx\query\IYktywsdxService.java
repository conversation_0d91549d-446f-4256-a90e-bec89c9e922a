package sdx.query;

import com.apex.sdx.api.req.compute.QtyktywtjReq;
import com.apex.sdx.api.req.query.YktywsdxcxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.compute.QtyktywtjVo;
import com.apex.sdx.api.vo.query.YktywsdxVo;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR> <PERSON>liang
 * @Date 2025-03-05
 * @Description:
 */
public interface IYktywsdxService {
    @LiveMethod(paramAsRequestBody = true, note = "已开通业务适当性查询")
    QueryPageResponse<YktywsdxVo> yktywsdxcx(YktywsdxcxReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "业务开通适当性统计")
    QueryPageResponse<QtyktywtjVo> ywktSdxtj4cxywlb(QtyktywtjReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "全体已开通业务不适当统计")
    QueryResponse<QtyktywtjVo> qtyktywBsdxtj(QtyktywtjReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "全体已开通业务不适当明细统计")
    QueryPageResponse<QtyktywtjVo> qtyktywBsdxmxtj(QtyktywtjReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "客户已开通业务适当性统计")
    QueryResponse<QtyktywtjVo> khyktywsdxtj(QtyktywtjReq req) throws Exception;
}
