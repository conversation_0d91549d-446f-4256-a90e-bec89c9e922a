package com.apex.sdx.api.vo.khgl;

import com.apexsoft.LiveProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025-02-14
 * @Description:
 */
@Getter
@Setter
public class GmsfzyzsqcxVo {

    @LiveProperty(note = "申请日期", index = 1)
    private Integer sqrq;

    @LiveProperty(note = "申请时间", index = 2)
    private String sqsj;

    @LiveProperty(note = "证件编号", index = 3)
    private String zjbh;

    @LiveProperty(note = "申请姓名", index = 4)
    private String sqxm;

    @LiveProperty(note = "营业部", index = 5)
    private Long yyb;

    @LiveProperty(note = "查询类型", index = 6)
    private Long cxlx;

    @LiveProperty(note = "出生日期", index = 7)
    private Integer csrq;

    @LiveProperty(note = "姓名", index = 8)
    private String xm;

    @LiveProperty(note = "证件编号核查结果", index = 9)
    private String zjbhhcjg;

    @LiveProperty(note = "姓名核查结果", index = 10)
    private String xmhcjg;

    @LiveProperty(note = "处理结果", index = 11)
    private Long cljg;

    @LiveProperty(note = "结果说明", index = 12)
    private String jgsm;

    @LiveProperty(note = "处理时间", index = 13)
    private String clsj;

    @LiveProperty(note = "操作类别", index = 14)
    private Long czlb;

    @LiveProperty(note = "比对分值", index = 15)
    private BigDecimal bdfz;

    @LiveProperty(note = "比对流水号", index = 16)
    private String bdlsh;

    @LiveProperty(note = "人像比对结果", index = 17)
    private String rxbdjg;
}
