package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.JgkhxxhcsqcxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.vo.khgl.JgkhxxhcsqVo;
import com.apex.sdx.convert.JgkhxxhcsqVoMapping;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTzdZhywsq;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTzdZhywsqService;
import com.apexsoft.LiveService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.query.IZdZhywsqService;

/**
 * <AUTHOR>
 * @Date 2025-02-17
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "中登账户业务申请查询服务")
public class ZdZhywsqService implements IZdZhywsqService {

    @Autowired
    TOdsYgtCifTzdZhywsqService tzdZhywsqService;

    @Autowired
    JgkhxxhcsqVoMapping jgkhxxhcsqVoMapping;

    @Override
    public QueryPageResponse<JgkhxxhcsqVo> jgkhxxhcsqcx(JgkhxxhcsqcxReq req) throws Exception {
        Assert.notNull(req, JgkhxxhcsqcxReq::getKhh);
        QueryPageResponse<JgkhxxhcsqVo> result = new QueryPageResponse<>(1, "查询成功");

        Page<TOdsYgtCifTzdZhywsq> page = tzdZhywsqService.queryJgkhxxhcsqByConditionos(req.getKhh(), req.getKsrq(), req.getJsrq(), req.isSearchCount(), req.getPagesize(), req.getPagenum());
        Page<JgkhxxhcsqVo> jgkhxxhcsqVoPage = jgkhxxhcsqVoMapping.pageConver(page);

        result.page(jgkhxxhcsqVoPage);

        return result;
    }
}
