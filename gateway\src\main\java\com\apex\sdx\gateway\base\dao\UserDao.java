package com.apex.sdx.gateway.base.dao;

import com.alibaba.fastjson.JSONObject;
import com.apex.fix.JFixComm;
import org.springframework.stereotype.Repository;

/**
 * 关于用户的ESB访问类。
 *
 * <AUTHOR> Wu
 *
 */
@Repository("userDao")
public class UserDao extends BaseDao {

	/**
	 * 验证柜员登录密码。
	 *
	 * @param userid 用户名
	 * @param password 密码
	 * @return 验证结果
	 */
	public JSONObject verifyPassword(String userid, String password){
		JSONObject reqData = new JSONObject();
		reqData.put("userid", userid);
		reqData.put("pwd", JFixComm.encrptPwd(password));
		//String result = "{\"note\":\"验证通过\",\"code\":1,\"records\":[{\"chgpwdlimit\":10000,\"lastlogin\":\"2023-02-10 17:01:59\",\"grade\":0,\"chgpwdtime\":\"2023-02-09 15:21:38\",\"name\":\"管理员\",\"photo\":\"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\",\"id\":0,\"userid\":\"admin\",\"logins\":591,\"status\":1}],\"count\":1}";
		//return JSONObject.parseObject(result);
		return execAmsService("ecif.xtgl.IGydlmmyzService","gydlmmyz", reqData,null);
	}

	/**
	 * 更新柜员头像
	 *
	 */
	public JSONObject getUserInfo(String userid){
		JSONObject reqData = new JSONObject();
		reqData.put("userid", userid);
		return execAmsService("ecif.xtgl.ICxgyxxService","gyxxcx", reqData);
	}
}
