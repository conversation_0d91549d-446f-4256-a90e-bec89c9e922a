package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.vo.query.TgcpsdxVo;
import com.apex.sdx.core.exception.BusinessException;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TsdxjgQtdytg;
import com.apex.sdx.core.mybatis.service.TsdxjgQtdytgService;
import com.apex.sdx.core.mybatis.mapper.TsdxjgQtdytgMapper;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tsdxjg_qtdytg(全体已订阅投顾适当性)】的数据库操作Service实现
* @createDate 2025-06-11 18:54:56
*/
@Service
public class TsdxjgQtdytgServiceImpl extends ServiceImpl<TsdxjgQtdytgMapper, TsdxjgQtdytg>
    implements TsdxjgQtdytgService{

    @Override
    public List<TgcpsdxVo> selectByKhhSjlxAndSdxjg(String khh, Integer sjlx, boolean onlysdx,String rq) {
        try {
            return this.baseMapper.selectByKhhSjlxAndSdxjg(khh, sjlx, onlysdx, rq);
        } catch (Exception e) {
            String note = String.format("查询投顾产品适当性异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }
}




