package com.apex.sdx.core.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.Vxtdm;
import com.apex.sdx.core.mybatis.service.VxtdmService;
import com.apex.sdx.core.mybatis.mapper.VxtdmMapper;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Service
public class VxtdmServiceImpl extends ServiceImpl<VxtdmMapper, Vxtdm>
    implements VxtdmService{

    /**
     * 查询字典集合
     *
     * @param fldm 分类代码
     * @return
     */
    @Override
    @Cacheable(cacheNames = "dict_xtdm", key = "#fldm", unless = "#result.size == 0")
    public List<Vxtdm> querySjzd(String fldm) {
        QueryWrapper<Vxtdm> wrapper = new QueryWrapper<>();
        wrapper.eq("fldm", fldm);
        try {
            return baseMapper.selectList(wrapper);
        } catch (Exception e) {
            log.warn(String.format("查询数据字典[%s]失败：%s", fldm, e.getMessage()));
            return null;
        }
    }

    @Override
    @Cacheable(cacheNames = "dict_xtdm", key = "#fldm", unless = "#result.size == 0")
    public Map<String, Vxtdm> querySjzd4Map(String fldm) {
        if(fldm == null) {
            return new HashMap<>();
        }
        List<Vxtdm> list = this.querySjzd(fldm);
        Map<String, Vxtdm> map = new HashMap<>();
        for (Vxtdm vxtdm : list) {
            map.put(vxtdm.getIbm(), vxtdm);
        }
        return map;
    }

    @Override
    @Cacheable(cacheNames = "dict_xtdm", key = "#fldm+'_'+#ibm", unless = "#result == null")
    public String getNote(String fldm, String ibm, String defaultNote) {
        LambdaQueryWrapper<Vxtdm> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Vxtdm::getFldm, fldm).eq(Vxtdm::getIbm, ibm);
        Vxtdm one = this.getOne(wrapper, false);
        if(one != null) {
            return one.getNote();
        }
        return defaultNote;
    }

    @Override
    @Cacheable(cacheNames = "dict_xtdm", key = "#fldm+'_'+#ibm", unless = "#result == null")
    public String getNote(String fldm, String ibm) {
        return getNote(fldm, ibm, "");
    }

    @Override
    @Cacheable(cacheNames = "dict_xtdm", key = "#fldm+'_'+#ibm", unless = "#result == null")
    public String getNote(String fldm, Integer ibm) {
        return getNote(fldm, ibm + "", "");
    }
}




