<template>
  <div class="dxsdx_cxtj">
    <div class="cxtj_item">
      <span>开始日期：</span>
      <a-date-picker v-model:value="ksrq" style="width: 250px" placeholder="开始日期" />
    </div>
    <div class="cxtj_item">
      <span>结束日期：</span>
      <a-date-picker v-model:value="jsrq" style="width: 250px" placeholder="结束日期" />
    </div>
    <div class="cxtj_item">
      <a class="btn" @click="getKhwjcpls">查询</a>
      <a class="btn fz" @click="reset">重置</a>
    </div>
  </div>
  <a-divider style="margin: 12px 0"/>
  <div>
    <a-table
      :columns="columns"
      :data-source="data"
      :loading="loading"
      :pagination="pagination"
      @change="pageChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operation'">
          <a style="color: #B48A3B" @click="openWjxq(record)">详情</a>
        </template>
      </template>
    </a-table>
  </div>
  <wjmx-modal
    v-model:open="open"
    :dcwjData="dcwjData"
    :khh="khh"
    :dictArray="dictArray"
    :khjbxx="khjbxx"
  />
</template>
<script>
import {defineComponent, inject} from "vue";
import WjmxModal from "@views/dhcx/sdxlh/wjmxModal.vue";
import {commonApi} from "@/api/common";
import {message} from "ant-design-vue";
import {getDcNote} from "@utils/bussinessUtils";
import dayjs from "dayjs";

export default defineComponent({
  name: "wjcpls",
  inject: ["khh"],
  props: ["dictArray", "khjbxx"],
  components: { WjmxModal },
  data() {
    return {
      ksrq: null,
      jsrq: null,
      columns: [
        { title: '调查问卷', dataIndex: 'dcwjmc', key: 'dcwjmc', fixed: true},
        { title: '测评分数', dataIndex: 'cpdf', key: 'cpdf', fixed: true},
        { title: '测评等级', dataIndex: 'cpdj', key: 'cpdj',
          customRender: ({text}) => {
            return getDcNote("SDX_FXCSNL", text, this.dictArray);
          }
        },
        { title: '测评日期', dataIndex: 'cprq', key: 'cprq',},
        { title: '测评有效期', dataIndex: 'cpyxq',  key: 'cpyxq', },
        { title: '查看问卷', dataIndex: 'operation',  key: 'operation',  },
      ],
      data: [],
      open: false,
      loading: false,
      current: 1,
      pageSize: 10,
      total: 0,
      dcwjData: null,
    }
  },
  computed: {
    pagination() {
      return {
        total: this.total,
        current: this.current,
        pageSize: this.pageSize,
        //showQuickJumper: true,
        showSizeChanger: true,
        showTotal(total) {
          return "共 " + total + " 条";
        },
      };
    }
  },
  /*mounted() {
    this.getKhwjcpls();
  },*/
  methods: {
    pageChange(page){
      this.current = page.current;
      this.pageSize = page.pageSize;
      this.getKhwjcpls();
    },
    reset(){
      this.ksrq = null;
      this.jsrq = null;
    },
    getKhwjcpls() {
      this.loading = true;
      commonApi.executeAMS(
        "sdx.query.IKhwjcpService",
        "khwjcplscx",
        {
          khh: this.khh,
          ksrq: this.ksrq ? dayjs(this.ksrq).format("YYYYMMDD") : null,
          jsrq: this.jsrq ? dayjs(this.jsrq).format("YYYYMMDD") : null,
          isSearchCount: true,
          pagenum: this.current,
          pagesize: this.pageSize
        },
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.data = res.records || [];
        this.total = res.total;
      }).finally(() => {
        this.loading = false;
      })
    },
    openWjxq(record) {
      this.open = true;
      this.dcwjData = record;
    }
  }
});
</script>
<style scoped>
.dxsdx_cxtj{line-height: 32px; padding: 0 5px 3px 10px;}
.dxsdx_cxtj .cxtj_item{ white-space: nowrap;margin-bottom: 12px; font-size: 14px; display: inline-block; vertical-align: middle; margin-right: 20px;}
.dxsdx_cxtj .cxtj_item span{color: #888; display: inline-block; margin-right: 15px; vertical-align: middle;}
.dxsdx_cxtj .cxtj_item input[type=text]{display: inline-block; vertical-align: middle; width: 340px; height: 32px; line-height: 30px; border:1px solid #d6d6d6; border-radius: 4px; padding: 0 10px;}
.dxsdx_cxtj .cxtj_item input[type=text]:focus{outline:1px solid #d0ad6b;}
a.btn{min-width: 80px; padding: 0 10px; margin-right: 10px; text-align: center; line-height: 32px;border-radius: 4px; background-color: #f6e5d1; color: #bf935f; cursor: pointer; display: inline-block; vertical-align: middle;}
a.btn.fz{background-color: #fff; color: #777;border: 1px solid #d6d6d6; line-height: 30px;}
a.btn:hover,a.btn.fz:Hover{ background-color: #bf935f; color: #fff; border: none; line-height: 32px;}
</style>