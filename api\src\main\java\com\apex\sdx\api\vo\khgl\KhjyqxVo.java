package com.apex.sdx.api.vo.khgl;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-01-22
 * @Description:
 */
@Getter
@Setter
public class KhjyqxVo {
    /**
     * 客户号
     */
    @LiveProperty(note = "客户号", index = 1)
    private String khh;

    /**
     * 业务系统
     */
    @LiveProperty(note = "业务系统", index = 2)
    private Integer ywxt;

    /**
     * 业务账号
     */
    @LiveProperty(note = "业务账号", index = 3)
    private String ywzh;

    /**
     * 交易所. 数据字典: GT_JYS
     */
    @LiveProperty(note = "交易所", index = 4)
    private Integer jys;

    /**
     * 股东号
     */
    @LiveProperty(note = "股东号", index = 5)
    private String gdh;

    /**
     * 交易权限
     */
    @LiveProperty(note = "交易权限", index = 6)
    private Integer jyqx;

    /**
     * sx1
     */
    @LiveProperty(note = "sx1", index = 7)
    private String sx1;

    /**
     * sx2
     */
    @LiveProperty(note = "sx2", index = 8)
    private String sx2;

    /**
     * 营业部
     */
    @LiveProperty(note = "营业部", index = 9)
    private Integer yyb;

    /**
     * 0-正常;3-关闭
     */
    @LiveProperty(note = "状态", index = 10)
    private Integer zt;

    /**
     * 开通日期
     */
    @LiveProperty(note = "开通日期", index = 11)
    private Integer ktrq;

    /**
     * 关闭日期
     */
    @LiveProperty(note = "关闭日期", index = 12)
    private Integer gbrq;

    /**
     * ywqqid
     */
    @LiveProperty(note = "业务请求id", index = 13)
    private Integer ywqqid;

    /**
     * 证券代码
     */
    @LiveProperty(note = "证券代码", index = 14)
    private String zqdm;

    /**
     * 证券名称
     */
    @LiveProperty(note = "证券名称", index = 15)
    private String zqmc;

    /**
     *  权限有效期
     */
    @LiveProperty(note = "权限有效期", index = 16)
    private Integer qxyxq;

    /**
     *  适当性结果
     */
    @LiveProperty(note = "适当性结果", index = 17)
    private String sdxjg;

    /**
     *  流水日期
     */
    @LiveProperty(note = "流水日期", index = 18)
    private String lsrq;

    /**
     * 作用
     */
    @LiveProperty(note = "作用", index = 19)
    private String zy;

    /**
     * 业务系统名称
     */
    @LiveProperty(note = "业务系统名称", index = 20)
    private String ywxtmc;
}
