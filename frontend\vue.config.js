const path = require('path')
const themeType = 'base'
const { VUE_APP_PORT, } = process.env;
const packageInfo = require('./public/version')

function resolve(dir) {
  return path.join(__dirname, dir)
}
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
const IS_DEV = process.env.NODE_ENV !== 'production'
/**
 * @todo 开发环境配置
 * 某些实用工具， plugins 和 loaders 都只能在构建生产环境时才有用
 * 在开发时使用 UglifyJsPlugin 来压缩和修改代码是没有意义的,不压缩
 */
const DEVELOPMENT = webpackConfig => {
  /**
   * @todo 启用 eval-source-map 更好的测试
   * 每个模块使用 eval() 执行，并且 source map 转换为 DataUrl 后添加到 eval() 中。
   * 初始化 source map 时比较慢，但是会在重新构建时提供比较快的速度，并且生成实际的文件。
   * 行数能够正确映射，因为会映射到原始代码中。它会生成用于开发环境的最佳品质的 source map。
   */
  webpackConfig.merge({ devtool: 'eval-source-map' })
  webpackConfig.plugin('html').tap(([options]) => [
    Object.assign(options, {
      minify: false,
      chunksSortMode: 'none',
      appVersion: `v${packageInfo.version}`
    })
  ])
  // webpackConfig.plugin('BundleAnalyzerPlugin').use(BundleAnalyzerPlugin)
  return webpackConfig
}
/**
 * @todo 生产环境配置
 * 每个额外的 loader/plugin 都有启动时间。尽量少使用不同的工具
 */
const PRODUCTION = webpackConfig => {
  /**
   * @todo 不需要启用 source-map，去除 console 的情况下 source-map 根本没用，还浪费大量时间和空间
   * 详情见：https://webpack.js.org/configuration/devtool/#devtool
   */
  webpackConfig.merge({ devtool: false })
  webpackConfig.plugin('html').tap(([options]) => [
    Object.assign(options, {
      minify: {
        removeComments: true,
        removeCommentsFromCDATA: true,
        collapseWhitespace: true,
        conservativeCollapse: false,
        collapseInlineTagWhitespace: true,
        collapseBooleanAttributes: true,
        removeRedundantAttributes: true,
        removeAttributeQuotes: false,
        removeEmptyAttributes: true,
        removeScriptTypeAttributes: true,
        removeStyleLinkTypeAttributes: true,
        useShortDoctype: true,
        minifyJS: true,
        minifyCSS: true
      },
      cache: true, // 仅在文件被更改时发出文件
      hash: true, // true则将唯一的webpack编译哈希值附加到所有包含的脚本和CSS文件中,这对于清除缓存很有用
      scriptLoading: 'defer', // 现代浏览器支持非阻塞javascript加载（'defer'）,以提高页面启动性能。
      inject: true, // true所有javascript资源都将放置在body元素的底部
      chunksSortMode: 'none',
      appVersion: `v${packageInfo.version}`
    })
  ])
  return webpackConfig
}
// 时间戳保证不会版本重复
const timestamp = new Date().getTime()

module.exports = {
  publicPath: '/app',
  pluginOptions: {
    /** 全局加载less 的 webpack 插件  */
    'style-resources-loader': {
      preProcessor: 'less',
      patterns: [resolve('./src/theme/var.less')]
    },
    autoRouting: {
      chunkNamePrefix: 'page-',
      outFile: 'src/router/routes.js',
      nested: false
    }
  },
  configureWebpack: {
    output: {
      // 输出重构  打包编译后的 文件名称  【模块名称.版本号.时间戳】
      filename: `js/[name].${timestamp}.js`,
      chunkFilename: `js/[name].${timestamp}.js`
    },
    optimization: {
      splitChunks: {
        chunks: 'all',
        minSize: 3000,
        minChunks: 1,
        maxAsyncRequests: 5,
        maxInitialRequests: 6,
        automaticNameDelimiter: '-',
        cacheGroups: {
          vendors: {
            name: 'chunk-vendors',
            test: /[\\/]node_modules[\\/]/,
            priority: -10,
            chunks: 'initial'
          },
          lodash: {
            name: 'chunk-lodash',
            test: /[\\/]node_modules[\\/]lodash[\\/]/,
            priority: 20
          },
          vue: {
            name: 'chunk-vue',
            test: /[\\/]node_modules[\\/]vue[\\/]/
          },
          vuex: {
            name: 'chunk-vuex',
            test: /[\\/]node_modules[\\/]vuex[\\/]/
          },
          'vue-router': {
            name: 'chunk-vue-router',
            test: /[\\/]node_modules[\\/]vue-router[\\/]/
          },
          'ant-design-vue': {
            name: 'chunk-ant-design-vue',
            test: /[\\/]node_modules[\\/]ant-design-vue[\\/]/
          }
        }
      }
    }
  },
  chainWebpack: config => {
    config.resolve.symlinks(true)

    if (process.env.use_analyzer) {
      config.plugin('webpack-bundle-analyzer').use(BundleAnalyzerPlugin)
    }
    config.resolve.alias
      .set('@assets', resolve('src/assets'))
      .set('@views', resolve('src/views'))
      .set('@components', resolve('src/components'))
      .set('@layout', resolve('src/layout'))
      .set('@router', resolve('src/router'))
      .set('@store', resolve('src/store'))
      .set('@utils', resolve('src/utils'))
    IS_DEV ? DEVELOPMENT(config) : PRODUCTION(config)
  },
  // 跨域问题
  devServer: {
    headers: {
      'Access-Control-Allow-Origin': '*'
    },
    host: '0.0.0.0',
    webSocketServer:false,
    open: true,
    hot: true,
    historyApiFallback: true,
    allowedHosts: "all",
    port: VUE_APP_PORT || 8090,
    proxy: {
      '/plugins/react/themes/sdx-theme': {
        target: 'http://localhost:3030',
        changeOrigin: true,
      },
      /*'/plugins/react/plugin/login': {
        target: 'http://localhost:3040',
        changeOrigin: true,
      },*/
      '/api': {
        // target: 'http://**************:9999',
        target: 'http://localhost:9999/',
        changeOrigin: true,
        pathRewrite: { '/api': '/' },
      },
      '/': {
        target: 'http://**************:8080/',
        //target: 'http://**************:9080/',
        changeOrigin: true,
      },
    }
  },
  css: {
    loaderOptions: {
      less: {
        sourceMap: IS_DEV,
        lessOptions: {
          javascriptEnabled: true
        }
      }
    }
  }
}