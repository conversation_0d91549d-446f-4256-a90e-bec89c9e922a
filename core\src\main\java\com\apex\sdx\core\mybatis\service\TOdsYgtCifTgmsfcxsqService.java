package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTgmsfcxsq;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *
 */
public interface TOdsYgtCifTgmsfcxsqService extends IService<TOdsYgtCifTgmsfcxsq> {

    /**
     * 根据三要素和条件查询公民身份证验证申请
     * @param khxm
     * @param zjlb
     * @param zjbh
     * @param ksrq
     * @param jsrq
     * @param xmhcjg
     * @param zjhcjg
     * @param rxbdjg
     * @param cxlx
     * @param czlb
     * @param isSearchCount
     * @param pagesize
     * @param pagenum
     * @return
     */
    Page<TOdsYgtCifTgmsfcxsq> queryPageByConditions(String khxm, Integer zjlb, String zjbh, Integer ksrq, Integer jsrq, String xmhcjg, String zjhcjg, String rxbdjg, Integer cxlx, Integer czlb, boolean isSearchCount, int pagesize, int pagenum);
}
