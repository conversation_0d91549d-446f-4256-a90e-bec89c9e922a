import App from './App.vue'
import router from './router'
import store from './store'
import './assets/css/index.css'
import { createApp } from 'vue'
import * as echarts from 'echarts'
if (module.hot) {
  module.hot.accept()
}
import Antd from "ant-design-vue";
import {message} from "ant-design-vue";

const app = createApp(App)
app.use(Antd)
app.config.globalProperties.$message = message;
app.config.globalProperties.$echarts = echarts;
message.config({
  duration: 2,// 持续时间
  top:`100px`, // 到页面顶部距离
  maxCount: 3 // 最大显示数, 超过限制时，最早的消息会被自动关闭
});
app.use(router)
app.use(store)
app.mount('#app')
