package com.apex.sdx.api.resp.query;

import com.apex.sdx.api.resp.common.R;
import com.apex.sdx.api.vo.query.XyzhVo;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/1/23 11:41
 * @Description: TODO
 */
@Setter
@Getter
@SuperBuilder
public class CxxyzhRes extends R {
    @LiveProperty(note = "信用账户信息", index = 1)
    private XyzhVo xyzhxx;
}
