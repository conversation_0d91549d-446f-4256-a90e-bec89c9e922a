package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.api.vo.khgl.DcwjxqVo;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjWjcsQuestions;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface TOdsYgtCifTpjWjcsQuestionsService extends IService<TOdsYgtCifTpjWjcsQuestions> {

    /**
     * 根据问卷参数id获取问卷问题
     * @param id
     * @return
     */
    List<DcwjxqVo> getQuestionByWjcsId(Long id);
}
