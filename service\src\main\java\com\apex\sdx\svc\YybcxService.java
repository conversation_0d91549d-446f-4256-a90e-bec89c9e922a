package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.YybcxReq;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.xtcs.YybVo;
import com.apex.sdx.convert.YybVoMapping;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.entity.Lborganization;
import com.apex.sdx.core.mybatis.service.LborganizationService;
import com.apexsoft.LiveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.query.IYybcxService;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @Date 2025-03-07
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "营业部已开通业务适当性查询服务")
public class YybcxService implements IYybcxService{

    @Autowired
    LborganizationService lborganizationService;

    @Autowired
    YybVoMapping yybVoMapping;

    @Override
    public QueryResponse<YybVo> yybcx(YybcxReq req) throws Exception {
        QueryResponse<YybVo> result = new QueryResponse<>(1, "查询成功");
        String orgcode = req.getOrgcode();

        List<Lborganization> list = lborganizationService.queryYybList(orgcode);

        List<YybVo> yybVos = yybVoMapping.listConver(list);

        result.setRecords(yybVos);

        return result;
    }
}
