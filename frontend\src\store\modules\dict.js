import dict from '@/api/xtgl/dict'

const getDefaultState = () => {
    return {
        // 字典map
        dictMap: {}
    }
}

const state = getDefaultState()

const mutations = {
    // 清除字典
    RESET_DICT_STATE: (state) => {
        Object.assign(state, getDefaultState())
    },
    // 保存字典项
    SAVE_DICT_ITEM: (state, data) => {
        let obj = {}
        obj[data.fldm] = data
        // 需要拷贝一份，要不然数据变动监听不到
        state.dictMap = Object.assign({}, state.dictMap, obj)
    }
}

const actions = {
    // 未登录时清除state
    clearDict({ commit }) {
        return new Promise((resolve) => {
            commit('RESET_DICT_STATE')
            resolve()
        })
    },
    // 获取字典的action
    getDictByFldm({ commit }, data) {
        if(!data.fldm) return
        return new Promise((resolve) => {
            if (state.dictMap[data.fldm]) {
                resolve()
            } else {
                // 根据字典类型请求后台数据
                dict.getDictByFldm(data.fldm).then((res) => {
                    commit('SAVE_DICT_ITEM', {
                        fldm: data.fldm,
                        items: res.records
                    })
                    resolve()
                })
            }
        })
    }
}

export default {
    namespaced: true,
    state,
    mutations,
    actions
}

