package sdx.common;

import com.apexsoft.LiveMethod;

/**
 * 微服务公共接口
 *
 * @param <Req>
 * @param <Resp>
 */
public interface IService<Req, Resp> {

    /**
     * 业务检查
     *
     * @param req 请求数据
     * @return
     * @throws Exception
     */
    @LiveMethod(paramAsRequestBody = true, note = "业务检查", hidden = true)
    Resp check(Req req);

    /**
     * 业务处理
     *
     * @param req 请求数据
     * @return
     * @throws Exception
     */
    @LiveMethod(paramAsRequestBody = true, note = "业务执行")
    Resp execute(Req req);

}
