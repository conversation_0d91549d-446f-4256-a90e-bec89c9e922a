package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.vo.query.QxbsdxyqslscxVo;
import com.apex.sdx.core.exception.BusinessException;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhbsdxyqs;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhbsdxyqsService;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTkhbsdxyqsMapper;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_ods_ygt_cif_tkhbsdxyqs】的数据库操作Service实现
* @createDate 2025-06-06 16:59:26
*/
@Service
public class TOdsYgtCifTkhbsdxyqsServiceImpl extends ServiceImpl<TOdsYgtCifTkhbsdxyqsMapper, TOdsYgtCifTkhbsdxyqs>
    implements TOdsYgtCifTkhbsdxyqsService{

    @Override
    public List<QxbsdxyqslscxVo> qxbsdxyqslscx(String khh,String ksrq, String jsrq, String cxywlb) {
        try {
            return this.baseMapper.qxbsdxyqslscx(khh,ksrq, jsrq, cxywlb);
        } catch (Exception e) {
            String note = String.format("查询权限不适当协议签署流水异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }
}




