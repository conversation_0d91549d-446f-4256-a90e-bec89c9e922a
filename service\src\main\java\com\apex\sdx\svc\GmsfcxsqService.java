package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.GmsfzyzsqcxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.khgl.GmsfzyzsqcxVo;
import com.apex.sdx.convert.GmsfzyzsqcxVoMapping;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTgmsfcxsq;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTgmsfcxsqService;
import com.apexsoft.LiveService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.query.IGmsfcxsqService;

/**
 * <AUTHOR>
 * @Date 2025-02-14
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "客户资料变更查询服务")
public class GmsfcxsqService implements IGmsfcxsqService {

    @Autowired
    TOdsYgtCifTgmsfcxsqService tgmsfcxsqService;

    @Autowired
    GmsfzyzsqcxVoMapping gmsfzyzsqcxVoMapping;

    @Override
    public QueryPageResponse<GmsfzyzsqcxVo> Gmsfzyzsqcx(GmsfzyzsqcxReq req) throws Exception {
        Assert.notNull(req, GmsfzyzsqcxReq::getKhxm, GmsfzyzsqcxReq::getZjlb, GmsfzyzsqcxReq::getZjbh);
        QueryPageResponse<GmsfzyzsqcxVo> result = new QueryPageResponse<>(1, "查询成功");

        Page<TOdsYgtCifTgmsfcxsq> page = tgmsfcxsqService.queryPageByConditions(req.getKhxm(), req.getZjlb(), req.getZjbh(), req.getKsrq(), req.getJsrq(), req.getXmhcjg(), req.getZjhcjg(), req.getRxbdjg(), req.getCxlx(), req.getCzlb(), req.isSearchCount(), req.getPagesize(), req.getPagenum());
        Page<GmsfzyzsqcxVo> gmsfzyzsqcxVoPage = gmsfzyzsqcxVoMapping.pageConver(page);

        result.page(gmsfzyzsqcxVoPage);

        return result;
    }
}
