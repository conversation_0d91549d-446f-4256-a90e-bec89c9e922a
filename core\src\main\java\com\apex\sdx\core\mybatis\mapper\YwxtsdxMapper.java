package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.api.vo.sdx.YwxtsdxTjVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务系统适当性统计Mapper
 */
public interface YwxtsdxMapper extends BaseMapper<Object> {
    
    /**
     * 根据日期统计业务系统适当性情况
     * @param rq 日期
     * @return 业务系统适当性统计列表
     */
    List<YwxtsdxTjVo> tjYwxtSdxByRq(@Param("rq") Integer rq);
} 