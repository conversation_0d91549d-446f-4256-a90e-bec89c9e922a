<template>
  <div class="h-full w-full">
<!--    <div class="mb-2 text-base text-darker font-bold">
      &lt;!&ndash; 由于原代码中 t 函数未使用，这里暂时保留占位 &ndash;&gt;
      {{ t('home.rcsjz') }}
    </div>-->
<!--    <div class="relative h-full w-full flex">-->
<!--  <div id="rcsjz-container" class="flex-1" style="width:100%;height: 250px;"></div>-->
    <div ref="containerGraph" class="relative h-full w-full flex">
      <div
          class="!absolute !right-4 !top-2"
          style="
          height: 35px;
          width: 170px;
          background-color: white;
          z-index: 999999;
          border-radius: 14px;
          box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.1);
        "
      >
        <div
            style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 100%;
            padding: 0 10px;
          "
        >
          <i class="icon-sx iconfont" style="cursor: pointer" @click="lowScale"></i>
          <div style="width: 30px; display: flex; justify-content: center">{{ scaleText }}</div>

          <i class="icon-fd iconfont" style="cursor: pointer" @click="addScale"></i>
          <i class="icon-zuixiaohua iconfont" style="cursor: pointer" @click="refresh"></i>
          <i
              v-if="!isFullScreen"
              class="icon-qp_tb iconfont"
              style="cursor: pointer"
              @click="showAllScreen"
          ></i>
          <i
              v-else
              class="icon-rqsjz_sx iconfont"
              style="cursor: pointer"
              @click="showAllScreen"
          ></i>
        </div>
      </div>
      <div id="rcsjz-container" class="flex-1 w-full"></div>
    </div>

  </div>
</template>

<script>
import { Graph } from '@antv/x6';
import { register } from '@antv/x6-vue-shape';
import TimeShaftLineNode from './TimeShaftLineNode.vue';
import TimerShaftNode from './TimeShaftNode.vue';

export default {
  name: "TimerShaft",
  props: ["TimerShaftData"],//从别的页面传递进来
  data() {
    return {
      daySchedules: [],
      scaleText: '100%',
      scale: 1,
      containerGraph: null,
      isFullScreen: false,
      graph: null,
      initialX: 0,
      reduceWidth: 300,
      scheduleDetailRef: null,
      showAddScheduleModal: false,
      editScheduleId: undefined,
      showScheduleDetail: false,
      selectedScheduleId: undefined
    };
  }, watch: {
    // 监听 events 变化
    TimerShaftData: {
      // immediate: true, // 立即触发一次
      handler(newVal) {
        /*
        [{"date":20241201,"title":"事件消除","id":3,"description":"金融产品风险不匹配","highlight":true},{"date":20241201,"title":"事件生成","id":4,"description":"投顾服务产品风险不匹配","highlight":false},{"date":20241201,"title":"事件生成","id":5,"description":"公募投顾产品风险不匹配","highlight":false},{"date":20241201,"title":"事件消除","id":6,"description":"客户适当性信息变更后对保有业务影响","highlight":true},{"date":20250401,"title":"事件生成","id":10,"description":"投资者即将到期评估","highlight":false},{"date":20250401,"title":"事件消除","id":9,"description":"年龄类适当性变化提醒","highlight":true},{"date":20250401,"title":"事件生成","id":7,"description":"客户风险评测过程分析","highlight":false},{"date":20250401,"title":"事件生成","id":8,"description":"专业投资者过期提醒","highlight":false}]
        */
        this.daySchedules = newVal.map((schedule) => ({
          id: schedule.id,
          start: schedule.date,
          description: schedule.description,
          highlight:schedule.highlight,
          title:schedule.title
        }));
      /*  this.daySchedules = newVal.map((schedule) => ({
          id: schedule.id,
          // start: schedule.scsj,
          start: schedule.scrq,
          // date: schedule.scrq,
          description: schedule.sjmc || '',
          highlight:!schedule.clzt == '0',
          title: (!schedule.clzt == '0') ? "事件消除" : "事件生成"
        }));*/

        if (this.graph) {
          this.graph.zoomTo(1);
          this.graph.translate(140, 140);
          const nodes = this.graph.getNodes();
          nodes.forEach((node1) => {
            this.graph.removeNode(node1);
          });
          this.initializeGraph();
        }
      }
    }
  },
  methods: {


    // 时间转换为像素
    timeToPixels(time) {
    /*  const [hours, minutes] = time.split(':').map(Number);
      return (hours * 60 + minutes) * (200 / 60);*/
      // const [hours, minutes] = time.split(':').map(Number);
      return time* (200 / 60);
    },
    // 初始化 Graph
    initializeGraph() {
      if (!this.graph) return;
      const firstTaskStart =
          this.daySchedules.length > 0 ? this.timeToPixels(this.daySchedules[0].start) : 0;
      const container = document.querySelector('#rcsjz-container');
      const containerWidth = container.clientWidth;
      this.initialX = Math.max(0, firstTaskStart - containerWidth / 2);
      this.reduceWidth = (containerWidth / 2 / 200 - 1) * 200;

      const uniqueStarts = [];
      const seenStarts = new Set();
      const startMap = new Map();

      this.daySchedules.forEach((item) => {
        if (!seenStarts.has(item.start)) {
          seenStarts.add(item.start);
          uniqueStarts.push(item.start);
        }
        const key = item.start;
        if (!startMap.has(key)) {
          startMap.set(key, []);
        }
        startMap.get(key).push(item);
      });

      uniqueStarts.forEach((time, index) => {
        this.graph.addNode({
          data: {
            tasks: this.daySchedules
          },
          id: time,
          shape: 'timer-shaft-line-node',
          x: index * 150,
          y: 0
        });
        if (index > 0) {
          this.addEdge(uniqueStarts[index - 1], time);
        }
        startMap.get(time).forEach((node, index1) => {
          const xSign = index % 2 === 0 ? -1 : 1;
          this.graph.addNode({
            data: {
              schedule: node,
              time,
              xSign
            },
            id: node.id,
            ports: {
              groups: {
                group1: {
                  attrs: {
                    circle: {
                      fill: '#bfbfbf',
                      magnet: true,
                      r: 6,
                      stroke: '#bfbfbf',
                      strokeWidth: 0
                    },
                    text: {
                      fill: '#888',
                      fontSize: 12
                    }
                  },
                  position: {
                    name: 'absolute'
                  }
                }
              },
              items: [
                {
                  args: {
                    x: 8,
                    y: xSign < 0 ? 10 : 63
                  },
                  group: 'group1',
                  id: `port${node.id}`
                }
              ]
            },
            shape: 'timer-shaft-node',
            x: index * 150,
            y: xSign < 0 ? xSign * (index1 + 1) * 100 : xSign * (index1 + 1) * 100 - 70
          });
          if (index1 === 0) {
            this.addEdge(time, node.id, true);
          } else {
            this.addEdge(startMap.get(time)[index1 - 1].id, node.id, true);
          }
        });
      });
    },
    // 添加边
    addEdge(sourceId, targetId, port = false) {
      this.graph.addEdge({
        attrs: {
          line: {
            stroke: '#bfbfbf',
            strokeWidth: 2,
            targetMarker: null
          }
        },
        source: {
          cell: sourceId,
          port: port ? `port${sourceId}` : undefined
        },
        target: {
          cell: targetId,
          port: port ? `port${targetId}` : undefined
        }
      });
    },
    // 刷新数据
    async refresh() {
      if (this.graph) {
        this.graph.zoomTo(1);
        this.graph.translate(140, 140);
        const nodes = this.graph.getNodes();
        nodes.forEach((node1) => {
          this.graph.removeNode(node1);
        });
        this.initializeGraph();
      }
    },
    // 处理全屏变化
    handleFullScreenChange() {
      if (document.fullscreenElement) {
        this.isFullScreen = true;
      } else {
        this.isFullScreen = false;
        this.refresh();
      }
    },
    // 全屏显示
    showAllScreen() {
      const container = document.querySelector('#rcsjz-container');
      if (!container) return;

      if (document.fullscreenElement) {
        document.exitFullscreen();
        this.isFullScreen = false;
      } else {
        container.requestFullscreen();
        this.isFullScreen = true;
        setTimeout(() => {
          this.graph?.resize(this.graph?.container.clientWidth, this.graph?.container.clientHeight);
          this.graph?.centerContent();
        }, 100);
      }
    },
    // 放大
    addScale() {
      this.graph.zoomTo(this.scale + 0.1);
    },
    // 缩小
    lowScale() {
      this.graph.zoomTo(this.scale - 0.1);
    }
  },
  mounted() {
    document.addEventListener('fullscreenchange', this.handleFullScreenChange);
    // this.fetchCalendarData().then(() => {
    //   this.$nextTick(() => {
        const container = document.querySelector('#rcsjz-container');
        // const width = container.clientWidth;
        // const height = container.clientHeight;
        // console.error("width:"+width);
        // console.error("height:"+width);
        this.graph = new Graph({
          autoResize: true,
          background: {
            color: '#FAFBFC'
          },
          container,
          grid: {
            type: 'dot',
            visible: true
          },
          interacting: {
            nodeMovable: false
          },
          mousewheel: {
            enabled: true,
            zoomAtMousePosition: true
          },
          panning: {
            enabled: true,
            eventTypes: ['leftMouseDown']
          },
          scaling: {
            max: 1.5,
            min: 0.5
          }
        });
        // this.graph.resize(width, height)
        this.graph.translate(140, 140);
        this.initializeGraph();
        this.graph.on('scale', ({ sx }) => {
          const scalePercent = Math.round(sx * 100);
          this.scaleText = `${scalePercent}%`;
          this.scale = sx;
        });
    //   });
    // });
  },
  beforeDestroy() {
    document.removeEventListener('fullscreenchange', this.handleFullScreenChange);
  },
  created() {
    register({
      component: TimerShaftNode,
      height: 80,
      shape: 'timer-shaft-node',
      width: 300
    });
    register({
      component: TimeShaftLineNode,
      height: 15,
      shape: 'timer-shaft-line-node',
      width: 15
    });
  }
};
</script>

<style >
/*:deep(.apexRed-modal-footer) {
  padding: 0 !important;
}*/
.flex {
  display: flex;
}
.h-full {
  height: 100%;
}
.w-full {
  width: 100%;
}
.relative {
  position: relative;
}
.\!top-2 {
  top: .5rem !important;
}
.\!right-4 {
  right: 1rem !important;
}
.\!absolute {
  position: absolute !important;
}
</style>
