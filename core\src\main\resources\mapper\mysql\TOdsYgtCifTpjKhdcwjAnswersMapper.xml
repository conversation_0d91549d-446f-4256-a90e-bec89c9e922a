<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTpjKhdcwjAnswersMapper">

    <select id="selectDacByWjid" resultType="java.lang.String">
        SELECT GROUP_CONCAT(
                       CONCAT(paperqid, '|', REPLACE(answer, ';', '-'))
                       ORDER BY paperqid
                       SEPARATOR ';'
               ) AS dac
        FROM ods.t_ods_ygt_cif_tpj_khdcwj_answers
        WHERE tpj_khdcwj_id = #{khdcwjid}
    </select>
</mapper>
