package com.apex.sdx.gateway.base.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.apex.ams.livebos.services.*;
import com.apex.sdx.gateway.base.model.CommonResponse;
import com.apex.sdx.gateway.base.dao.LivebosGrpcDao;
import com.apex.sdx.gateway.base.dao.UserDao;
import com.apex.sdx.gateway.base.model.JSONResponse;
import com.apex.sdx.gateway.common.redis.RedisStringDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

@Slf4j
@Service
public class WorkflowService {


    @Value("${server.livebos-path}")
    private String livebosPath;

    @Value("${application.debug}")
    boolean applicationDebug;

    @Autowired
    LivebosGrpcDao livebosGRPC;
    @Autowired
    RedisStringDao redisStringDao;
    @Autowired
    UserDao userDao;

    public JSONResponse queryWorkflowItems(String uid, boolean startUpOnly){
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询流程信息成功");
        JSONArray records = new JSONArray();
        try{
            QueryWorkflowItemsReply reply = livebosGRPC.queryWorkflowItems( uid, startUpOnly);
            Result result = reply.getResult();
            if(result.getResult()>0){
                List<WorkflowItemInfo> list = reply.getItemsList();
                for(WorkflowItemInfo workflowItemInfo:list){
                    JSONObject record = new JSONObject();
                    record.put("name", workflowItemInfo.getName());
                    record.put("describe", workflowItemInfo.getDescribe());
                    record.put("category", workflowItemInfo.getCategory());
                    record.put("type", workflowItemInfo.getType());
                    record.put("attribute", workflowItemInfo.getAttribute());
                    records.add(record);
                }
                response.setRecords(records);
            }else{
                response = new CommonResponse(JSONResponse.CODE_FAIL, result.getMessage());
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
            response = new CommonResponse(JSONResponse.CODE_FAIL, "查询流程信息失败:"+e.getMessage());
        }
        return response;
    }

    public JSONResponse createStartWorkflowHyperLink(String userid, String workflowName, Parameter parameter, int authFlag, int attribute){
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "创建流程链接成功");
        try{
            HyperLinkReply reply = livebosGRPC.createStartWorkflowHyperLink(userid, workflowName, parameter, authFlag, attribute);
            if(reply.getResult()>0){
                JSONObject data = new JSONObject();
                data.put("url",reply.getUrl());
                response.setData(data);
            }else{
                response = new CommonResponse(JSONResponse.CODE_FAIL, reply.getMessage());
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
            response = new CommonResponse(JSONResponse.CODE_FAIL, "查询流程信息失败:"+e.getMessage());
        }
        return response;
    }

    public JSONResponse createTransactWorkflowHyperLink(String userid, int instId, int stepId, List<Parameter> parameters){
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "创建流程链接成功");
        try{
            HyperLinkReply reply = livebosGRPC.createTransactWorkflowHyperLink(userid, instId, stepId, parameters, 0);
            if(reply.getResult()>0){
                JSONObject data = new JSONObject();
                data.put("url",reply.getUrl());
                response.setData(data);
            }else{
                response = new CommonResponse(JSONResponse.CODE_FAIL, reply.getMessage());
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
            response = new CommonResponse(JSONResponse.CODE_FAIL, "查询流程信息失败:"+e.getMessage());
        }
        return response;
    }

    public JSONResponse queryWorkTasks(String uid){
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询流程任务成功");

        try{
            Iterator<QueryReply> itQrs = livebosGRPC.queryWorkTasks(uid);
            JSONArray records =new JSONArray();
            if (itQrs.hasNext()) {
                QueryReply qrsMate = itQrs.next();
                QueryBaseInfo queryBaseInfo = qrsMate.getBaseInfo();
                if(queryBaseInfo.getResult()>0) {
                    response.setCount(queryBaseInfo.getCount());
                    List<String> th = new ArrayList<>();
                    for (Iterator<ColInfo> it = queryBaseInfo.getMetaData().getColInfoList().iterator(); it.hasNext(); ) {
                        ColInfo colInfo = it.next();
                        th.add(colInfo.getName());
                    }
                    int k = 0;
                    while (itQrs.hasNext()) {
                        k = 0;
                        QueryReply qrs = itQrs.next();
                        JSONObject item = new JSONObject();
                        for (Iterator<String> itVal = qrs.getRecord().getValuesList().iterator(); itVal.hasNext(); ) {
                            item.put(th.get(k++), itVal.next());
                        }
                        records.add(item);
                    }
                    response.setRecords(records);
                }else{
                    response = new CommonResponse(JSONResponse.CODE_FAIL, queryBaseInfo.getMessage());
                }
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
            response = new CommonResponse(JSONResponse.CODE_FAIL, "查询流程信息失败:"+e.getMessage());
        }
        return response;
    }
}
