package sdx.query;

import com.apex.sdx.api.req.query.GmsfzyzsqcxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.vo.khgl.GmsfzyzsqcxVo;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR>
 * @Date 2025-02-14
 * @Description:
 */
public interface IGmsfcxsqService {
    @LiveMethod(paramAsRequestBody = true, note = "公民身份证验证申请查询")
    QueryPageResponse<GmsfzyzsqcxVo> Gmsfzyzsqcx(GmsfzyzsqcxReq req) throws Exception;
}
