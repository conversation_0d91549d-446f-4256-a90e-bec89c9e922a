package com.apex.sdx.core.config;

import com.apex.sdx.core.adapter.*;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.core.incrementer.IKeyGenerator;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

@Slf4j
@Configuration
public class MybatisPlusConfig {

    @Value("${spring.datasource.url:}")
    private String url;

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        String dbtype = getDbType().toUpperCase();
        if ("MYSQL".equals(dbtype)) {
            interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        } else if ("DM".equals(dbtype)) {
            interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.DM));
        }
        return interceptor;
    }
    @Bean
    public IKeyGenerator keyGenerator() {
        //支持序列数据库适配器
        return new DBKeyGenerator();
        //不支持序列数据库适配器待补充
    }
    @Bean
    public ISqlAdapter sqlAdapter() {
        ISqlAdapter sqlAdapter = null;
        String dbType = this.getDbType();
        switch (dbType) {
            case "MYSQL":
                sqlAdapter = new MysqlAdapter();
                break;
            case "DM":
                sqlAdapter = new DmSqlAdapter();
                break;
            case "ORACLE":
                sqlAdapter = new OracleSqlAdapter();
                break;
            case "POSTGRESQL":
                sqlAdapter = new PgSqlAdapter();
                break;
            default:
                sqlAdapter = new OracleSqlAdapter();
                break;
        }
        return sqlAdapter;
    }

    /**
     * 代码增强，覆盖mybatis-plus原始BigDecimalTypeHandler改为自定义MyBigDecimalTypeHandler
     * <AUTHOR>
     * @date 2021/7/12
     * @param
     * @return : com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer
     */
    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return configuration -> {
            //代码增强，实现插入数据库和返回数据的时候bigDecimal末尾0去除
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            typeHandlerRegistry.register(BigDecimal.class, new BigDecimalTypeHandler());
            typeHandlerRegistry.register(JdbcType.NUMERIC, new BigDecimalTypeHandler());
            typeHandlerRegistry.register(JdbcType.DECIMAL, new BigDecimalTypeHandler());
        };
    }

    private String getDbType() {
        String[] strs = url.split(":");
        if (strs.length < 2) {
            log.error("数据库连接地址未配置，解析数据库类型异常！！");
            return "";
        }
        return strs[1].toUpperCase();
    }

}
