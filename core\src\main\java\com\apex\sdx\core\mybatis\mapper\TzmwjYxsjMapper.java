package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.api.vo.query.ZmwjyxsjVo;
import com.apex.sdx.core.mybatis.entity.TzmwjYxsj;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Entity com.apex.sdx.core.mybatis.entity.TzmwjYxsj
 */
public interface TzmwjYxsjMapper extends BaseMapper<TzmwjYxsj> {

    @Select("select a.*, b.ZMWJ " +
            "from sdx.tzmwj_yxsj a " +
            "         left join sdx.tzmwj_yxlxdy b on a.ZMWJID = b.ID " +
            "where a.khh = #{khh} ")
    List<ZmwjyxsjVo> selectZmwjyxsj(@Param("khh") String khh);
}




