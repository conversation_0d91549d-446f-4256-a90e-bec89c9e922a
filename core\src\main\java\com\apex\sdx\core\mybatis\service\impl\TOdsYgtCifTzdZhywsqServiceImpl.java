package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.resp.common.Response;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTgmsfcxsq;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTzdZhywsq;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTzdZhywsqService;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTzdZhywsqMapper;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class TOdsYgtCifTzdZhywsqServiceImpl extends ServiceImpl<TOdsYgtCifTzdZhywsqMapper, TOdsYgtCifTzdZhywsq>
    implements TOdsYgtCifTzdZhywsqService{

    @Override
    public Page<TOdsYgtCifTzdZhywsq> queryJgkhxxhcsqByConditionos(String khh, Integer ksrq, Integer jsrq, boolean isSearchCount, int pagesize, int pagenum) {
        Page<TOdsYgtCifTzdZhywsq> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);

        try {
            LambdaQueryWrapper<TOdsYgtCifTzdZhywsq> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TOdsYgtCifTzdZhywsq::getKhh, khh);
            wrapper.eq(TOdsYgtCifTzdZhywsq::getYwlb, 301);
            wrapper.ge(ksrq != null, TOdsYgtCifTzdZhywsq::getSqrq, ksrq);
            wrapper.le(jsrq != null, TOdsYgtCifTzdZhywsq::getSqrq, jsrq);
            wrapper.orderByDesc(TOdsYgtCifTzdZhywsq::getId);
            this.page(page, wrapper);
            return page;

        } catch (Exception e) {
            String note = String.format("获取机构客户信息核查申请失败[%s]", e.getMessage());
            log.error(note, e);
            throw new BusinessException(Response.buildErrorMsg(-601, note));
        }
    }
}




