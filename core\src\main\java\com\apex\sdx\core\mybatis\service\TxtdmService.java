package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.core.mybatis.entity.Txtdm;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 *
 */
public interface TxtdmService extends IService<Txtdm> {

    List<Txtdm> queryDict(String fldm);

    Map<String, Object> getDictMap(String fldm, String keyField, String valueField);

    String getNoteByIbm(String fldm, String ibm);

    /**
     * 清除缓存
     */
    void refreshAll();

    /**
     * 根据cbm获取ibm
     * @param fldm
     * @param cbm
     * @return
     */
    String getIbmByCbm(String fldm, String cbm);
}
