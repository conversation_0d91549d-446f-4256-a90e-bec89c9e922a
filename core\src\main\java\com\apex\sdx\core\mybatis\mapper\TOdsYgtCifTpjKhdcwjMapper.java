package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjKhdcwj;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @Entity com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjKhdcwj
 */
public interface TOdsYgtCifTpjKhdcwjMapper extends BaseMapper<TOdsYgtCifTpjKhdcwj> {


    @Select({"<script>"+
            "SELECT ${maxSql} " +
            "FROM ods.t_ods_ygt_cif_TPJ_KHDCWJ b " +
            "   LEFT JOIN ods.t_ods_ygt_cif_tpj_wjcs c ON b.paperid = c.id  " +
            "WHERE c.sdxfl = #{sdxfl} " +
            "   AND c.sdxlb = #{sdxlbIbm} " +
            "   AND (c.khlb = #{khlb} " +
            "       OR c.khlb IS NULL) " +
            "   AND c.wjzt = 0 " +
            "   AND (b.khh = #{khh} " +
            "       OR (b.khh IS NULL " +
            "           AND b.zjlb = #{zjlb} " +
            "           AND b.zjbh = #{zjbh} " +
            "           AND b.khjc = #{khjc}) " +
            "   <if test='sdxfl == 1 and sdxlbCbm == \"JJFXCSNL\"'>" +
            "       OR b.cid = #{cid}"+
            "   </if>)" +
            "</script>"})
    Long selectMaxIdByKhsdx(@Param("maxSql")String maxSql, @Param("sdxfl")Integer sdxfl, @Param("sdxlbCbm")String sdxlbCbm,
                            @Param("sdxlbIbm")String sdxlbIbm, @Param("khlb")Integer khlb, @Param("khh")String khh,
                            @Param("zjlb")Integer zjlb, @Param("zjbh")String zjbh, @Param("khjc")String khjc, @Param("cid")String cid);
}




