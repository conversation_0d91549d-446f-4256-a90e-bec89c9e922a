package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.JyrReq;
import com.apex.sdx.api.resp.query.JyrRes;
import com.apex.sdx.convert.JyrVoMapping;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.entity.TLdTradingCalendar;
import com.apex.sdx.core.mybatis.service.TLdTradingCalendarService;
import com.apex.sdx.core.utils.DateUtil;
import com.apexsoft.LiveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.query.IJyrService;

@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "交易日查询服务")
@RequiredArgsConstructor
public class JyrService implements IJyrService {

    private final TLdTradingCalendarService tLdTradingCalendarService;

    @Autowired
    private JyrVoMapping jyrVoMapping;

    @Override
    public JyrRes cxjyr(JyrReq req) throws Exception {

        String zrr = DateUtil.getDateModifyDay(req.getZrr(), req.getTs(), "yyyyMMdd");

        TLdTradingCalendar one = tLdTradingCalendarService.queryJyrByZrr(zrr);
        JyrRes map = jyrVoMapping.map(one);
        map.setCode(1);
        map.setNote("查询成功");

        return map;
    }
}
