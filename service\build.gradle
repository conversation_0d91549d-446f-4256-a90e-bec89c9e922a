plugins {
    id 'java'
    //springBoot插件
    id 'org.springframework.boot' version "${springBootVersion}" apply false
    //spring依赖管理插件
    id 'io.spring.dependency-management' version "${springDependencyManagement}"
    id 'java-library'
}

dependencyManagement {
    imports {
        mavenBom org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        mavenBom "com.apexsoft:live-spring-boot2-bom:${liveVersion}"
    }
    resolutionStrategy{
        cacheChangingModulesFor 0, 'seconds'
    }
}
compileJava {
    options.compilerArgs << '-parameters'
}
compileTestJava{
    options.compilerArgs << '-parameters'
}
group = 'com.apexsoft'
version = '1.0.0'
//依赖
dependencies {
    //lombok
    compileOnly 'org.projectlombok:lombok:1.18.24'
    annotationProcessor 'org.projectlombok:lombok:1.18.24'
    implementation(project(":core"))
    //implementation(project(":gateway")) 确认这个不需要
    implementation group: 'org.mapstruct', name: 'mapstruct', version: '1.5.3.Final'
    annotationProcessor "org.mapstruct:mapstruct-processor:1.5.3.Final"
    testAnnotationProcessor "org.mapstruct:mapstruct-processor:1.5.3.Final"
}
//单元测试
test {
    useJUnitPlatform()
}

//依赖缓存时间
configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}
configurations {
    all*.exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    all*.exclude group:"org.slf4j",module:"slf4j-log4j12"
}
jar {
    baseName 'sdx-service'
    version ''
}