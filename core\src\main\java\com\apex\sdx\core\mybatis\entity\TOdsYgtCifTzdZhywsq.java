package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName t_ods_ygt_cif_tzd_zhywsq
 */
@TableName(value ="t_ods_ygt_cif_tzd_zhywsq", schema = "ods")
@Data
public class TOdsYgtCifTzdZhywsq implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 
     */
    private Long sqbh;

    /**
     * 
     */
    private Integer sqrq;

    /**
     * 
     */
    private String sqsj;

    /**
     * 
     */
    private Long jys;

    /**
     * 
     */
    private Long ywlb;

    /**
     * 
     */
    private String czlb;

    /**
     * 
     */
    private Long cdbz;

    /**
     * 
     */
    private Long yyb;

    /**
     * 
     */
    private Long sqgy;

    /**
     * 
     */
    private Long shgy;

    /**
     * 
     */
    private String shyj;

    /**
     * 
     */
    private String khjgdm;

    /**
     * 
     */
    private String khwddm;

    /**
     * 
     */
    private String khh;

    /**
     * 
     */
    private Long clbz;

    /**
     * 
     */
    private String jgsm;

    /**
     * 
     */
    private Long ywpzbs;

    /**
     * 
     */
    private Integer sbrq;

    /**
     * 
     */
    private String sbsj;

    /**
     * 
     */
    private Long ywph;

    /**
     * 
     */
    private String wfId;

    /**
     * 
     */
    private String ymth;

    /**
     * 
     */
    private String khmc;

    /**
     * 
     */
    private Long khlb;

    /**
     * 
     */
    private Long gjdm;

    /**
     * 
     */
    private Long zjlb;

    /**
     * 
     */
    private String zjbh;

    /**
     * 
     */
    private Integer zjjzrq;

    /**
     * 
     */
    private String zjdz;

    /**
     * 
     */
    private Long fzzjlb;

    /**
     * 
     */
    private String fzzjbh;

    /**
     * 
     */
    private Integer fzzjjzrq;

    /**
     * 
     */
    private String fzzjdz;

    /**
     * 
     */
    private Long khfs;

    /**
     * 
     */
    private Integer csrq;

    /**
     * 
     */
    private Long xb;

    /**
     * 
     */
    private Long xldm;

    /**
     * 
     */
    private Long zyxz;

    /**
     * 
     */
    private Long mzdm;

    /**
     * 
     */
    private Long jglb;

    /**
     * 
     */
    private Long zbsx;

    /**
     * 
     */
    private Long gysx;

    /**
     * 
     */
    private String jgjc;

    /**
     * 
     */
    private String ywmc;

    /**
     * 
     */
    private String gswz;

    /**
     * 
     */
    private String frxm;

    /**
     * 
     */
    private Long frzjlb;

    /**
     * 
     */
    private String frzjbh;

    /**
     * 
     */
    private String lxrxm;

    /**
     * 
     */
    private Long lxrzjlb;

    /**
     * 
     */
    private String lxrzjbh;

    /**
     * 
     */
    private String yddh;

    /**
     * 
     */
    private String gddh;

    /**
     * 
     */
    private String czhm;

    /**
     * 
     */
    private String lxdz;

    /**
     * 
     */
    private String lxyb;

    /**
     * 
     */
    private String dzyx;

    /**
     * 
     */
    private Long dxfwbs;

    /**
     * 
     */
    private Long wlfwbs;

    /**
     * 
     */
    private String wlmm;

    /**
     * 
     */
    private Long zhlb;

    /**
     * 
     */
    private String zqzh;

    /**
     * 
     */
    private String xymth;

    /**
     * 
     */
    private Long xzjlb;

    /**
     * 
     */
    private String xzjbh;

    /**
     * 
     */
    private String phzqzh;

    /**
     * 
     */
    private String jscyr;

    /**
     * 
     */
    private String jydy;

    /**
     * 
     */
    private String yybbm;

    /**
     * 
     */
    private Long sdxlb;

    /**
     * 
     */
    private Long qylb;

    /**
     * 
     */
    private Integer qyrq;

    /**
     * 
     */
    private Long hhcdfs;

    /**
     * 
     */
    private Long jcxzlb;

    /**
     * 
     */
    private String byzd1;

    /**
     * 
     */
    private String byzd2;

    /**
     * 
     */
    private String byzd3;

    /**
     * 
     */
    private Long ywqqid;

    /**
     * 
     */
    private Long ywqqclid;

    /**
     * 
     */
    private String cpjc;

    /**
     * 
     */
    private Integer cpdqr;

    /**
     * 
     */
    private Long cplb;

    /**
     * 
     */
    private String glrmc;

    /**
     * 
     */
    private Long glrzjlb;

    /**
     * 
     */
    private String glrzjbh;

    /**
     * 
     */
    private String tgrmc;

    /**
     * 
     */
    private Long tgrzjlb;

    /**
     * 
     */
    private String tgrzjbh;

    /**
     * 
     */
    private Long clzt;

    /**
     * 
     */
    private String clcx;

    /**
     * 
     */
    private String byzd;

    /**
     * 
     */
    private String tdbh;

    /**
     * 
     */
    private String bz;

    /**
     * 
     */
    private String tazh;

    /**
     * 
     */
    private Long tazhlb;

    /**
     * 
     */
    private Long tazjlb;

    /**
     * 
     */
    private Long zblb;

    /**
     * 
     */
    private String zqdm;

    /**
     * 
     */
    private String gczqzh;

    /**
     * 
     */
    private String gctgdy;

    /**
     * 
     */
    private String grzqzh;

    /**
     * 
     */
    private String grtgdy;

    /**
     * 
     */
    private String byzd4;

    /**
     * 
     */
    private String tzzmc;

    /**
     * 
     */
    private String zhaiqhm;

    /**
     * 
     */
    private String zhaiqkhmc;

    /**
     * 
     */
    private Long zhaiqzjlb;

    /**
     * 
     */
    private String zhaiqzjdm;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TOdsYgtCifTzdZhywsq other = (TOdsYgtCifTzdZhywsq) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSqbh() == null ? other.getSqbh() == null : this.getSqbh().equals(other.getSqbh()))
            && (this.getSqrq() == null ? other.getSqrq() == null : this.getSqrq().equals(other.getSqrq()))
            && (this.getSqsj() == null ? other.getSqsj() == null : this.getSqsj().equals(other.getSqsj()))
            && (this.getJys() == null ? other.getJys() == null : this.getJys().equals(other.getJys()))
            && (this.getYwlb() == null ? other.getYwlb() == null : this.getYwlb().equals(other.getYwlb()))
            && (this.getCzlb() == null ? other.getCzlb() == null : this.getCzlb().equals(other.getCzlb()))
            && (this.getCdbz() == null ? other.getCdbz() == null : this.getCdbz().equals(other.getCdbz()))
            && (this.getYyb() == null ? other.getYyb() == null : this.getYyb().equals(other.getYyb()))
            && (this.getSqgy() == null ? other.getSqgy() == null : this.getSqgy().equals(other.getSqgy()))
            && (this.getShgy() == null ? other.getShgy() == null : this.getShgy().equals(other.getShgy()))
            && (this.getShyj() == null ? other.getShyj() == null : this.getShyj().equals(other.getShyj()))
            && (this.getKhjgdm() == null ? other.getKhjgdm() == null : this.getKhjgdm().equals(other.getKhjgdm()))
            && (this.getKhwddm() == null ? other.getKhwddm() == null : this.getKhwddm().equals(other.getKhwddm()))
            && (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
            && (this.getClbz() == null ? other.getClbz() == null : this.getClbz().equals(other.getClbz()))
            && (this.getJgsm() == null ? other.getJgsm() == null : this.getJgsm().equals(other.getJgsm()))
            && (this.getYwpzbs() == null ? other.getYwpzbs() == null : this.getYwpzbs().equals(other.getYwpzbs()))
            && (this.getSbrq() == null ? other.getSbrq() == null : this.getSbrq().equals(other.getSbrq()))
            && (this.getSbsj() == null ? other.getSbsj() == null : this.getSbsj().equals(other.getSbsj()))
            && (this.getYwph() == null ? other.getYwph() == null : this.getYwph().equals(other.getYwph()))
            && (this.getWfId() == null ? other.getWfId() == null : this.getWfId().equals(other.getWfId()))
            && (this.getYmth() == null ? other.getYmth() == null : this.getYmth().equals(other.getYmth()))
            && (this.getKhmc() == null ? other.getKhmc() == null : this.getKhmc().equals(other.getKhmc()))
            && (this.getKhlb() == null ? other.getKhlb() == null : this.getKhlb().equals(other.getKhlb()))
            && (this.getGjdm() == null ? other.getGjdm() == null : this.getGjdm().equals(other.getGjdm()))
            && (this.getZjlb() == null ? other.getZjlb() == null : this.getZjlb().equals(other.getZjlb()))
            && (this.getZjbh() == null ? other.getZjbh() == null : this.getZjbh().equals(other.getZjbh()))
            && (this.getZjjzrq() == null ? other.getZjjzrq() == null : this.getZjjzrq().equals(other.getZjjzrq()))
            && (this.getZjdz() == null ? other.getZjdz() == null : this.getZjdz().equals(other.getZjdz()))
            && (this.getFzzjlb() == null ? other.getFzzjlb() == null : this.getFzzjlb().equals(other.getFzzjlb()))
            && (this.getFzzjbh() == null ? other.getFzzjbh() == null : this.getFzzjbh().equals(other.getFzzjbh()))
            && (this.getFzzjjzrq() == null ? other.getFzzjjzrq() == null : this.getFzzjjzrq().equals(other.getFzzjjzrq()))
            && (this.getFzzjdz() == null ? other.getFzzjdz() == null : this.getFzzjdz().equals(other.getFzzjdz()))
            && (this.getKhfs() == null ? other.getKhfs() == null : this.getKhfs().equals(other.getKhfs()))
            && (this.getCsrq() == null ? other.getCsrq() == null : this.getCsrq().equals(other.getCsrq()))
            && (this.getXb() == null ? other.getXb() == null : this.getXb().equals(other.getXb()))
            && (this.getXldm() == null ? other.getXldm() == null : this.getXldm().equals(other.getXldm()))
            && (this.getZyxz() == null ? other.getZyxz() == null : this.getZyxz().equals(other.getZyxz()))
            && (this.getMzdm() == null ? other.getMzdm() == null : this.getMzdm().equals(other.getMzdm()))
            && (this.getJglb() == null ? other.getJglb() == null : this.getJglb().equals(other.getJglb()))
            && (this.getZbsx() == null ? other.getZbsx() == null : this.getZbsx().equals(other.getZbsx()))
            && (this.getGysx() == null ? other.getGysx() == null : this.getGysx().equals(other.getGysx()))
            && (this.getJgjc() == null ? other.getJgjc() == null : this.getJgjc().equals(other.getJgjc()))
            && (this.getYwmc() == null ? other.getYwmc() == null : this.getYwmc().equals(other.getYwmc()))
            && (this.getGswz() == null ? other.getGswz() == null : this.getGswz().equals(other.getGswz()))
            && (this.getFrxm() == null ? other.getFrxm() == null : this.getFrxm().equals(other.getFrxm()))
            && (this.getFrzjlb() == null ? other.getFrzjlb() == null : this.getFrzjlb().equals(other.getFrzjlb()))
            && (this.getFrzjbh() == null ? other.getFrzjbh() == null : this.getFrzjbh().equals(other.getFrzjbh()))
            && (this.getLxrxm() == null ? other.getLxrxm() == null : this.getLxrxm().equals(other.getLxrxm()))
            && (this.getLxrzjlb() == null ? other.getLxrzjlb() == null : this.getLxrzjlb().equals(other.getLxrzjlb()))
            && (this.getLxrzjbh() == null ? other.getLxrzjbh() == null : this.getLxrzjbh().equals(other.getLxrzjbh()))
            && (this.getYddh() == null ? other.getYddh() == null : this.getYddh().equals(other.getYddh()))
            && (this.getGddh() == null ? other.getGddh() == null : this.getGddh().equals(other.getGddh()))
            && (this.getCzhm() == null ? other.getCzhm() == null : this.getCzhm().equals(other.getCzhm()))
            && (this.getLxdz() == null ? other.getLxdz() == null : this.getLxdz().equals(other.getLxdz()))
            && (this.getLxyb() == null ? other.getLxyb() == null : this.getLxyb().equals(other.getLxyb()))
            && (this.getDzyx() == null ? other.getDzyx() == null : this.getDzyx().equals(other.getDzyx()))
            && (this.getDxfwbs() == null ? other.getDxfwbs() == null : this.getDxfwbs().equals(other.getDxfwbs()))
            && (this.getWlfwbs() == null ? other.getWlfwbs() == null : this.getWlfwbs().equals(other.getWlfwbs()))
            && (this.getWlmm() == null ? other.getWlmm() == null : this.getWlmm().equals(other.getWlmm()))
            && (this.getZhlb() == null ? other.getZhlb() == null : this.getZhlb().equals(other.getZhlb()))
            && (this.getZqzh() == null ? other.getZqzh() == null : this.getZqzh().equals(other.getZqzh()))
            && (this.getXymth() == null ? other.getXymth() == null : this.getXymth().equals(other.getXymth()))
            && (this.getXzjlb() == null ? other.getXzjlb() == null : this.getXzjlb().equals(other.getXzjlb()))
            && (this.getXzjbh() == null ? other.getXzjbh() == null : this.getXzjbh().equals(other.getXzjbh()))
            && (this.getPhzqzh() == null ? other.getPhzqzh() == null : this.getPhzqzh().equals(other.getPhzqzh()))
            && (this.getJscyr() == null ? other.getJscyr() == null : this.getJscyr().equals(other.getJscyr()))
            && (this.getJydy() == null ? other.getJydy() == null : this.getJydy().equals(other.getJydy()))
            && (this.getYybbm() == null ? other.getYybbm() == null : this.getYybbm().equals(other.getYybbm()))
            && (this.getSdxlb() == null ? other.getSdxlb() == null : this.getSdxlb().equals(other.getSdxlb()))
            && (this.getQylb() == null ? other.getQylb() == null : this.getQylb().equals(other.getQylb()))
            && (this.getQyrq() == null ? other.getQyrq() == null : this.getQyrq().equals(other.getQyrq()))
            && (this.getHhcdfs() == null ? other.getHhcdfs() == null : this.getHhcdfs().equals(other.getHhcdfs()))
            && (this.getJcxzlb() == null ? other.getJcxzlb() == null : this.getJcxzlb().equals(other.getJcxzlb()))
            && (this.getByzd1() == null ? other.getByzd1() == null : this.getByzd1().equals(other.getByzd1()))
            && (this.getByzd2() == null ? other.getByzd2() == null : this.getByzd2().equals(other.getByzd2()))
            && (this.getByzd3() == null ? other.getByzd3() == null : this.getByzd3().equals(other.getByzd3()))
            && (this.getYwqqid() == null ? other.getYwqqid() == null : this.getYwqqid().equals(other.getYwqqid()))
            && (this.getYwqqclid() == null ? other.getYwqqclid() == null : this.getYwqqclid().equals(other.getYwqqclid()))
            && (this.getCpjc() == null ? other.getCpjc() == null : this.getCpjc().equals(other.getCpjc()))
            && (this.getCpdqr() == null ? other.getCpdqr() == null : this.getCpdqr().equals(other.getCpdqr()))
            && (this.getCplb() == null ? other.getCplb() == null : this.getCplb().equals(other.getCplb()))
            && (this.getGlrmc() == null ? other.getGlrmc() == null : this.getGlrmc().equals(other.getGlrmc()))
            && (this.getGlrzjlb() == null ? other.getGlrzjlb() == null : this.getGlrzjlb().equals(other.getGlrzjlb()))
            && (this.getGlrzjbh() == null ? other.getGlrzjbh() == null : this.getGlrzjbh().equals(other.getGlrzjbh()))
            && (this.getTgrmc() == null ? other.getTgrmc() == null : this.getTgrmc().equals(other.getTgrmc()))
            && (this.getTgrzjlb() == null ? other.getTgrzjlb() == null : this.getTgrzjlb().equals(other.getTgrzjlb()))
            && (this.getTgrzjbh() == null ? other.getTgrzjbh() == null : this.getTgrzjbh().equals(other.getTgrzjbh()))
            && (this.getClzt() == null ? other.getClzt() == null : this.getClzt().equals(other.getClzt()))
            && (this.getClcx() == null ? other.getClcx() == null : this.getClcx().equals(other.getClcx()))
            && (this.getByzd() == null ? other.getByzd() == null : this.getByzd().equals(other.getByzd()))
            && (this.getTdbh() == null ? other.getTdbh() == null : this.getTdbh().equals(other.getTdbh()))
            && (this.getBz() == null ? other.getBz() == null : this.getBz().equals(other.getBz()))
            && (this.getTazh() == null ? other.getTazh() == null : this.getTazh().equals(other.getTazh()))
            && (this.getTazhlb() == null ? other.getTazhlb() == null : this.getTazhlb().equals(other.getTazhlb()))
            && (this.getTazjlb() == null ? other.getTazjlb() == null : this.getTazjlb().equals(other.getTazjlb()))
            && (this.getZblb() == null ? other.getZblb() == null : this.getZblb().equals(other.getZblb()))
            && (this.getZqdm() == null ? other.getZqdm() == null : this.getZqdm().equals(other.getZqdm()))
            && (this.getGczqzh() == null ? other.getGczqzh() == null : this.getGczqzh().equals(other.getGczqzh()))
            && (this.getGctgdy() == null ? other.getGctgdy() == null : this.getGctgdy().equals(other.getGctgdy()))
            && (this.getGrzqzh() == null ? other.getGrzqzh() == null : this.getGrzqzh().equals(other.getGrzqzh()))
            && (this.getGrtgdy() == null ? other.getGrtgdy() == null : this.getGrtgdy().equals(other.getGrtgdy()))
            && (this.getByzd4() == null ? other.getByzd4() == null : this.getByzd4().equals(other.getByzd4()))
            && (this.getTzzmc() == null ? other.getTzzmc() == null : this.getTzzmc().equals(other.getTzzmc()))
            && (this.getZhaiqhm() == null ? other.getZhaiqhm() == null : this.getZhaiqhm().equals(other.getZhaiqhm()))
            && (this.getZhaiqkhmc() == null ? other.getZhaiqkhmc() == null : this.getZhaiqkhmc().equals(other.getZhaiqkhmc()))
            && (this.getZhaiqzjlb() == null ? other.getZhaiqzjlb() == null : this.getZhaiqzjlb().equals(other.getZhaiqzjlb()))
            && (this.getZhaiqzjdm() == null ? other.getZhaiqzjdm() == null : this.getZhaiqzjdm().equals(other.getZhaiqzjdm()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSqbh() == null) ? 0 : getSqbh().hashCode());
        result = prime * result + ((getSqrq() == null) ? 0 : getSqrq().hashCode());
        result = prime * result + ((getSqsj() == null) ? 0 : getSqsj().hashCode());
        result = prime * result + ((getJys() == null) ? 0 : getJys().hashCode());
        result = prime * result + ((getYwlb() == null) ? 0 : getYwlb().hashCode());
        result = prime * result + ((getCzlb() == null) ? 0 : getCzlb().hashCode());
        result = prime * result + ((getCdbz() == null) ? 0 : getCdbz().hashCode());
        result = prime * result + ((getYyb() == null) ? 0 : getYyb().hashCode());
        result = prime * result + ((getSqgy() == null) ? 0 : getSqgy().hashCode());
        result = prime * result + ((getShgy() == null) ? 0 : getShgy().hashCode());
        result = prime * result + ((getShyj() == null) ? 0 : getShyj().hashCode());
        result = prime * result + ((getKhjgdm() == null) ? 0 : getKhjgdm().hashCode());
        result = prime * result + ((getKhwddm() == null) ? 0 : getKhwddm().hashCode());
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getClbz() == null) ? 0 : getClbz().hashCode());
        result = prime * result + ((getJgsm() == null) ? 0 : getJgsm().hashCode());
        result = prime * result + ((getYwpzbs() == null) ? 0 : getYwpzbs().hashCode());
        result = prime * result + ((getSbrq() == null) ? 0 : getSbrq().hashCode());
        result = prime * result + ((getSbsj() == null) ? 0 : getSbsj().hashCode());
        result = prime * result + ((getYwph() == null) ? 0 : getYwph().hashCode());
        result = prime * result + ((getWfId() == null) ? 0 : getWfId().hashCode());
        result = prime * result + ((getYmth() == null) ? 0 : getYmth().hashCode());
        result = prime * result + ((getKhmc() == null) ? 0 : getKhmc().hashCode());
        result = prime * result + ((getKhlb() == null) ? 0 : getKhlb().hashCode());
        result = prime * result + ((getGjdm() == null) ? 0 : getGjdm().hashCode());
        result = prime * result + ((getZjlb() == null) ? 0 : getZjlb().hashCode());
        result = prime * result + ((getZjbh() == null) ? 0 : getZjbh().hashCode());
        result = prime * result + ((getZjjzrq() == null) ? 0 : getZjjzrq().hashCode());
        result = prime * result + ((getZjdz() == null) ? 0 : getZjdz().hashCode());
        result = prime * result + ((getFzzjlb() == null) ? 0 : getFzzjlb().hashCode());
        result = prime * result + ((getFzzjbh() == null) ? 0 : getFzzjbh().hashCode());
        result = prime * result + ((getFzzjjzrq() == null) ? 0 : getFzzjjzrq().hashCode());
        result = prime * result + ((getFzzjdz() == null) ? 0 : getFzzjdz().hashCode());
        result = prime * result + ((getKhfs() == null) ? 0 : getKhfs().hashCode());
        result = prime * result + ((getCsrq() == null) ? 0 : getCsrq().hashCode());
        result = prime * result + ((getXb() == null) ? 0 : getXb().hashCode());
        result = prime * result + ((getXldm() == null) ? 0 : getXldm().hashCode());
        result = prime * result + ((getZyxz() == null) ? 0 : getZyxz().hashCode());
        result = prime * result + ((getMzdm() == null) ? 0 : getMzdm().hashCode());
        result = prime * result + ((getJglb() == null) ? 0 : getJglb().hashCode());
        result = prime * result + ((getZbsx() == null) ? 0 : getZbsx().hashCode());
        result = prime * result + ((getGysx() == null) ? 0 : getGysx().hashCode());
        result = prime * result + ((getJgjc() == null) ? 0 : getJgjc().hashCode());
        result = prime * result + ((getYwmc() == null) ? 0 : getYwmc().hashCode());
        result = prime * result + ((getGswz() == null) ? 0 : getGswz().hashCode());
        result = prime * result + ((getFrxm() == null) ? 0 : getFrxm().hashCode());
        result = prime * result + ((getFrzjlb() == null) ? 0 : getFrzjlb().hashCode());
        result = prime * result + ((getFrzjbh() == null) ? 0 : getFrzjbh().hashCode());
        result = prime * result + ((getLxrxm() == null) ? 0 : getLxrxm().hashCode());
        result = prime * result + ((getLxrzjlb() == null) ? 0 : getLxrzjlb().hashCode());
        result = prime * result + ((getLxrzjbh() == null) ? 0 : getLxrzjbh().hashCode());
        result = prime * result + ((getYddh() == null) ? 0 : getYddh().hashCode());
        result = prime * result + ((getGddh() == null) ? 0 : getGddh().hashCode());
        result = prime * result + ((getCzhm() == null) ? 0 : getCzhm().hashCode());
        result = prime * result + ((getLxdz() == null) ? 0 : getLxdz().hashCode());
        result = prime * result + ((getLxyb() == null) ? 0 : getLxyb().hashCode());
        result = prime * result + ((getDzyx() == null) ? 0 : getDzyx().hashCode());
        result = prime * result + ((getDxfwbs() == null) ? 0 : getDxfwbs().hashCode());
        result = prime * result + ((getWlfwbs() == null) ? 0 : getWlfwbs().hashCode());
        result = prime * result + ((getWlmm() == null) ? 0 : getWlmm().hashCode());
        result = prime * result + ((getZhlb() == null) ? 0 : getZhlb().hashCode());
        result = prime * result + ((getZqzh() == null) ? 0 : getZqzh().hashCode());
        result = prime * result + ((getXymth() == null) ? 0 : getXymth().hashCode());
        result = prime * result + ((getXzjlb() == null) ? 0 : getXzjlb().hashCode());
        result = prime * result + ((getXzjbh() == null) ? 0 : getXzjbh().hashCode());
        result = prime * result + ((getPhzqzh() == null) ? 0 : getPhzqzh().hashCode());
        result = prime * result + ((getJscyr() == null) ? 0 : getJscyr().hashCode());
        result = prime * result + ((getJydy() == null) ? 0 : getJydy().hashCode());
        result = prime * result + ((getYybbm() == null) ? 0 : getYybbm().hashCode());
        result = prime * result + ((getSdxlb() == null) ? 0 : getSdxlb().hashCode());
        result = prime * result + ((getQylb() == null) ? 0 : getQylb().hashCode());
        result = prime * result + ((getQyrq() == null) ? 0 : getQyrq().hashCode());
        result = prime * result + ((getHhcdfs() == null) ? 0 : getHhcdfs().hashCode());
        result = prime * result + ((getJcxzlb() == null) ? 0 : getJcxzlb().hashCode());
        result = prime * result + ((getByzd1() == null) ? 0 : getByzd1().hashCode());
        result = prime * result + ((getByzd2() == null) ? 0 : getByzd2().hashCode());
        result = prime * result + ((getByzd3() == null) ? 0 : getByzd3().hashCode());
        result = prime * result + ((getYwqqid() == null) ? 0 : getYwqqid().hashCode());
        result = prime * result + ((getYwqqclid() == null) ? 0 : getYwqqclid().hashCode());
        result = prime * result + ((getCpjc() == null) ? 0 : getCpjc().hashCode());
        result = prime * result + ((getCpdqr() == null) ? 0 : getCpdqr().hashCode());
        result = prime * result + ((getCplb() == null) ? 0 : getCplb().hashCode());
        result = prime * result + ((getGlrmc() == null) ? 0 : getGlrmc().hashCode());
        result = prime * result + ((getGlrzjlb() == null) ? 0 : getGlrzjlb().hashCode());
        result = prime * result + ((getGlrzjbh() == null) ? 0 : getGlrzjbh().hashCode());
        result = prime * result + ((getTgrmc() == null) ? 0 : getTgrmc().hashCode());
        result = prime * result + ((getTgrzjlb() == null) ? 0 : getTgrzjlb().hashCode());
        result = prime * result + ((getTgrzjbh() == null) ? 0 : getTgrzjbh().hashCode());
        result = prime * result + ((getClzt() == null) ? 0 : getClzt().hashCode());
        result = prime * result + ((getClcx() == null) ? 0 : getClcx().hashCode());
        result = prime * result + ((getByzd() == null) ? 0 : getByzd().hashCode());
        result = prime * result + ((getTdbh() == null) ? 0 : getTdbh().hashCode());
        result = prime * result + ((getBz() == null) ? 0 : getBz().hashCode());
        result = prime * result + ((getTazh() == null) ? 0 : getTazh().hashCode());
        result = prime * result + ((getTazhlb() == null) ? 0 : getTazhlb().hashCode());
        result = prime * result + ((getTazjlb() == null) ? 0 : getTazjlb().hashCode());
        result = prime * result + ((getZblb() == null) ? 0 : getZblb().hashCode());
        result = prime * result + ((getZqdm() == null) ? 0 : getZqdm().hashCode());
        result = prime * result + ((getGczqzh() == null) ? 0 : getGczqzh().hashCode());
        result = prime * result + ((getGctgdy() == null) ? 0 : getGctgdy().hashCode());
        result = prime * result + ((getGrzqzh() == null) ? 0 : getGrzqzh().hashCode());
        result = prime * result + ((getGrtgdy() == null) ? 0 : getGrtgdy().hashCode());
        result = prime * result + ((getByzd4() == null) ? 0 : getByzd4().hashCode());
        result = prime * result + ((getTzzmc() == null) ? 0 : getTzzmc().hashCode());
        result = prime * result + ((getZhaiqhm() == null) ? 0 : getZhaiqhm().hashCode());
        result = prime * result + ((getZhaiqkhmc() == null) ? 0 : getZhaiqkhmc().hashCode());
        result = prime * result + ((getZhaiqzjlb() == null) ? 0 : getZhaiqzjlb().hashCode());
        result = prime * result + ((getZhaiqzjdm() == null) ? 0 : getZhaiqzjdm().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sqbh=").append(sqbh);
        sb.append(", sqrq=").append(sqrq);
        sb.append(", sqsj=").append(sqsj);
        sb.append(", jys=").append(jys);
        sb.append(", ywlb=").append(ywlb);
        sb.append(", czlb=").append(czlb);
        sb.append(", cdbz=").append(cdbz);
        sb.append(", yyb=").append(yyb);
        sb.append(", sqgy=").append(sqgy);
        sb.append(", shgy=").append(shgy);
        sb.append(", shyj=").append(shyj);
        sb.append(", khjgdm=").append(khjgdm);
        sb.append(", khwddm=").append(khwddm);
        sb.append(", khh=").append(khh);
        sb.append(", clbz=").append(clbz);
        sb.append(", jgsm=").append(jgsm);
        sb.append(", ywpzbs=").append(ywpzbs);
        sb.append(", sbrq=").append(sbrq);
        sb.append(", sbsj=").append(sbsj);
        sb.append(", ywph=").append(ywph);
        sb.append(", wfId=").append(wfId);
        sb.append(", ymth=").append(ymth);
        sb.append(", khmc=").append(khmc);
        sb.append(", khlb=").append(khlb);
        sb.append(", gjdm=").append(gjdm);
        sb.append(", zjlb=").append(zjlb);
        sb.append(", zjbh=").append(zjbh);
        sb.append(", zjjzrq=").append(zjjzrq);
        sb.append(", zjdz=").append(zjdz);
        sb.append(", fzzjlb=").append(fzzjlb);
        sb.append(", fzzjbh=").append(fzzjbh);
        sb.append(", fzzjjzrq=").append(fzzjjzrq);
        sb.append(", fzzjdz=").append(fzzjdz);
        sb.append(", khfs=").append(khfs);
        sb.append(", csrq=").append(csrq);
        sb.append(", xb=").append(xb);
        sb.append(", xldm=").append(xldm);
        sb.append(", zyxz=").append(zyxz);
        sb.append(", mzdm=").append(mzdm);
        sb.append(", jglb=").append(jglb);
        sb.append(", zbsx=").append(zbsx);
        sb.append(", gysx=").append(gysx);
        sb.append(", jgjc=").append(jgjc);
        sb.append(", ywmc=").append(ywmc);
        sb.append(", gswz=").append(gswz);
        sb.append(", frxm=").append(frxm);
        sb.append(", frzjlb=").append(frzjlb);
        sb.append(", frzjbh=").append(frzjbh);
        sb.append(", lxrxm=").append(lxrxm);
        sb.append(", lxrzjlb=").append(lxrzjlb);
        sb.append(", lxrzjbh=").append(lxrzjbh);
        sb.append(", yddh=").append(yddh);
        sb.append(", gddh=").append(gddh);
        sb.append(", czhm=").append(czhm);
        sb.append(", lxdz=").append(lxdz);
        sb.append(", lxyb=").append(lxyb);
        sb.append(", dzyx=").append(dzyx);
        sb.append(", dxfwbs=").append(dxfwbs);
        sb.append(", wlfwbs=").append(wlfwbs);
        sb.append(", wlmm=").append(wlmm);
        sb.append(", zhlb=").append(zhlb);
        sb.append(", zqzh=").append(zqzh);
        sb.append(", xymth=").append(xymth);
        sb.append(", xzjlb=").append(xzjlb);
        sb.append(", xzjbh=").append(xzjbh);
        sb.append(", phzqzh=").append(phzqzh);
        sb.append(", jscyr=").append(jscyr);
        sb.append(", jydy=").append(jydy);
        sb.append(", yybbm=").append(yybbm);
        sb.append(", sdxlb=").append(sdxlb);
        sb.append(", qylb=").append(qylb);
        sb.append(", qyrq=").append(qyrq);
        sb.append(", hhcdfs=").append(hhcdfs);
        sb.append(", jcxzlb=").append(jcxzlb);
        sb.append(", byzd1=").append(byzd1);
        sb.append(", byzd2=").append(byzd2);
        sb.append(", byzd3=").append(byzd3);
        sb.append(", ywqqid=").append(ywqqid);
        sb.append(", ywqqclid=").append(ywqqclid);
        sb.append(", cpjc=").append(cpjc);
        sb.append(", cpdqr=").append(cpdqr);
        sb.append(", cplb=").append(cplb);
        sb.append(", glrmc=").append(glrmc);
        sb.append(", glrzjlb=").append(glrzjlb);
        sb.append(", glrzjbh=").append(glrzjbh);
        sb.append(", tgrmc=").append(tgrmc);
        sb.append(", tgrzjlb=").append(tgrzjlb);
        sb.append(", tgrzjbh=").append(tgrzjbh);
        sb.append(", clzt=").append(clzt);
        sb.append(", clcx=").append(clcx);
        sb.append(", byzd=").append(byzd);
        sb.append(", tdbh=").append(tdbh);
        sb.append(", bz=").append(bz);
        sb.append(", tazh=").append(tazh);
        sb.append(", tazhlb=").append(tazhlb);
        sb.append(", tazjlb=").append(tazjlb);
        sb.append(", zblb=").append(zblb);
        sb.append(", zqdm=").append(zqdm);
        sb.append(", gczqzh=").append(gczqzh);
        sb.append(", gctgdy=").append(gctgdy);
        sb.append(", grzqzh=").append(grzqzh);
        sb.append(", grtgdy=").append(grtgdy);
        sb.append(", byzd4=").append(byzd4);
        sb.append(", tzzmc=").append(tzzmc);
        sb.append(", zhaiqhm=").append(zhaiqhm);
        sb.append(", zhaiqkhmc=").append(zhaiqkhmc);
        sb.append(", zhaiqzjlb=").append(zhaiqzjlb);
        sb.append(", zhaiqzjdm=").append(zhaiqzjdm);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}