package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.YxsjReq;
import com.apex.sdx.api.req.query.ZmwjyxsjcxReq;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.query.ZmwjyxsjVo;
import com.apex.sdx.api.vo.query.YxsjVo;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.service.TzmwjYxsjService;
import com.apex.sdx.core.utils.MediaUtils;
import com.apexsoft.LiveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.query.IZmwjcxService;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-03-10
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "证明文件查询服务")
public class ZmwjcxService implements IZmwjcxService {

    @Autowired
    TzmwjYxsjService tzmwjYxsjService;

    @Override
    public QueryResponse<ZmwjyxsjVo> zmwjyxsjcx(ZmwjyxsjcxReq req) throws Exception {
        Assert.notNull(req, ZmwjyxsjcxReq::getKhh);

        QueryResponse<ZmwjyxsjVo> result = new QueryResponse<>(1, "查询成功");

        List<ZmwjyxsjVo> list = tzmwjYxsjService.queryByKhh(req.getKhh());

        result.setRecords(list);

        return result;
    }

    @Override
    public QueryResponse<YxsjVo> getBatchImages(YxsjReq req) throws Exception {
        QueryResponse<YxsjVo> result = new QueryResponse<>(1, "查询成功");
        List<String> tokenList = req.getToken();
        if (tokenList == null || tokenList.isEmpty()) {
            result.setCode(-1);
            result.setNote("token不能为空");
            return result;
        }

        ArrayList<YxsjVo> imageList = new ArrayList<>();

        for (String token : tokenList) {
            YxsjVo yxsj = new YxsjVo();
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                MediaUtils.readMediaFile(baos, token, false);

                // 转换为Base64编码
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                yxsj.setStatus(1);
                yxsj.setBase64Image(base64Image);
                yxsj.setContentType("image/jpeg");
            } catch (IOException e) {
                yxsj.setStatus(0);
            }

            imageList.add(yxsj);
        }

        result.setRecords(imageList);

        return result;
    }
}
