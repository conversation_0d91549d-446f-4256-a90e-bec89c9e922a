package com.apex.sdx.core.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/27
 */
public interface IMapping<T, V> {

    /**
     * pojo对象转换（dto转entity）
     *
     * @param pojo
     * @return
     */
    T map(V pojo);

    /**
     * pojo对象转换（entity转dto）
     *
     * @param pojo
     * @return
     */
    V mapTo(T pojo);

    /**
     * 分页数据转换（dto转entity）
     *
     * @param page
     * @return
     */
    Page<T> pageConver(Page<V> page);

    /**
     * 分页数据转换（entity转dto）
     *
     * @param page
     * @return
     */
    Page<V> pageConverTo(Page<T> page);

    /**
     * list数据转换（dto转entity）
     *
     * @param list
     * @return
     */
    List<T> listConver(List<V> list);

    /**
     * list数据转换（entity转dto）
     *
     * @param list
     * @return
     */
    List<V> listConverTo(List<T> list);


}
