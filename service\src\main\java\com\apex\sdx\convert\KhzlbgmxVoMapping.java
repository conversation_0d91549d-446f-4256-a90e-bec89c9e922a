package com.apex.sdx.convert;

import com.apex.sdx.api.vo.khgl.KhzlbgmxVo;
import com.apex.sdx.core.converter.IMapping;
import com.apex.sdx.core.converter.MapStructConfig;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhzlxg;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025-02-13
 * @Description:
 */
@Mapper(config = MapStructConfig.class)
@Component
public interface KhzlbgmxVoMapping extends IMapping<KhzlbgmxVo, TOdsYgtCifTkhzlxg> {
}
