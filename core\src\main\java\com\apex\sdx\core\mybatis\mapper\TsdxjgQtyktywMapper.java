package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.api.vo.compute.QtyktywtjVo;
import com.apex.sdx.core.mybatis.entity.TsdxjgQtyktyw;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Entity com.apex.sdx.core.mybatis.entity.TsdxjgQtyktyw
 */
public interface TsdxjgQtyktywMapper extends BaseMapper<TsdxjgQtyktyw> {

    List<QtyktywtjVo> compute(@Param("khh") String khh, @Param("sdxjg") String sdxjg, @Param("rq") String rq);

    Page<QtyktywtjVo> compute4cxywlb(Page<QtyktywtjVo> page, @Param("khh") String khh, @Param("cxywlb") String cxywlb, @Param("rq") String rq);

    Page<QtyktywtjVo> computemxBySdx(Page<QtyktywtjVo> page, @Param("khh") String khh, @Param("cxywlb") String cxywlb, @Param("qsbsdxy") String qsbsdxy, @Param("rq") String rq);


}




