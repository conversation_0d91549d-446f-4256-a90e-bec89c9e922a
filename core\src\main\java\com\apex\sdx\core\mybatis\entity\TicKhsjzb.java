package com.apex.sdx.core.mybatis.entity;

//import javax.validation.constraints.NotBlank;
//import javax.validation.constraints.Size;
//import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
//import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
//import org.hibernate.validator.constraints.Length;

/**
* 客户事件指标
* @TableName tic_khsjzb
*/
@TableName(value ="tic_khsjzb")
@Data
public class TicKhsjzb implements Serializable {

    /**
    * 日期
    */
    //@NotNull(message="[日期]不能为空")
    //@ApiModelProperty("日期")
    private Integer rq;
    /**
    * 客户号
    */
    //@NotBlank(message="[客户号]不能为空")
    //@Size(max= 20,message="编码长度不能超过20")
    //@Length(max= 20,message="编码长度不能超过20")
    private String khh;
    /**
    * 指标ID;TIC_ZBCS.ID
    */
    private Long idxId;
    /**
    * 指标代码
    */
    private String idxCode;
    /**
    * 结果
    */
    private BigDecimal result;
    /**
    * 周期
    */
    private Integer cycle;
    /**
    * 相关数据
    */
    private String xgsj;


    //rq khh idx_id,idx_code result cycle xgsj

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TicKhsjzb other = (TicKhsjzb) that;
        return (this.getRq() == null ? other.getRq() == null : this.getRq().equals(other.getRq()))
                && (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
                && (this.getIdxId() == null ? other.getIdxId() == null : this.getIdxId().equals(other.getIdxId()))
                && (this.getIdxCode() == null ? other.getIdxCode() == null : this.getIdxCode().equals(other.getIdxCode()))
                && (this.getResult() == null ? other.getResult() == null : this.getResult().equals(other.getResult()))
                && (this.getCycle() == null ? other.getCycle() == null : this.getCycle().equals(other.getCycle()))
                && (this.getXgsj() == null ? other.getXgsj() == null : this.getXgsj().equals(other.getXgsj()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getRq() == null) ? 0 : getRq().hashCode());
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getIdxId() == null) ? 0 : getIdxId().hashCode());
        result = prime * result + ((getIdxCode() == null) ? 0 : getIdxCode().hashCode());
        result = prime * result + ((getResult() == null) ? 0 : getResult().hashCode());
        result = prime * result + ((getCycle() == null) ? 0 : getCycle().hashCode());
        result = prime * result + ((getXgsj() == null) ? 0 : getXgsj().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", rq=").append(rq);
        sb.append(", khh=").append(khh);
        sb.append(", idxId=").append(idxId);
        sb.append(", idxCode=").append(idxCode);
        sb.append(", result=").append(result);
        sb.append(", cycle=").append(cycle);
        sb.append(", xgsj=").append(xgsj);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
