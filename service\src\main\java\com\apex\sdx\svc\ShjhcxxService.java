package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.SjhcxxcxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.vo.khgl.SjhcxxVo;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhSjhhcxx;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhSjhhcxxService;
import com.apexsoft.LiveService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.query.ISjhhcxxService;

/**
 * <AUTHOR>
 * @Date 2025-02-17
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "手机号核查信息查询服务")
public class ShjhcxxService implements ISjhhcxxService {

    @Autowired
    TOdsYgtCifTkhSjhhcxxService tkhSjhhcxxService;

    @Override
    public QueryPageResponse<SjhcxxVo> sjhcxxcx(SjhcxxcxReq req) throws Exception {
        Assert.notNull(req, SjhcxxcxReq::getKhh);
        QueryPageResponse<SjhcxxVo> result = new QueryPageResponse<>(1, "查询成功");

        Page<SjhcxxVo> page = tkhSjhhcxxService.queryByCoinditions(req.getKhh(), req.getKsrq(), req.getJsrq(), req.getHcjg(), req.isSearchCount(), req.getPagesize(), req.getPagenum());

        result.page(page);

        return result;
    }
}
