package sdx.query;

import com.apex.sdx.api.req.khgl.DcwjxqcxReq;
import com.apex.sdx.api.req.khgl.KhwjcplscxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.khgl.DcwjxqVo;
import com.apex.sdx.api.vo.khgl.KhwjcplsVo;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR> <PERSON>
 * @Date 2025-01-23
 * @Description:
 */
public interface IKhwjcpService {
    @LiveMethod(paramAsRequestBody = true, note = "客户问卷测评流水查询")
    QueryPageResponse<KhwjcplsVo> khwjcplscx(KhwjcplscxReq req) throws Exception;;

    @LiveMethod(paramAsRequestBody = true, note = "调查问卷详情查询")
    QueryResponse<DcwjxqVo> dcwjxqcx(DcwjxqcxReq req) throws Exception;;
}
