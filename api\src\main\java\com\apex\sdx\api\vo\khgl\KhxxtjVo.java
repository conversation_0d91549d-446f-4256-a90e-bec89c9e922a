package com.apex.sdx.api.vo.khgl;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> chenglingyu
 * @create 2025/5/8 13:50
 */
@Setter
@Getter
public class KhxxtjVo {

    @LiveProperty(note = "账户状态正常占比", index = 1)
    private Double zhztzczb;  

    @LiveProperty(note = "账户状态异常占比", index = 2)
    private Double zhztyczb;  

    @LiveProperty(note = "账户反洗钱风险等级正常占比", index = 3)
    private Double zhfxqfxdjzczb;  

    @LiveProperty(note = "账户反洗钱风险等级异常占比", index = 4)
    private Double zhfxqfxdjyczb;  

    @LiveProperty(note = "专业投资者测评正常占比", index = 5)
    private Double zytzzcpzczb;  

    @LiveProperty(note = "专业投资者测评异常占比", index = 6)
    private Double zytzzcpyczb;  

    @LiveProperty(note = "证件有效期正常占比", index = 7)
    private Double zjyxqzczb;  

    @LiveProperty(note = "证件有效期异常占比", index = 8)
    private Double zjyxqyczb;  

    @LiveProperty(note = "风险测评有效正常占比", index = 9)
    private Double fxcpyxzczb;  

    @LiveProperty(note = "风险测评有效异常占比", index = 10)
    private Double fxcpyxyczb;  

    @LiveProperty(note = "账户其他信息正常占比", index = 11)
    private Double zhqtxxzczb;  

    @LiveProperty(note = "账户其他信息异常占比", index = 12)
    private Double zhqtxxyczb;  

    @LiveProperty(note = "账户状态正常个数", index = 13)
    private Integer zhztzcgs;  

    @LiveProperty(note = "账户状态异常个数", index = 14)
    private Integer zhztycgs;  

    @LiveProperty(note = "账户反洗钱风险等级正常个数", index = 15)
    private Integer zhfxqfxdjzcgs;  

    @LiveProperty(note = "账户反洗钱风险等级异常个数", index = 16)
    private Integer zhfxqfxdjycgs;  

    @LiveProperty(note = "专业投资者测评正常个数", index = 17)
    private Integer zytzzcpzcgs;  

    @LiveProperty(note = "专业投资者测评异常个数", index = 18)
    private Integer zytzzcpycgs;  

    @LiveProperty(note = "证件有效期正常个数", index = 19)
    private Integer zjyxqzcgs;  

    @LiveProperty(note = "证件有效期异常个数", index = 20)
    private Integer zjyxqycgs;  

    @LiveProperty(note = "风险测评有效正常个数", index = 21)
    private Integer fxcpyxzcgs;  

    @LiveProperty(note = "风险测评有效异常个数", index = 22)
    private Integer fxcpyxycgs;  

    @LiveProperty(note = "账户其他信息正常个数", index = 23)
    private Integer zhqtxxzcgs;  

    @LiveProperty(note = "账户其他信息异常个数", index = 24)
    private Integer zhqtxxycgs;  
}
