<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTkhbsdxyqsCpMapper">

    <resultMap id="BaseResultMap" type="com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhbsdxyqsCp">
            <result property="khh" column="KHH" jdbcType="VARCHAR"/>
            <result property="type" column="TYPE" jdbcType="DECIMAL"/>
            <result property="cpdm" column="CPDM" jdbcType="VARCHAR"/>
            <result property="fxcsnl" column="FXCSNL" jdbcType="DECIMAL"/>
            <result property="khtzqx" column="KHTZQX" jdbcType="DECIMAL"/>
            <result property="khtzpz" column="KHTZPZ" jdbcType="VARCHAR"/>
            <result property="khyqsy" column="KHYQSY" jdbcType="VARCHAR"/>
            <result property="ywfxdj" column="YWFXDJ" jdbcType="DECIMAL"/>
            <result property="ywtzqx" column="YWTZQX" jdbcType="DECIMAL"/>
            <result property="ywtzpz" column="YWTZPZ" jdbcType="VARCHAR"/>
            <result property="ywyqsy" column="YWYQSY" jdbcType="VARCHAR"/>
            <result property="fxjsid" column="FXJSID" jdbcType="DECIMAL"/>
            <result property="xyqsid" column="XYQSID" jdbcType="DECIMAL"/>
            <result property="qsrq" column="QSRQ" jdbcType="DECIMAL"/>
            <result property="bsdlx" column="BSDLX" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        KHH,TYPE,CPDM,
        FXCSNL,KHTZQX,KHTZPZ,
        KHYQSY,YWFXDJ,YWTZQX,
        YWTZPZ,YWYQSY,FXJSID,
        XYQSID,QSRQ,BSDLX
    </sql>
</mapper>
