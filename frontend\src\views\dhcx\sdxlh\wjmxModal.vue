<template>
  <modal-component
    :title="title"
    :open="isOpen"
    :height="600"
    width="1050px"
    :on-cancel="handleOk"
  >
    <template #content>
      <div class="modal-container">
        <a-row style="background-color: #F5F9FC; padding: 20px">
          <a-col :span="6">
            <div>测评等级 :</div>
            <div style="font-weight: 400;font-size: 34px;color: #BF935F; line-height: 40px">{{ getDcNote("SDX_FXCSNL", dcwjData.cpdj, this.dictArray) }}</div>
          </a-col>
          <a-col :span="6">
            <div>测评得分 :</div>
            <div><span style="font-weight: 400;font-size: 34px;color: #BF935F; line-height: 40px">{{ dcwjData.cpdf }}</span>分</div>
          </a-col>
          <a-col :span="12">
            <div>客户姓名: <span style="color: #333333">{{ khjbxx?.khmc }}</span></div>
            <div>证件编号: <span style="color: #333333">{{ khjbxx?.zjbh }}</span></div>
            <div><a style="color: #B48A3B">打印风险测评</a> <a style="color: #B48A3B">业务开通适当性</a></div>
          </a-col>
        </a-row>
        <a-divider style="margin: 0 0 12px 0"/>
        <div style="padding: 0 20px">
          <div class="question-item" v-for="(item, index) in dcwjList" :key="index">
            <div class="question-title">
              <span style="font-weight: 400;font-size: 24px;color: #BF935F;padding-right: 5px">{{ index + 1 }} </span>
              <span style="font-weight: 400;font-size: 14px;color: #BF935F;padding-right: 10px">/{{ dcwjList.length }}</span>
              {{ item.qdescribe }}
            </div>
            <a-radio-group :value="item.da" style="padding: 0 10px" v-if="item.da.indexOf('-')<0">
              <a-radio class="radioStyle" :class="item.da == option.key ? 'selectedDa' : ''" :value="option.key" v-for="(option, index) in getOptions(item.sanswer)" :key="index">{{ option.value }}</a-radio>
            </a-radio-group>
            <a-checkbox-group :value="item.da.split('-')" v-else>
              <div v-for="(option, index) in getOptions(item.sanswer)" :key="index" style="width: 100%;margin-left: 12px;">
                  <a-checkbox class="radioStyle" :class="item.da.indexOf(option.key) != -1 ? 'selectedDa' : ''" :value="option.key">{{ option.value }}</a-checkbox>
              </div>
            </a-checkbox-group>
          </div>
        </div>
      </div>
    </template>
  </modal-component>
</template>
<script>
import {defineComponent} from "vue";
import ModalComponent from "@components/ModalComponent.vue";
import {commonApi} from "@/api/common";
import {getDcNote} from "../../../utils/bussinessUtils";

export default defineComponent({
  name: "wjmxModal",
  components: { ModalComponent },
  props: {
    open: {
      type: Boolean,
      default: false
    },

    dcwjData: {
      type: Object,
    },
    khh: {
      type: String,
      required: true
    },
    dictArray: {
      required: true
    },
    khjbxx: {
      type: Object,
    }
  },
  data() {
    return {
      isOpen: this.open,
      cljgOptions: [{label: '全部', value: 1}],
      cljg: 1,
      value: 1,
      dcwjList: null,
      title: ''
    }
  },
  watch: {
    open(newVal) {
      this.isOpen = newVal;
      if (this.open) {
        this.getDcwjxq();
      }
    }
  },
  methods: {
    getDcNote,
    handleOk(){
      this.isOpen = false;
      this.$emit("update:open", false);
    },
    getDcwjxq() {
      commonApi.executeAMS(
        "sdx.query.IKhwjcpService",
        "dcwjxqcx",
        {
          khdcwjid: this.dcwjData.wjid,
          khh: this.khh,
          sdxlb: this.dcwjData.sdxlb
        }
      ).then((res) => {
        this.title = res?.records[0]?.name
        this.dcwjList = res?.records;
      })
    },
    getOptions(str) {
      return str
        .split(';')
        .map(group => {
          const [key, ...values] = group.split('|');
          return {
            key: key?.trim() || '',
            value: values.join('|')?.trim() || ''
          };
        })
        .filter(item => item.key && item.value);
    },
  }
});
</script>
<style scoped>
.modal-container {
  background-color: #FFFFFF;;
  height: 100%;
  border-radius: 4px;
  line-height: 30px;
  color: #888888;
  overflow-y: auto;
}

.question-item {
  padding: 5px 10px;
  line-height: 30px;
}

.radioStyle {
  display: flex;
  height: 30px;
  line-height: 30px;
  font-weight: 400;
  font-size: 14px;
  color: #AAAAAA;
}

.question-title {
  font-weight: 400;
  font-size: 14px;
  color: #333333;
}
:deep(.ant-radio-wrapper:hover .ant-radio-inner ){
  border-color: #BF935F;
}
:deep(.ant-radio-wrapper .ant-radio-checked .ant-radio-inner) {
  border-color: #BF935F;
  background-color: #BF935F;
}
:deep(.ant-checkbox-checked .ant-checkbox-inner ){
  border-color: #BF935F;
  background-color: #BF935F;
}
:deep(.ant-checkbox-checked:not(.ant-checkbox-disabled):hover .ant-checkbox-inner){
  background-color: #BF935F;
}
:deep(.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-checked:not(.ant-checkbox-disabled):after){
  border-color: #BF935F;
}
:deep(.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-checked:not(.ant-checkbox-disabled) .ant-checkbox-inner){
  background-color: #BF935F;
}
:deep(.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-inner){
  border-color: #BF935F;
}
:deep(.ant-checkbox .ant-checkbox-input:focus-visible+.ant-checkbox-inner ){
  outline: 2px solid #bf935f8f;
}
:deep(.ant-checkbox-checked:after){
  border: 2px solid #bf935f8f;
}
.selectedDa {
  font-weight: 400;
  font-size: 14px;
  color: #333333;
}
</style>