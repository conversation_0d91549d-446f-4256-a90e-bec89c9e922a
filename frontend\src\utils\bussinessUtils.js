import CryptoJS from 'crypto-js'
/**
 * 根据 fldm 和 ibm 从字典中获取映射
 * @param fldm
 * @param ibm
 * @param sjzd
 * @returns {string}
 */
export const getDcNote = (fldm, ibm, sjzd) => {
  if (ibm === null || ibm === undefined) {
    return "";
  }
  let noteStr = ibm.toString(); // Default note string if no dictionary value is found
  if (!(typeof ibm === "number") && !(typeof ibm === "string")) {
    return "";
  }

  let ibmStr = null;
  if (typeof ibm === "number") {
    ibmStr = ibm.toString();
  } else {
    ibmStr = ibm;
  }

  if (!ibmStr || ibmStr.trim() === "") {
    return noteStr;
  }
  sjzd = sjzd||[]
  const sjzdList = sjzd[fldm];

  if (sjzdList != null && sjzdList.length > 0) {
    const ibms = ibmStr.split(";");
    if (ibms.length > 1) {
      let note = "";
      for (const str of ibms) {
        const sjzd = sjzdList.find(vsjzd => vsjzd.ibm === str) || null;
        if (sjzd !== null) {
          note += sjzd.note + "|";
        } else {
          note += str + "|";
        }
      }
      noteStr = note.slice(0, -1);  // Remove the last "|"
    } else {
      const sjzd = sjzdList.find(vsjzd => vsjzd.ibm === ibms[0]) || null;
      if (sjzd !== null) {
        noteStr = sjzd.note;
      }
    }
  }
  return noteStr;
};

/**
 * 日期转化YYYYMMDD=>YYYY-MM-DD
 * @param dateStr
 * @returns {*|null}
 */
export const formatDate=(val)=> {
  let dateStr = val? val.toString():"";
  return dateStr ? dateStr.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3") : null;
};
// 十六位十六进制数作为密钥
const SECRET_KEY = CryptoJS.enc.Utf8.parse("4A1B3F8E2D9C7F0A");
// 十六位十六进制数作为密钥偏移量
const SECRET_IV = CryptoJS.enc.Utf8.parse("7E5D9A3BC4F8162E");
/**
 * 加密方法
 * @param data
 * @returns {string}
 */
export function encrypt(data) {
  if (typeof data === "object") {
    try {
      // eslint-disable-next-line no-param-reassign
      data = JSON.stringify(data);
    } catch (error) {
      console.log("encrypt error:", error);
    }
  }
  const dataHex = CryptoJS.enc.Utf8.parse(data);
  const encrypted = CryptoJS.AES.encrypt(dataHex, SECRET_KEY, {
    iv: SECRET_IV,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });
  return encrypted.ciphertext.toString();
}

/**
 * 解密方法
 * @param data
 * @returns {string}
 */
export function decrypt(data) {
  if(!data) {
    return data;
  }
  const encryptedHexStr = CryptoJS.enc.Hex.parse(data);
  const str = CryptoJS.enc.Base64.stringify(encryptedHexStr);
  const decrypt = CryptoJS.AES.decrypt(str, SECRET_KEY, {
    iv: SECRET_IV,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });
  const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
  return decryptedStr.toString();
}