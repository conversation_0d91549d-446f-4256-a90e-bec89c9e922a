package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.api.vo.khgl.KhwjcplsVo;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhsdx;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @Entity com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhsdx
 */
public interface TOdsYgtCifTkhsdxMapper extends BaseMapper<TOdsYgtCifTkhsdx> {

    @Select({"<script> "
            +"SELECT a.sdxfl,  "
            + "    a.sdxlb,"
            /*+ "	if(a.sdxfl = 2, (SELECT jgjc FROM tjr_jgcs WHERE jgdm = a.sdxlb), f.note) AS sdxlbmc,  "
            + "    (SELECT djsm FROM tPJ_CPDJ WHERE id = a.cpdj AND sdxfl = a.sdxfl) AS djsm,  "*/
            + "    a.cpdj,"
            + "    a.cpdf,"
            + "    a.cprq,  "
            + "    a.cpyxq  "
            + " FROM ods.t_ods_ygt_cif_tkhsdx a "
            + " LEFT JOIN ods.t_ods_ygt_cif_TXTDM f "
            + "    ON f.FLDM = 'GT_SDXLB' "
            + "        AND f.CBM = a.SDXLB "
            + " WHERE a.khh = #{khh} "
            + "        AND a.sdxlb &lt;&gt; 'GPFXCSNL' "
            + "   <if test='ksrq != null'>"
            + "       and a.cprq &gt;= #{ksrq}"
            + "   </if>"
            + "   <if test='jsrq != null'>"
            + "       and a.cprq &lt;= #{jsrq}"
            + "   </if>"
            + " ORDER BY  a.sdxfl, a.sdxlb "
            + "</script>"})
    Page<KhwjcplsVo> selectWjsdx(Page<KhwjcplsVo> page, @Param("khh") String khh, @Param("ksrq") Integer ksrq, @Param("jsrq") Integer jsrq);
}




