package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.api.vo.khgl.SjhcxxVo;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhSjhhcxx;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *
 */
public interface TOdsYgtCifTkhSjhhcxxService extends IService<TOdsYgtCifTkhSjhhcxx> {

    /**
     *
     * @param khh
     * @param ksrq
     * @param jsrq
     * @param hcjg
     * @param isSearchCount
     * @param pagesize
     * @param pagenum
     * @return
     */
    Page<SjhcxxVo> queryByCoinditions(String khh, Integer ksrq, Integer jsrq, Integer hcjg, boolean isSearchCount, int pagesize, int pagenum);
}
