package com.apex.sdx.api.req.compute;

import com.apex.sdx.api.req.common.PageRequest;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> wu<PERSON><PERSON><PERSON>
 * @create 2025/4/30 16:28
 */
@Setter
@Getter
public class SdxsjtjReq extends PageRequest {

    @LiveProperty(note = "统计维度,1|所有事件;2|每个事件",index = 1)
    @NotNull
    private Integer tjwd = 1;

    @LiveProperty(note = "起始日期", index = 2)
    private String ksrq;

    @LiveProperty(note = "结束日期", index = 3)
    private String jsrq;

    @LiveProperty(note = "客户号",index = 4)
    private String khh;

    @LiveProperty(note = "事件编码",index = 5)
    private String sjbm;
}
