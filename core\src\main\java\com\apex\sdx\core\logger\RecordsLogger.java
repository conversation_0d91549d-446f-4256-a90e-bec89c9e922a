package com.apex.sdx.core.logger;

import com.alibaba.fastjson.JSONObject;
import com.apex.sdx.core.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/7/7
 */
@Slf4j
public class RecordsLogger {

    public static void log(String func, long invokeDate, Object outParam, Object inParam) {
        JSONObject json = new JSONObject();
        json.put("func", func);
        json.put("invokeDate", DateUtil.formatMillise(invokeDate, "yyyyMMdd"));
        json.put("invokeTime", DateUtil.formatMillise(invokeDate, "HH:mm:ss"));
        json.put("responseTime", System.currentTimeMillis() - invokeDate + "ms");
        json.put("inParam", JSONObject.toJSONString(inParam));
        json.put("outParam", JSONObject.toJSONString(outParam));
        log.debug(json.toJSONString());
    }
}
