@charset "utf-8";
/* CSS Document */
*{ margin:0px; padding:0px;font-family:"微软雅黑";}
a,em,input{font-family:"微软雅黑";}
body{ width:100%; height:100%;}
li{ list-style:none;}
a{ text-decoration:none;}
.clear:after{ content:""; height:0; display:table; clear:both;}
.hide{ display:none;}
.pd4{ padding:4px 0px;}
img{ border:none;}
.ml15{ margin-left:15px !important;}
.mr15{ margin-right:15px !important;}
.mr5{ margin-right:5px !important;}
.mr1{ margin-right:1px !important;}
.mt12{ margin-top:12px;}
.mt5{ margin-top:5px;}
.mt8{ margin-top:8px;}
.l{ float:left;}
.r{ float:right;}
.fs14{ font-size:14px !important;}
.fs15{ font-size:15px !important;}
.fs18{ font-size:18px !important;}
.fs24{ font-size:24px !important;}
.grey{ color:#b7b7b7;}
.red{ color:#ff663a;}
.warn_col{ color:#e80202;}
.fb{ font-weight:bold;}
.tl{ text-align:left;}
.tc{ text-align:center;}
.tr{ text-align:right;}
a:active, a:hover, a:focus, li , button, input, select, textarea {outline: 0;}
.clear:after{ content:""; height:0; display:table; clear:both;}
*{box-sizing:border-box;}
select {
    /*很关键：将默认的select选择框样式清除*/
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    /*在选择框的最右侧中间显示小箭头图片*/
    background-image: url("../images/jtsx_select.png");
    background-repeat:no-repeat;
    background-position: right 5px center;
    cursor: pointer;
}
select::-ms-expand{display: none;}/*清除IE默认下拉按钮，但是测试发现IE10以上有效，IE8，9默认下拉按钮仍旧存在*/
/*适当性配置*/
.sdxtc_nav{padding:5px 10px; background-color: #fff;}
.sdx_bus_con{}
.sdx_bus_con table{ table-layout: fixed;}
.sdx_bus_con table td{padding: 5px;}
.sdx_bus_con table td{ font-size: 14px; color: #333;}
.sdx_bus_con table td input[type=text]{ color: #333; width: 160px; border-radius: 2px; border: 1px solid #aaa; padding: 4px;background-color: #fff;}
.sdx_bus_con table td select{ color: #333; width: 160px; border-radius: 2px; border: 1px solid #aaa; padding: 4px;background-color: #fff;}
.sdx_bus_con table td input[type=text][disabled],.sdx_bus_con table td select[disabled]{ background-color: #eee; color: #999;}
.sdx_dep{ margin: 10px 0px; border: 1px solid #ddd; min-height: 420px;}
.sdx_dep_left{ background-color: #f9f9f9; height: calc(100%); margin-right: 400px; position: relative;}
.sdx_dep_right{ float: right; background-color: #fff; height: calc(100%); width: 400px; border-left: 1px solid #ddd;}
.sdx_dep_btn{ padding: 5px 10px; width: 100%; height: 40px; background-color: #f9f9f9;top: 0; left: 0;}
.sdx_btn{ display: inline-block; padding: 0px 10px; border-radius: 4px; margin-right: 10px; min-width: 76px; background-color: #048dc6; border: 1px solid #048dc6; cursor: pointer; text-align: center; line-height: 30px; font-size: 12px; color: #fff;}
.sdx_btn.dis{color: #999; border:1px solid #aaa; background-color: #f6f6f6; cursor: default;}
.sdx_dep_right ul{ margin: 10px;}
.sdx_dep_right ul li{ font-size: 12px; margin-bottom: 5px;}
.sdx_dep_right ul li p{ color: #333; line-height: 24px;}
/*.sdx_dep_right ul li input[type=text]{width: 180px}*/
/*.sdx_dep_right ul li input[type=text]{color: #333; width: 180px; border-radius: 2px; border: 1px solid #aaa; padding: 4px;background-color: #fff;}*/
.sdx_dep_right ul li select{ color: #333; width: 180px; border-radius: 2px; border: 1px solid #aaa; padding: 4px;background-color: #fff;}
.sdx_btn.fz{ background-color: #fff; border: 1px solid #aaa; color: #666;}
.tc_btn{ text-align: center;}
.tc_btn .sdx_btn{ font-size: 14px; margin-left: 10px;}
.sdx_dep_content{height: 100%}


.zxtj_tc{min-height: 380px; overflow-y: auto; padding: 5px 10px;}
.zxtj_tc table{}
.zxtj_left_bor{width:1px;display: table-cell; vertical-align: middle; border-right: 1px dashed #0cb3e8; }
.zxtj_tc table a.tj_gx{display:inline-block;position: relative;z-index: 9999;right: -10px;background-color: #0cb3e8;border-radius:10px;color: #fff;width: 20px;height:20px;text-align: center;line-height: 20px;font-size: 12px;}
.zxtj_tc table a.tj_gx.big{ right: -13px; width: 26px;height: 26px; line-height: 26px; font-size: 14px; border-radius:13px;}
.zxtj_tc table td em.line{ display: inline-block; width: 20px; height: 1px; border-top: 1px dashed #0cb3e8;}
.zxtj_tc table td .second_tj_con_li ul li{min-height: 40px;}
.zxtj_tc table td .second_tj_con_li em.line{width: 10px;margin-top: 19px;float: left;}
.zxtj_tc table td .second_tj_con_li ul li .compute_mode{ float: left;margin-top: 12px;}

.zxtj_left_bor{width:1px;display: table-cell; vertical-align: middle; border-right: 1px dashed #0cb3e8;}

.tj_dl_model{border: 1px dotted #aaa;margin-bottom: 8px;padding: 10px 5px 10px 5px;border-radius: 2px;}

.add_tj{padding-left: 20px;display: inline-block;float: right;margin-top: -18px;background-repeat: no-repeat;background-position: left center;font-size: 12px;color: #0a9dda;background-image: url(../images/zj_tj_tb_pic.png);margin-right: 2px;}

.second_tj_gx{float: left;padding-top: 20px;}
.second_tj_gx .tj_box{width: 1px;display: table-cell;vertical-align: middle;border-right: 1px #0a9dda dashed;}

.second_tj_con_li{margin-left: 21px;}
.second_tj_con_li select{float: left;width: 120px;border:1px solid #ddd;border-radius: 4px;line-height: inherit;padding:5px 4px;color: #333;font-size: 12px;margin-top: 5px;}
.second_tj_con_li .tj_con{margin: 0px 40px 0px 156px;line-height: 30px;padding-left: 4px;}
.condition_td:hover{ background-color:#eff7fb;}
.condition_td:hover .delete_tj{ display:inline-block;}
.condition_td .delete_tj{cursor: pointer;float: right;display: none;width: 20px;height: 30px;background-repeat: no-repeat;background-position: center center;background-image: url(../images/khblyw_tab_close_2.png);}
.condition_td .delete_tj:hover{ background-image: url(../images/khblyw_tab_close_2_hov.png);}

.compute_mode{ position:relative; width:16px; height:16px; display:inline-block; vertical-align:middle; background:url(../images/count_pic.png) center center no-repeat; margin-left:5px; cursor:pointer;}

.tj_con label{font-size: 12px;color: #333;white-space: nowrap;margin-right: 20px;display: inline-block;vertical-align: middle;margin-bottom: 5px;margin-top: 5px;}
.tj_con label input[type=checkbox],.tj_con label input[type=radio]{ display: inline-block; vertical-align: middle; margin-top: -3px;}
.tj_con label input[type=text]{ border: 1px solid #ccc; font-size: 12px; border-radius: 4px; width: 100px; padding:6px 4px; height: 30px;}
.tj_con label span{ display: inline-block; margin-right: 4px;}

.jd_con{
    font-size: 12px;
    border: 2px solid #aaa;
    margin: 0px 10px;
    padding: 8px 5px;
    border-radius: 3px;
    width: 120px;
    cursor: pointer;
    text-align: center;
}

.xnjd_con{
    border: 2px dashed #aaa;
    font-size: 12px;
    margin: 0px 10px;
    padding: 8px 5px;
    border-radius: 3px;
    width: 120px;
    cursor: pointer;
    text-align: center;
}
.hide {
    display: none !important;
}

.third_tj_con_li {
    margin-left: 10px;
    border: 2px solid #aaa;
    width: 144px;
    padding: 10px 0 8px 0;
    margin-bottom: 8px;
}
.cur{
    background: #0cb3e8;
    color: #fff;
    border: 2px solid #0cb3e8;
}

.xnline{
    width: 10px;
    height: 92px;
    float: left;
    margin: 20px 0;
    border-top: 1px dashed #0cb3e8;
    border-bottom: 1px dashed #0cb3e8;
    border-left: 1px dashed #0cb3e8;
}

td{
    vertical-align: middle;
}