package com.apex.sdx.api.vo.khgl;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-02-14
 * @Description:
 */
@Setter
@Getter
public class JgkhxxhcsqVo {


    @LiveProperty(note = "申请编号", index = 1)
    private Long sqbh;

    @LiveProperty(note = "处理标志", index = 2)
    private Long clbz;

    @LiveProperty(note = "结果说明", index = 3)
    private String jgsm;

    @LiveProperty(note = "客户号", index = 4)
    private String khh;

    @LiveProperty(note = "客户名称", index = 5)
    private String khmc;

    @LiveProperty(note = "辅助证件编号", index = 6)
    private String fzzjbh;

    @LiveProperty(note = "申请日期", index = 7)
    private Integer sqrq;

    @LiveProperty(note = "申请时间", index = 8)
    private String sqsj;

    @LiveProperty(note = "营业部", index = 9)
    private Long yyb;

    @LiveProperty(note = "申请柜员", index = 10)
    private Long sqgy;

    @LiveProperty(note = "开户机构代码", index = 11)
    private String khjgdm;

    @LiveProperty(note = "开户网点代码", index = 12)
    private String khwddm;
}
