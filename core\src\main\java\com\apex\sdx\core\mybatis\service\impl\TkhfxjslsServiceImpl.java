package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.resp.common.Response;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTgmsfcxsq;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.Tkhfxjsls;
import com.apex.sdx.core.mybatis.service.TkhfxjslsService;
import com.apex.sdx.core.mybatis.mapper.TkhfxjslsMapper;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class TkhfxjslsServiceImpl extends ServiceImpl<TkhfxjslsMapper, Tkhfxjsls>
    implements TkhfxjslsService{

    @Override
    public Page<Tkhfxjsls> queryByCondition(String khh, Integer ksrq, Integer jsrq, String jsnr, boolean searchCount, int pagenum, int pagesize) {
        Page<Tkhfxjsls> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(searchCount);
        try {
            LambdaQueryWrapper<Tkhfxjsls> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Tkhfxjsls::getKhh, khh);
            wrapper.ge(ksrq != null, Tkhfxjsls::getCzrq, ksrq);
            wrapper.le(jsrq != null, Tkhfxjsls::getCzrq, jsrq);
            wrapper.like(jsnr != null, Tkhfxjsls::getJsnr, jsnr);
            wrapper.orderByDesc(Tkhfxjsls::getId);
            this.page(page, wrapper);
            return page;

        } catch (Exception e) {
            String note = String.format("客户风险警示流水查询失败[%s]", e.getMessage());
            log.error(note, e);
            throw new BusinessException(Response.buildErrorMsg(-601, note));
        }
    }
}




