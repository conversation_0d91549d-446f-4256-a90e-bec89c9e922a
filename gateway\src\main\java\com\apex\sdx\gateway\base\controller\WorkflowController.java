package com.apex.sdx.gateway.base.controller;


import com.alibaba.fastjson.JSONObject;
import com.apex.sdx.gateway.aas.modules.index.service.DefaultServiceHandler;
import com.apex.sdx.gateway.base.model.JSONResponse;
import com.apex.sdx.gateway.base.service.WorkflowService;
import com.apex.sdx.gateway.aas.modules.index.model.AuthUser;
import com.apexsoft.live.session.UserAuthenticateSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


@RequestMapping("/workflow")
@Controller
@Slf4j
public class WorkflowController {
    @Autowired
    WorkflowService workflowService;

    private DefaultServiceHandler defaultServiceHandler = new DefaultServiceHandler();


    @RequestMapping(value = "/queryWorkflowItems",name = "获取用户权限内的流程信息")
    @ResponseBody
    public JSONResponse queryUserProject(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
        JSONResponse response = workflowService.queryWorkflowItems(user.getId()+"", true);
        return response;
    }

    @RequestMapping(value = "/createStartWorkflowHyperLink",name = "创建流程启动链接")
    @ResponseBody
    public JSONResponse createStartWorkflowHyperLink(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
        JSONResponse response = workflowService.createStartWorkflowHyperLink(user.getUserId(),
                jsonData.getString("workflowname"),null,0,0);
        return response;
    }

    @RequestMapping(value = "/createTransactWorkflowHyperLink",name = "创建流程办理链接")
    @ResponseBody
    public JSONResponse createTransactWorkflowHyperLink(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
        JSONResponse response = workflowService.createTransactWorkflowHyperLink(user.getUserId(),
                jsonData.getIntValue("instid"),jsonData.getIntValue("stepid"),null);
        return response;
    }


    @RequestMapping(value = "/queryWorkTasks",name = "创建流程启动链接")
    @ResponseBody
    public JSONResponse queryWorkTasks(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception{
        AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
        JSONResponse response = workflowService.queryWorkTasks(user.getId()+"");
        return response;
    }
}
