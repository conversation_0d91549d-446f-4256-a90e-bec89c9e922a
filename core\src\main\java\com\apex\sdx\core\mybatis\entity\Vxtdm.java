package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName vxtdm
 */
@TableName(value ="vxtdm", schema = "sdx")
@Data
public class Vxtdm implements Serializable {
    /**
     * 
     */
    private String fldm;

    /**
     * 
     */
    private String flmc;

    /**
     * 
     */
    private String ibm;

    /**
     * 
     */
    private String cbm;

    /**
     * 
     */
    private String note;

    /**
     * 
     */
    private Long flag;

    /**
     * 
     */
    private Long type;

    /**
     * 
     */
    private String category;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Vxtdm other = (Vxtdm) that;
        return (this.getFldm() == null ? other.getFldm() == null : this.getFldm().equals(other.getFldm()))
            && (this.getFlmc() == null ? other.getFlmc() == null : this.getFlmc().equals(other.getFlmc()))
            && (this.getIbm() == null ? other.getIbm() == null : this.getIbm().equals(other.getIbm()))
            && (this.getCbm() == null ? other.getCbm() == null : this.getCbm().equals(other.getCbm()))
            && (this.getNote() == null ? other.getNote() == null : this.getNote().equals(other.getNote()))
            && (this.getFlag() == null ? other.getFlag() == null : this.getFlag().equals(other.getFlag()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getCategory() == null ? other.getCategory() == null : this.getCategory().equals(other.getCategory()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getFldm() == null) ? 0 : getFldm().hashCode());
        result = prime * result + ((getFlmc() == null) ? 0 : getFlmc().hashCode());
        result = prime * result + ((getIbm() == null) ? 0 : getIbm().hashCode());
        result = prime * result + ((getCbm() == null) ? 0 : getCbm().hashCode());
        result = prime * result + ((getNote() == null) ? 0 : getNote().hashCode());
        result = prime * result + ((getFlag() == null) ? 0 : getFlag().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getCategory() == null) ? 0 : getCategory().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fldm=").append(fldm);
        sb.append(", flmc=").append(flmc);
        sb.append(", ibm=").append(ibm);
        sb.append(", cbm=").append(cbm);
        sb.append(", note=").append(note);
        sb.append(", flag=").append(flag);
        sb.append(", type=").append(type);
        sb.append(", category=").append(category);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}