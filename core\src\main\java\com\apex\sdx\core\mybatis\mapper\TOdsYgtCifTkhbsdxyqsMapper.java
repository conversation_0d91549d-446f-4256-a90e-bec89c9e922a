package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.api.vo.query.QxbsdxyqslscxVo;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhbsdxyqs;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_ods_ygt_cif_tkhbsdxyqs】的数据库操作Mapper
 * @createDate 2025-06-06 16:59:26
 * @Entity com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhbsdxyqs
 */
public interface TOdsYgtCifTkhbsdxyqsMapper extends BaseMapper<TOdsYgtCifTkhbsdxyqs> {

    @Select({"<script> " +
            "select * from ods.t_ods_ygt_cif_tkhbsdxyqs " +
            "<where>" +
            " <if test='ksrq != null and ksrq != \"\"'> " +
            " and qsrq &gt;= #{ksrq} " +
            " </if> " +
            " <if test='jsrq != null and jsrq != \"\"'> " +
            " and qsrq &lt;= #{jsrq} " +
            " </if> " +
            " <if test='cxywlb != null and cxywlb != \"\"'> " +
            " and cxywlb = #{cxywlb} " +
            " </if> " +
            " <if test='khh != null and khh != \"\"'> " +
            " and khh = #{khh} " +
            " </if> " +
            "</where> " +
            "</script>"})
    List<QxbsdxyqslscxVo> qxbsdxyqslscx(@Param("khh") String khh,@Param("ksrq") String ksrq, @Param("jsrq") String jsrq, @Param("cxywlb") String cxywlb);

}




