package sdx.query;

import com.apex.sdx.api.req.query.YxsjReq;
import com.apex.sdx.api.req.query.ZmwjyxsjcxReq;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.query.ZmwjyxsjVo;
import com.apex.sdx.api.vo.query.YxsjVo;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR>
 * @Date 2025-03-10
 * @Description:
 */
public interface IZmwjcxService {
    @LiveMethod(paramAsRequestBody = true, note = "证明文件影像数据查询")
    QueryResponse<ZmwjyxsjVo> zmwjyxsjcx(ZmwjyxsjcxReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "影像数据查询")
    QueryResponse<YxsjVo> getBatchImages(YxsjReq req) throws Exception;
}
