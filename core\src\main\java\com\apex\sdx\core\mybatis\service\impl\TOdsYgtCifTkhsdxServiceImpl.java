package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.resp.common.Response;
import com.apex.sdx.api.vo.khgl.KhwjcplsVo;
import com.apex.sdx.core.exception.BusinessException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhsdx;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhsdxService;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTkhsdxMapper;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class TOdsYgtCifTkhsdxServiceImpl extends ServiceImpl<TOdsYgtCifTkhsdxMapper, TOdsYgtCifTkhsdx>
    implements TOdsYgtCifTkhsdxService{

    @Override
    public Page<KhwjcplsVo> getWjsdxByKhh(String khh, Integer ksrq, Integer jsrq, boolean isSearchCount, int pagesize, int pagenum) {
        Page<KhwjcplsVo> page = new Page<>(pagenum, pagesize, isSearchCount);
        try {
            return this.baseMapper.selectWjsdx(page, khh, ksrq, jsrq);
        }catch (Exception e){
            String note = String.format("获取问卷适当性失败[%s]", e.getMessage());
            log.error(note, e);
            throw new BusinessException(Response.buildErrorMsg(-601, note));
        }
    }
}




