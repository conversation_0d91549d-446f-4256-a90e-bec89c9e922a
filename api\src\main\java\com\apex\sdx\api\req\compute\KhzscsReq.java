package com.apex.sdx.api.req.compute;

import com.apex.sdx.api.req.common.Request;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025/5/9 13:55
 * @Description: TODO
 */
@Setter
@Getter
public class KhzscsReq extends Request {

    @LiveProperty(note = "查询类型", index = 1)
    private String type;

    @LiveProperty(note = "查询日期", index = 2)
    private String rq;
}
