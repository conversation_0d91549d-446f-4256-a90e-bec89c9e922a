package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.core.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.Tkhjyqx;
import com.apex.sdx.core.mybatis.service.TkhjyqxService;
import com.apex.sdx.core.mybatis.mapper.TkhjyqxMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class TkhjyqxServiceImpl extends ServiceImpl<TkhjyqxMapper, Tkhjyqx>
    implements TkhjyqxService{

    @Override
    public Page<Tkhjyqx> queryKhjyqx(String khh, String ywzh, String gdh, Integer jyqx, Integer zt, boolean isSearchCount, int pagesize, int pagenum) {
        Page<Tkhjyqx> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);
        QueryWrapper<Tkhjyqx> wrapper = new QueryWrapper<>();
        try {
            wrapper.eq("khh", khh);
            if (StringUtils.isNotBlank(ywzh)) {
                wrapper.eq("ywzh", ywzh);
            }
            if (StringUtils.isNotBlank(gdh)) {
                wrapper.eq("gdh", gdh);
            }
            if (jyqx != null) {
                wrapper.eq("jyqx", jyqx);
            }
            if (zt != null) {
                wrapper.eq("zt", zt);
            }
            return this.page(page, wrapper);
        } catch (Exception e) {
            String note = String.format("查询客户交易权限列表异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }
}




