package sdx.query;

import com.apex.sdx.api.req.query.KhsjzbReq;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.query.KhsjzbVo;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR>
 * @Date 2025-04-21
 * @Description: 查询tic_khsjzb
 */
public interface IKhsjzbService {
    @LiveMethod(paramAsRequestBody = true, note = "查询客户数据指标")
    QueryResponse<KhsjzbVo> getKhsjzbByCode(KhsjzbReq req) throws Exception;
}
