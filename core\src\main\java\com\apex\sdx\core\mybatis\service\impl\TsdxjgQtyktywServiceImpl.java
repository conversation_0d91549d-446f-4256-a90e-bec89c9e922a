package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.resp.common.Response;
import com.apex.sdx.api.vo.compute.QtyktywtjVo;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.mybatis.entity.TsdxjgQtyktyw;
import com.apex.sdx.core.mybatis.mapper.TsdxjgQtyktywMapper;
import com.apex.sdx.core.mybatis.service.TsdxjgQtyktywService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 *
 */
@Service
public class TsdxjgQtyktywServiceImpl extends ServiceImpl<TsdxjgQtyktywMapper, TsdxjgQtyktyw>
    implements TsdxjgQtyktywService{

    @Override
    public Page<TsdxjgQtyktyw> queryPageByKhhAndSdxjg(String khh, boolean onlysdx, String rq, boolean isSearchCount, int pagesize, int pagenum) {
        Page<TsdxjgQtyktyw> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);

        try {
            LambdaQueryWrapper<TsdxjgQtyktyw> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TsdxjgQtyktyw::getKhh, khh);
            wrapper.and(onlysdx, w -> w.eq(TsdxjgQtyktyw::getSdxjg, -1).or().eq(TsdxjgQtyktyw::getSdxjg, 0));
            if (StringUtils.hasText(rq)) {
                wrapper.eq(TsdxjgQtyktyw::getRq, rq);
            }
            wrapper.orderByDesc(TsdxjgQtyktyw::getRq);
            this.page(page, wrapper);
            return page;

        } catch (Exception e) {
            String note = String.format("查询已开通业务适当性失败[%s]", e.getMessage());
            log.error(note, e);
            throw new BusinessException(Response.buildErrorMsg(-601, note));
        }
    }

    @Override
    public Page<QtyktywtjVo> compute4cxywlb(String khh, String cxywlb, String rq, boolean isSearchCount, int pagesize, int pagenum) {
        Page<QtyktywtjVo> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);
        try {
            return this.baseMapper.compute4cxywlb(page, khh, cxywlb, rq);
        } catch (Exception e) {
            String note = String.format("统计全体已开通业务适当性异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-602, note);
        }
    }

    @Override
    public List<QtyktywtjVo> computeByBsdx(String rq) {
        try {
            return this.baseMapper.compute(null, "0", rq);
        } catch (Exception e) {
            String note = String.format("统计业务不适当协议签署情况异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-603, note);
        }
    }

    @Override
    public Page<QtyktywtjVo> computemxBySdx(String khh, String cxywlb, String qsbsdxy, String rq, boolean isSearchCount, int pagesize, int pagenum) {
        Page<QtyktywtjVo> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);
        try {
            return this.baseMapper.computemxBySdx(page, khh, cxywlb, qsbsdxy, rq);
        } catch (Exception e) {
            String note = String.format("全体已开通业务不适当明细统计异常：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-604, note);
        }
    }

    @Override
    public List<QtyktywtjVo> compute(String khh, String rq) {
        try {
            return this.baseMapper.compute(khh, null, rq);
        } catch (Exception e) {
            String note = String.format("客户已开通业务适当性统计异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-605, note);
        }
    }
}




