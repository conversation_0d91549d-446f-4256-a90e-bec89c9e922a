package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.api.vo.khgl.KhwjcplsVo;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhsdx;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *
 */
public interface TOdsYgtCifTkhsdxService extends IService<TOdsYgtCifTkhsdx> {

    /**
     * 获取客户问卷适当性信息
     * @param khh
     * @param ksrq
     * @param jsrq
     * @param isSearchCount
     * @param pagesize
     * @param pagenum
     * @return
     */
    Page<KhwjcplsVo> getWjsdxByKhh(String khh, Integer ksrq, Integer jsrq, boolean isSearchCount, int pagesize, int pagenum);
}
