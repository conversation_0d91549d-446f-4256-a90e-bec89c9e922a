<template>
  <div style="display: flex">
    <div style="display: flex; align-items: center">
      <div
          style="
          background-color: white;
          border-radius: 50%;
          height: 15px;
          width: 15px;
          border: 3px solid #bfbfbf;
        "
      ></div>
    </div>
  </div>
</template>

<script>
export default({
  // 注入 getNode 函数
  inject: ['getNode'],

  data() {
    // 初始化任务数据
    return {
      tasks: []
    };
  },

  mounted() {
    // 获取节点实例
    const node = this.getNode();
    // 监听节点数据变化
    node.on('change:data', ({ current }) => {
      this.tasks = current?.tasks || [];
    });
    // 初始化任务数据
    this.tasks = node.getData()?.tasks || [];
  }/*,

  methods: {
    // 处理任务点击事件的方法
    handleTaskClick(task) {
      const node = this.getNode();
      node.getData().openDetailModal(task.id);
    }
  }*/
});
</script>
<style>

</style>