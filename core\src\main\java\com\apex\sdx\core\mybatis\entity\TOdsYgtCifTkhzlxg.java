package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName t_ods_ygt_cif_tkhzlxg
 */
@TableName(value ="t_ods_ygt_cif_tkhzlxg", schema = "ods")
@Data
public class TOdsYgtCifTkhzlxg implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 
     */
    private String khh;

    /**
     * 
     */
    private Integer xgrq;

    /**
     * 
     */
    private String xgsj;

    /**
     * 
     */
    private String zd;

    /**
     * 
     */
    private String zdmc;

    /**
     * 
     */
    private String oldvalue;

    /**
     * 
     */
    private String newvalue;

    /**
     * 
     */
    private String oldname;

    /**
     * 
     */
    private String newname;

    /**
     * 
     */
    private Long xgr;

    /**
     * 
     */
    private Integer xgqd;

    /**
     * 
     */
    private Long czmxid;

    /**
     * 
     */
    private Integer sfxs;

    /**
     * 
     */
    private String gxy;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TOdsYgtCifTkhzlxg other = (TOdsYgtCifTkhzlxg) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
            && (this.getXgrq() == null ? other.getXgrq() == null : this.getXgrq().equals(other.getXgrq()))
            && (this.getXgsj() == null ? other.getXgsj() == null : this.getXgsj().equals(other.getXgsj()))
            && (this.getZd() == null ? other.getZd() == null : this.getZd().equals(other.getZd()))
            && (this.getZdmc() == null ? other.getZdmc() == null : this.getZdmc().equals(other.getZdmc()))
            && (this.getOldvalue() == null ? other.getOldvalue() == null : this.getOldvalue().equals(other.getOldvalue()))
            && (this.getNewvalue() == null ? other.getNewvalue() == null : this.getNewvalue().equals(other.getNewvalue()))
            && (this.getOldname() == null ? other.getOldname() == null : this.getOldname().equals(other.getOldname()))
            && (this.getNewname() == null ? other.getNewname() == null : this.getNewname().equals(other.getNewname()))
            && (this.getXgr() == null ? other.getXgr() == null : this.getXgr().equals(other.getXgr()))
            && (this.getXgqd() == null ? other.getXgqd() == null : this.getXgqd().equals(other.getXgqd()))
            && (this.getCzmxid() == null ? other.getCzmxid() == null : this.getCzmxid().equals(other.getCzmxid()))
            && (this.getSfxs() == null ? other.getSfxs() == null : this.getSfxs().equals(other.getSfxs()))
            && (this.getGxy() == null ? other.getGxy() == null : this.getGxy().equals(other.getGxy()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getXgrq() == null) ? 0 : getXgrq().hashCode());
        result = prime * result + ((getXgsj() == null) ? 0 : getXgsj().hashCode());
        result = prime * result + ((getZd() == null) ? 0 : getZd().hashCode());
        result = prime * result + ((getZdmc() == null) ? 0 : getZdmc().hashCode());
        result = prime * result + ((getOldvalue() == null) ? 0 : getOldvalue().hashCode());
        result = prime * result + ((getNewvalue() == null) ? 0 : getNewvalue().hashCode());
        result = prime * result + ((getOldname() == null) ? 0 : getOldname().hashCode());
        result = prime * result + ((getNewname() == null) ? 0 : getNewname().hashCode());
        result = prime * result + ((getXgr() == null) ? 0 : getXgr().hashCode());
        result = prime * result + ((getXgqd() == null) ? 0 : getXgqd().hashCode());
        result = prime * result + ((getCzmxid() == null) ? 0 : getCzmxid().hashCode());
        result = prime * result + ((getSfxs() == null) ? 0 : getSfxs().hashCode());
        result = prime * result + ((getGxy() == null) ? 0 : getGxy().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", khh=").append(khh);
        sb.append(", xgrq=").append(xgrq);
        sb.append(", xgsj=").append(xgsj);
        sb.append(", zd=").append(zd);
        sb.append(", zdmc=").append(zdmc);
        sb.append(", oldvalue=").append(oldvalue);
        sb.append(", newvalue=").append(newvalue);
        sb.append(", oldname=").append(oldname);
        sb.append(", newname=").append(newname);
        sb.append(", xgr=").append(xgr);
        sb.append(", xgqd=").append(xgqd);
        sb.append(", czmxid=").append(czmxid);
        sb.append(", sfxs=").append(sfxs);
        sb.append(", gxy=").append(gxy);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}