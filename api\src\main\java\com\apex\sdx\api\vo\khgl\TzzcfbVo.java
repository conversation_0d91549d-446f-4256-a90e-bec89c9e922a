package com.apex.sdx.api.vo.khgl;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 投资者构成及分布VO
 */
@Setter
@Getter
public class TzzcfbVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 风险承受能力数组
     */
    @LiveProperty(note = "风险承受能力数组", index = 1)
    private List<String> fxcsnlArray;

    /**
     * 普通投资者各风险等级总数数组
     */
    @LiveProperty(note = "普通投资者各风险等级总数数组", index = 2)
    private List<Integer> pttzrCountArray;

    /**
     * 普通投资者各风险等级占比数组
     */
    @LiveProperty(note = "普通投资者各风险等级占比数组", index = 3)
    private List<Double> pttzrPercentArray;

    /**
     * 专业投资者各风险等级总数数组
     */
    @LiveProperty(note = "专业投资者各风险等级总数数组", index = 4)
    private List<Integer> zytzrCountArray;

    /**
     * 专业投资者各风险等级占比数组
     */
    @LiveProperty(note = "专业投资者各风险等级占比数组", index = 5)
    private List<Double> zytzrPercentArray;
} 