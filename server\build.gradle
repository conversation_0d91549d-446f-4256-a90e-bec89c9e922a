//gradle插件
plugins {
    id 'java'
    //springBoot插件
    id 'org.springframework.boot' version "${springBootVersion}"
    //spring依赖管理插件
    id 'io.spring.dependency-management' version "${springDependencyManagement}"
}

//springBoot构建buildInfo信息
springBoot {
    buildInfo()
}
dependencyManagement {
    imports {
        //springBoot的依赖管理模板
        mavenBom org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES
        //springCloud的依赖管理，需要和SpringBoot配套
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        //LiveSupport的依赖管理
        mavenBom "com.apexsoft:live-spring-boot2-bom:${liveVersion}"
    }
    resolutionStrategy{
        cacheChangingModulesFor 0, 'seconds'
    }
}
dependencies {
    //希望暴露为SpringCloud服务
    implementation "com.apexsoft:live-service-http-exporter-starter:${liveVersion}"
    //希望暴露为liveAMS服务
    implementation "com.apexsoft:live-service-grpc-exporter-starter:${liveVersion}"
    implementation "com.apexsoft.live-gateway:protocol-extend-ygt-exporter:1.0.2-SNAPSHOT"
    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    //服务监听
    implementation "com.apexsoft:live-spring-boot-actuator-starter:${liveVersion}"
    implementation "com.apex:ams-spring-boot-starter-actuator"
    implementation "com.apex:ams-metrics-prometheus"

    implementation(project(":core"))
    implementation(project(":gateway"))
    implementation(project(":service"))
}

configurations.all {
    exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    exclude group: "org.slf4j", module: "slf4j-log4j12"
}

test {
    useJUnitPlatform()
}

publish.enabled=false

//将其他模块的jar包放到modules下
task copyModulesJar(type: Copy) {
    // 清除现有的lib目录
    delete "$buildDir/libs"
    from configurations.runtimeClasspath.filter {
        it.name.startsWith("sdx-")
    }
    into "$buildDir/libs/modules"
}
// 将第三方依赖包复制到lib目录
task copyJar(type: Copy) {
    from configurations.runtimeClasspath.filter {
        !it.name.startsWith("sdx-")
    }
    into "$buildDir/libs/lib"
}

// 拷贝配置文件
task copyConfigFile(type: Copy) {
    // 清除现有的配置目录
    delete "$buildDir/libs/config"
    from 'src/main/resources'
    into 'build/libs/config'
}
// 依赖jar包与主程序分离式打包
bootJar {
    archiveBaseName = 'sdx-server'
    archiveVersion =  ''
    //分离jar包
    excludes = ["*.jar"]

    // 拷贝文件到lib
    dependsOn copyModulesJar
    dependsOn copyJar
    dependsOn copyConfigFile
    // 指定依赖包的路径
    manifest {
        attributes 'Class-Path': project.configurations.runtimeClasspath.files.collect {
            if (it.name.startsWith("sdx-")) {
                return "modules/$it.name"
            }else {
                return "lib/$it.name"
            }
        }.join(' ')
    }
}