package com.apex.sdx.gateway.base.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.apex.ams.livebos.services.LoadNotificationReply;
import com.apex.ams.livebos.services.NotificationInfo;
import com.apex.ams.livebos.services.Result;
import com.apex.sdx.core.mybatis.entity.Tuser;
import com.apex.sdx.core.mybatis.service.impl.TuserServiceImpl;
import com.apex.sdx.core.utils.DateUtil;
import com.apex.sdx.gateway.aas.common.session.UserSession;
import com.apex.sdx.gateway.aas.modules.index.model.AuthUser;
import com.apex.sdx.gateway.base.dao.LivebosGrpcDao;
import com.apex.sdx.gateway.base.dao.UserDao;
import com.apex.sdx.gateway.base.model.CommonResponse;
import com.apex.sdx.gateway.base.model.JSONResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Iterator;

@Slf4j
@Service
public class UserService {

    @Autowired
    private LivebosGrpcDao livebosGRPC;
    @Autowired
    private UserDao userDao;
    @Autowired
    private TuserServiceImpl tuserService;
    /**
     * 获取用户信息
     *
     * @return
     * @throws Exception
     */
    public JSONObject getUserInfo(String userID) throws Exception {
        JSONObject userObj = new JSONObject();
        Tuser userInfo = this.cxgyxxByIdOrUserid(null, userID);
        if(userInfo != null){
            String jsonString= JSONObject.toJSONString(userInfo);
            //再将String类型转为JSONObject
            userObj = JSONObject.parseObject(jsonString);
            userObj.put("lastlogin", DateUtil.dateFormat(userInfo.getLastlogin(), DateUtil.F_DATE8_TIME8));
            userObj.put("chgpwdtime", DateUtil.dateFormat(userInfo.getChgpwdtime(), DateUtil.F_DATE8_TIME8));
            userObj.put("lasttrytime", DateUtil.dateFormat(userInfo.getLasttrytime(), DateUtil.F_DATE8_TIME8));
            userObj.put("locktime", DateUtil.dateFormat(userInfo.getLocktime(), DateUtil.F_DATE8_TIME8));
            if (userInfo.getPhoto() != null && userInfo.getPhoto().length > 0) {
                byte[] temp = userInfo.getPhoto();
                String hex = Integer.toHexString(temp[0] & 0xFF);
                int nameLength = Integer.parseInt(hex, 16);
                temp = ArrayUtils.subarray(temp, nameLength + 1, temp.length);
                userObj.put("photo", temp);
            }
        } else {
            throw new Exception("查询柜员信息失败: 改柜员不存在");
        }
        return userObj;
    }
    public Tuser cxgyxxByIdOrUserid(Long id, String userid) {
        LambdaQueryWrapper<Tuser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(id != null, Tuser::getId, id)
                .eq(StringUtils.isNotBlank(userid), Tuser::getUserid, userid);
        return tuserService.getOne(wrapper, false);
    }
    /**
     * 修改用户密码
     *
     * @return
     * @throws Exception
     */
    public JSONResponse changeUserPassword(String userID, String oldPassword, String newPassword) throws Exception {
        JSONResponse response = new JSONResponse(JSONResponse.CODE_SUCCESS, "密码修改成功");
        Result result = livebosGRPC.changeUserPassword(userID, oldPassword, newPassword);
        if(result.getResult()<0){
            response = new JSONResponse(JSONResponse.CODE_FAIL, "密码修改失败:"+result.getMessage());
        }
        return response;
    }

    /**
     * 修改用户密码
     *
     * @return
     * @throws Exception
     */
    public JSONResponse updatePhoto(String uid, File file, String fileName) throws Exception {
        JSONResponse response = new JSONResponse(JSONResponse.CODE_SUCCESS, "头像修改成功");
        JSONObject result = userDao.streamUploadService("ecif.xtgl.IGxgytxService", "gxtx", file, fileName, uid);
        if(result.getIntValue("code")<0){
            response = new JSONResponse(result.getIntValue("code"),result.getString("note"));
        }
        return response;
    }

    /**
     * 获取待办任务数
     *
     * @return
     */
    public JSONObject getDbWorkCount() {
        JSONObject result = new JSONObject();
        int workCount = 0;
        try {
            AuthUser<JSONObject> authUser = UserSession.getUserSession();
            JSONObject userInfo = authUser.getUser();
            workCount = livebosGRPC.getWorkTaskCount(authUser.getId()+"");
        } catch (Exception e) {
            log.error(e.toString());
        }
        result.put("workCount", workCount);
        return result;
    }

    /**
     * 平台消息
     * @return
     */
    public JSONObject getUnreadNotificationCount() {
        JSONObject result = new JSONObject();
        int workCount = 0;
        try {
            AuthUser<JSONObject> authUser = UserSession.getUserSession();
            JSONObject userInfo = authUser.getUser();
            workCount = livebosGRPC.getUnreadNotificationCount(authUser.getId()+"");
        } catch (Exception e) {
            log.error(e.toString());
        }
        result.put("msgCount", workCount);
        return result;
    }

    /**
     *
     * @param userId
     * @param setNotificationType：Workflow
     * @param pageNo
     * @param pageSize
     * @return
     * @throws Exception
     */
    public JSONObject loadNotification(String userId,String setNotificationType,int pageNo,int pageSize) throws Exception {
        Iterator<LoadNotificationReply> loadNotificationReplyIterator = livebosGRPC.loadNotification(userId, setNotificationType, pageNo, pageSize);
        JSONObject result = new JSONObject();
        JSONArray records =new JSONArray();
        if (loadNotificationReplyIterator.hasNext()){
            LoadNotificationReply qrs =  loadNotificationReplyIterator.next();
            result.put("count",qrs.getResult().getCount());
            while (loadNotificationReplyIterator.hasNext()){
                LoadNotificationReply qrs2 =  loadNotificationReplyIterator.next();
                NotificationInfo notification2= qrs2.getNotification();
                JSONObject item2 = new JSONObject();
                item2.put("id", notification2.getId());
                item2.put("sender", notification2.getSender());
                item2.put("objectid", notification2.getObjectId());
                item2.put("notificationtype", notification2.getNotificationType());
                item2.put("title", notification2.getTitle());
                item2.put("content", notification2.getContent());
                item2.put("sendtime", notification2.getSendTime());
                records.add(item2);
            }
            result.put("code",1);
        }
        result.put("records",records);
        return result;
    }

    public CommonResponse sendNotification(int sender, boolean isAll, String receiver, String notificationType, String title, String content, String objectId){
        CommonResponse data = new CommonResponse(JSONResponse.CODE_SUCCESS,"成功");
        try {

            Result result = livebosGRPC.sendNotification(sender, isAll, receiver, notificationType, title, content, objectId);
            if(result.getResult()<0){
                data = new CommonResponse(JSONResponse.CODE_FAIL,result.getMessage());
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
            data = new CommonResponse(JSONResponse.CODE_FAIL,e.getMessage());
        }
        return data;
    }


    /**
     * 标注已读消息
     * @param notificationId
     * @param userId
     * @throws Exception
     */
    public CommonResponse readNotification(long notificationId,String userId) throws Exception {
        CommonResponse commonResponse = new CommonResponse(JSONResponse.CODE_SUCCESS, "成功");
        Result result = livebosGRPC.readNotification(notificationId, userId);
        if(result.getResult()<0){
            commonResponse = new CommonResponse(JSONResponse.CODE_FAIL, result.getMessage());
        }
        return commonResponse;
    }
}
