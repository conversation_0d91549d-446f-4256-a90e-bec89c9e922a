package com.apex.sdx.api.vo.khgl;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> chenglingyu
 *
 * @create 2025/5/9 13:50
 */
@Setter
@Getter
public class ZhsdxqkVo {

  @LiveProperty(note = "客户号", index = 1)
  private String khh;

  @LiveProperty(note = "客户名称", index = 2)
  private String khmc;

  @LiveProperty(note = "账户情况", index = 3)
  private String zhqk;

  @LiveProperty(note = "账户状态", index = 4)
  private String zhzt;

  @LiveProperty(note = "证件有效期", index = 5)
  private String zjjzrq;

  @LiveProperty(note = "反洗钱风险等级", index = 6)
  private String xqfxdj;

  @LiveProperty(note = "反洗钱设置日期", index = 7)
  private String fxqszrq;

  @LiveProperty(note = "风险测评有效期", index = 8)
  private String cpyxq;

  @LiveProperty(note = "专业投资者测评有效期", index = 9)
  private String tzzpdyxq;

  @LiveProperty(note = "校验名单结果", index = 10)
  private String jymdjg;
}
