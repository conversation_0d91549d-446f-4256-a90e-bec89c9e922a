<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTkhxyxxMapper">

    <resultMap id="BaseResultMap" type="com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhxyxx">
            <result property="khh" column="KHH" jdbcType="VARCHAR"/>
            <result property="ywxt" column="YWXT" jdbcType="DECIMAL"/>
            <result property="ywzh" column="YWZH" jdbcType="VARCHAR"/>
            <result property="glywxt" column="GLYWXT" jdbcType="DECIMAL"/>
            <result property="glywzh" column="GLYWZH" jdbcType="VARCHAR"/>
            <result property="xydj" column="XYDJ" jdbcType="DECIMAL"/>
            <result property="pjzf" column="PJZF" jdbcType="DECIMAL"/>
            <result property="zxrq" column="ZXRQ" jdbcType="DECIMAL"/>
            <result property="zxyxq" column="ZXYXQ" jdbcType="DECIMAL"/>
            <result property="email" column="EMAIL" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        KHH,YWXT,YWZH,
        GLYWXT,GLYWZH,XYDJ,
        PJZF,ZXRQ,ZXYXQ,
        EMAIL
    </sql>
</mapper>
