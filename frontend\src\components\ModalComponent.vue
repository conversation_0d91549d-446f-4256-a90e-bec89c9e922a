<template>
  <div ref="pageModalComponent" class="page-modal-component">
    <a-modal
      v-model:open="isOpen"
      centered
      :title="title"
      :onCancel="handleOk"
      :width="modalWidth"
      :forceRender="forceRender"
      v-bind="footer"
      :getContainer="() => $refs.pageModalComponent"
      :wrap-class-name="wrapClassName"
      destroyOnClose
      :bodyStyle="{
        height: height ? `${height-121}px` : ''
      }"
    >
      <template #closeIcon>
        <div>
          <fullscreen-outlined
            style="padding-right: 10px; color: #BF935F"
            v-if="showFullScreenBtn && !isFullScreen"
            @click.stop="setFullScreen"
          />
          <fullscreen-exit-outlined
            style="padding-right: 10px; color: #BF935F"
            v-if="showFullScreenBtn && isFullScreen"
            @click.stop="exitFullScreen"
          />
          <close-outlined
            style="padding-right: 10px; color: #BF935F"
            @click.stop="handleOk"
          />
        </div>
      </template>

      <div class="content-container">
        <slot name="content"></slot>
      </div>
      <template #footer>
        <slot name="footer"></slot>
        <div>
          <a-button key="back" @click="handleOk">
            {{ cancelText ? cancelText : '取消' }}
          </a-button>
          <a-button
            v-if="showConfirmBtn"
            key="back"
            type="primary"
            @click="handleOk"
            style="color: #B48A3B; background-color: #f6e5d1"
          >
            确定
          </a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script>
import {defineComponent} from "vue";
import { CloseOutlined, FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  name: 'modalComponent',

  components: {
    CloseOutlined,
    FullscreenOutlined,
    FullscreenExitOutlined
  },

  props: {
    open: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      required: true
    },
    width: {
      type: String,
      default: '500px'
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    destroyOnClose: {
      type: Boolean,
      default: false
    },
    onCancel: {
      type: Function
    },
    onConfirm: {
      type: Function
    },
    cancelText: {
      type: String
    },
    showConfirmBtn: {
      type: Boolean,
      default: true
    },
    showCancelBtn: {
      type: Boolean,
      default: true
    },
    showFullScreenBtn: {
      type: Boolean,
      default: true
    },
    height: {
      type: Number
    },
    forceRender: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      isOpen: this.open,
      modalWidth: this.width,
      isFullScreen: false,
      wrapClassName: 'default-size-modal'
    }
  },

  watch: {
    open(newVal) {
      this.isOpen = newVal
    }
  },

  computed: {
    footer() {
      if (this.showFooter) {
        return {}
      } else {
        return {
          footer: null
        }
      }
    }
  },

  methods: {
    handleOk() {
      this.isOpen = false
      this.onCancel && this.onCancel()
    },

    confirm() {
      this.$emit('onConfirm', true)
    },

    setFullScreen() {
      this.wrapClassName = 'full-modal'
      this.modalWidth = '100%'
      this.isFullScreen = true
    },

    exitFullScreen() {
      this.wrapClassName = 'default-size-modal'
      this.modalWidth = this.width
      this.isFullScreen = false
    }
  }
});
</script>

<style scoped>
.page-modal-component [class*='-modal-content'] {
  /* height: 100% !important; */
}

.page-modal-component [class*='-modal-header'] {
  border-bottom: none;
}

.page-modal-component [class*='-modal-header'] [class*='-modal-title'] {
  font-weight: bold;
}

.page-modal-component [class*='-modal-footer'] {
  border-top: none;
}

.page-modal-component [class*='-modal-body'] {
  padding-top: 10px;
}

.page-modal-component :deep(.modal-action-btns) {
  pointer-events: auto;
}

:deep(.full-modal) .ant-modal-content,
:deep(.full-modal) .apex-red-modal-content,
:deep(.full-modal) .black-gold-modal-content {
  display: flex;
  flex-direction: column;
  height: calc(100vh) !important;
}

:deep(.full-modal) .ant-modal-body,
:deep(.full-modal) .apex-red-modal-body,
:deep(.full-modal) .black-gold-modal-body {
  flex: 1;
}

:deep(.ant-modal-header) {
  padding-left: 20px;
}

:deep(.ant-modal .ant-modal-close) {
  width: 50px;
}

:deep(.ant-modal .ant-modal-content) {
  padding: 20px 0;
}

:deep(.ant-modal-footer) {
  padding-right: 20px;
}

.content-container {
  background-color: #F1F2F6;
  padding: 10px;
  height: 100%;
}
</style>