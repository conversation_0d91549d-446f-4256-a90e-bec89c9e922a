package com.apex.sdx.gateway.aas.modules.index.service;

import com.alibaba.fastjson.JSONObject;
import com.apex.sdx.gateway.base.model.JSONResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


public class DefaultServiceHandler implements IServiceHandler {

    @Override
    public JSONObject preHandle(String func, String version, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            return JSONObject.parseObject(Reader.getData(request));
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public JSONResponse postHandle(JSONResponse jsonResponse, HttpServletRequest request, HttpServletResponse response) {
        return jsonResponse;
    }
}
