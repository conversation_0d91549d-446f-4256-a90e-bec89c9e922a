package com.apex.sdx.svc;

import com.apex.sdx.api.req.compute.QtyktywtjReq;
import com.apex.sdx.api.req.query.YktywsdxcxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.compute.QtyktywtjVo;
import com.apex.sdx.api.vo.query.YktywsdxVo;
import com.apex.sdx.convert.YktywsdxVoMapping;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.entity.TsdxjgQtyktyw;
import com.apex.sdx.core.mybatis.service.TsdxjgQtyktywService;
import com.apexsoft.LiveService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import sdx.query.IYktywsdxService;

import java.util.List;

/**
 * <AUTHOR> Jingliang
 * @Date 2025-03-05
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "已开通业务适当性查询服务")
@RequiredArgsConstructor
public class YktywsdxService implements IYktywsdxService {

    private final TsdxjgQtyktywService qtyktywService;

    private final YktywsdxVoMapping yktywsdxVoMapping;

    @Override
    public QueryPageResponse<YktywsdxVo> yktywsdxcx(YktywsdxcxReq req) throws Exception {
        Assert.notNull(req, YktywsdxcxReq::getKhh);
        QueryPageResponse<YktywsdxVo> result = new QueryPageResponse<>(1, "查询成功");

        String khh = req.getKhh();
        boolean onlysdx = req.isOnlysdx();
        String rq = req.getRq();

        Page<TsdxjgQtyktyw> page = qtyktywService.queryPageByKhhAndSdxjg(khh, onlysdx, rq, req.isSearchCount(), req.getPagesize(), req.getPagenum());

        Page<YktywsdxVo> yktywsdxVoPage = yktywsdxVoMapping.pageConver(page);

        result.page(yktywsdxVoPage);

        return result;
    }

    @Override
    public QueryPageResponse<QtyktywtjVo> ywktSdxtj4cxywlb(QtyktywtjReq req) throws Exception {
        QueryPageResponse<QtyktywtjVo> result = new QueryPageResponse<>(1, "查询成功");
        String khh = req.getKhh();
        String cxywlb = req.getCxywlb();
        String rq = req.getRq();

        Page<QtyktywtjVo> qtyktywtjVoPage = qtyktywService.compute4cxywlb(khh, cxywlb, rq, req.isSearchCount(), req.getPagesize(), req.getPagenum());

        result.page(qtyktywtjVoPage);
        return result;
    }

    @Override
    public QueryResponse<QtyktywtjVo> qtyktywBsdxtj(QtyktywtjReq req) throws Exception {
        QueryResponse<QtyktywtjVo> result = new QueryResponse<>(1, "查询成功");
        String rq = req.getRq();

        List<QtyktywtjVo> list = qtyktywService.computeByBsdx(rq);

        result.setRecords(list);
        return result;
    }

    @Override
    public QueryPageResponse<QtyktywtjVo> qtyktywBsdxmxtj(QtyktywtjReq req) throws Exception {
        QueryPageResponse<QtyktywtjVo> result = new QueryPageResponse<>(1, "查询成功");
        String khh = req.getKhh();
        String cxywlb = req.getCxywlb();
        String qsbsdxy = req.getQsbsdxy();
        String rq = req.getRq();
        
        Page<QtyktywtjVo> qtyktywtjVoPage = qtyktywService.computemxBySdx(khh, cxywlb, qsbsdxy, rq, req.isSearchCount(), req.getPagesize(), req.getPagenum());
        result.page(qtyktywtjVoPage);
        return result;
    }

    @Override
    public QueryResponse<QtyktywtjVo> khyktywsdxtj(QtyktywtjReq req) throws Exception {
        Assert.notNull(req, QtyktywtjReq::getKhh);

        QueryResponse<QtyktywtjVo> result = new QueryResponse<>(1, "查询成功");
        String khh = req.getKhh();
        String rq = req.getRq();

        List<QtyktywtjVo> list = qtyktywService.compute(khh, rq);

        result.setRecords(list);
        return result;
    }

}
