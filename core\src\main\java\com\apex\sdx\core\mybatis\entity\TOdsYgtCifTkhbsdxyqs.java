package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

/**
 * 
 * @TableName t_ods_ygt_cif_tkhbsdxyqs
 */
@TableName(value ="t_ods_ygt_cif_tkhbsdxyqs", schema = "ods")
public class TOdsYgtCifTkhbsdxyqs implements Serializable {
    /**
     * 
     */
    private String khh;

    /**
     * 
     */
    private Long cxywlb;

    /**
     * 
     */
    private Integer fxcsnl;

    /**
     * 
     */
    private Integer khtzqx;

    /**
     * 
     */
    private String khtzpz;

    /**
     * 
     */
    private String khyqsy;

    /**
     * 
     */
    private Integer ywfxdj;

    /**
     * 
     */
    private Integer ywtzqx;

    /**
     * 
     */
    private String ywtzpz;

    /**
     * 
     */
    private String ywyqsy;

    /**
     * 
     */
    private Long fxjsid;

    /**
     * 
     */
    private Long xyqsid;

    /**
     * 
     */
    private Integer qsrq;

    /**
     * 
     */
    private Integer bsdlx;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public String getKhh() {
        return khh;
    }

    /**
     * 
     */
    public void setKhh(String khh) {
        this.khh = khh;
    }

    /**
     * 
     */
    public Long getCxywlb() {
        return cxywlb;
    }

    /**
     * 
     */
    public void setCxywlb(Long cxywlb) {
        this.cxywlb = cxywlb;
    }

    /**
     * 
     */
    public Integer getFxcsnl() {
        return fxcsnl;
    }

    /**
     * 
     */
    public void setFxcsnl(Integer fxcsnl) {
        this.fxcsnl = fxcsnl;
    }

    /**
     * 
     */
    public Integer getKhtzqx() {
        return khtzqx;
    }

    /**
     * 
     */
    public void setKhtzqx(Integer khtzqx) {
        this.khtzqx = khtzqx;
    }

    /**
     * 
     */
    public String getKhtzpz() {
        return khtzpz;
    }

    /**
     * 
     */
    public void setKhtzpz(String khtzpz) {
        this.khtzpz = khtzpz;
    }

    /**
     * 
     */
    public String getKhyqsy() {
        return khyqsy;
    }

    /**
     * 
     */
    public void setKhyqsy(String khyqsy) {
        this.khyqsy = khyqsy;
    }

    /**
     * 
     */
    public Integer getYwfxdj() {
        return ywfxdj;
    }

    /**
     * 
     */
    public void setYwfxdj(Integer ywfxdj) {
        this.ywfxdj = ywfxdj;
    }

    /**
     * 
     */
    public Integer getYwtzqx() {
        return ywtzqx;
    }

    /**
     * 
     */
    public void setYwtzqx(Integer ywtzqx) {
        this.ywtzqx = ywtzqx;
    }

    /**
     * 
     */
    public String getYwtzpz() {
        return ywtzpz;
    }

    /**
     * 
     */
    public void setYwtzpz(String ywtzpz) {
        this.ywtzpz = ywtzpz;
    }

    /**
     * 
     */
    public String getYwyqsy() {
        return ywyqsy;
    }

    /**
     * 
     */
    public void setYwyqsy(String ywyqsy) {
        this.ywyqsy = ywyqsy;
    }

    /**
     * 
     */
    public Long getFxjsid() {
        return fxjsid;
    }

    /**
     * 
     */
    public void setFxjsid(Long fxjsid) {
        this.fxjsid = fxjsid;
    }

    /**
     * 
     */
    public Long getXyqsid() {
        return xyqsid;
    }

    /**
     * 
     */
    public void setXyqsid(Long xyqsid) {
        this.xyqsid = xyqsid;
    }

    /**
     * 
     */
    public Integer getQsrq() {
        return qsrq;
    }

    /**
     * 
     */
    public void setQsrq(Integer qsrq) {
        this.qsrq = qsrq;
    }

    /**
     * 
     */
    public Integer getBsdlx() {
        return bsdlx;
    }

    /**
     * 
     */
    public void setBsdlx(Integer bsdlx) {
        this.bsdlx = bsdlx;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TOdsYgtCifTkhbsdxyqs other = (TOdsYgtCifTkhbsdxyqs) that;
        return (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
            && (this.getCxywlb() == null ? other.getCxywlb() == null : this.getCxywlb().equals(other.getCxywlb()))
            && (this.getFxcsnl() == null ? other.getFxcsnl() == null : this.getFxcsnl().equals(other.getFxcsnl()))
            && (this.getKhtzqx() == null ? other.getKhtzqx() == null : this.getKhtzqx().equals(other.getKhtzqx()))
            && (this.getKhtzpz() == null ? other.getKhtzpz() == null : this.getKhtzpz().equals(other.getKhtzpz()))
            && (this.getKhyqsy() == null ? other.getKhyqsy() == null : this.getKhyqsy().equals(other.getKhyqsy()))
            && (this.getYwfxdj() == null ? other.getYwfxdj() == null : this.getYwfxdj().equals(other.getYwfxdj()))
            && (this.getYwtzqx() == null ? other.getYwtzqx() == null : this.getYwtzqx().equals(other.getYwtzqx()))
            && (this.getYwtzpz() == null ? other.getYwtzpz() == null : this.getYwtzpz().equals(other.getYwtzpz()))
            && (this.getYwyqsy() == null ? other.getYwyqsy() == null : this.getYwyqsy().equals(other.getYwyqsy()))
            && (this.getFxjsid() == null ? other.getFxjsid() == null : this.getFxjsid().equals(other.getFxjsid()))
            && (this.getXyqsid() == null ? other.getXyqsid() == null : this.getXyqsid().equals(other.getXyqsid()))
            && (this.getQsrq() == null ? other.getQsrq() == null : this.getQsrq().equals(other.getQsrq()))
            && (this.getBsdlx() == null ? other.getBsdlx() == null : this.getBsdlx().equals(other.getBsdlx()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getCxywlb() == null) ? 0 : getCxywlb().hashCode());
        result = prime * result + ((getFxcsnl() == null) ? 0 : getFxcsnl().hashCode());
        result = prime * result + ((getKhtzqx() == null) ? 0 : getKhtzqx().hashCode());
        result = prime * result + ((getKhtzpz() == null) ? 0 : getKhtzpz().hashCode());
        result = prime * result + ((getKhyqsy() == null) ? 0 : getKhyqsy().hashCode());
        result = prime * result + ((getYwfxdj() == null) ? 0 : getYwfxdj().hashCode());
        result = prime * result + ((getYwtzqx() == null) ? 0 : getYwtzqx().hashCode());
        result = prime * result + ((getYwtzpz() == null) ? 0 : getYwtzpz().hashCode());
        result = prime * result + ((getYwyqsy() == null) ? 0 : getYwyqsy().hashCode());
        result = prime * result + ((getFxjsid() == null) ? 0 : getFxjsid().hashCode());
        result = prime * result + ((getXyqsid() == null) ? 0 : getXyqsid().hashCode());
        result = prime * result + ((getQsrq() == null) ? 0 : getQsrq().hashCode());
        result = prime * result + ((getBsdlx() == null) ? 0 : getBsdlx().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", khh=").append(khh);
        sb.append(", cxywlb=").append(cxywlb);
        sb.append(", fxcsnl=").append(fxcsnl);
        sb.append(", khtzqx=").append(khtzqx);
        sb.append(", khtzpz=").append(khtzpz);
        sb.append(", khyqsy=").append(khyqsy);
        sb.append(", ywfxdj=").append(ywfxdj);
        sb.append(", ywtzqx=").append(ywtzqx);
        sb.append(", ywtzpz=").append(ywtzpz);
        sb.append(", ywyqsy=").append(ywyqsy);
        sb.append(", fxjsid=").append(fxjsid);
        sb.append(", xyqsid=").append(xyqsid);
        sb.append(", qsrq=").append(qsrq);
        sb.append(", bsdlx=").append(bsdlx);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}