<template>
  <div class="dxsdx_zhsdx_nav">
    <div class="dxsdx_khqjgk_title">产品适当性</div>
  <div class="sdxlx-tabs">
    <a-tabs v-model:activeKey="activeKey" type="card" size="small">
      <a-tab-pane key="1" tab="金融产品">
        <jrcp :dictArray="dictArray"></jrcp>
      </a-tab-pane>
      <a-tab-pane key="2" tab="投顾产品">
        <tgcp :dictArray="dictArray"></tgcp>
      </a-tab-pane>
    </a-tabs>
  </div>
  </div>
</template>
<script>
import {defineComponent} from "vue";
import tgcp from "@views/dhcx/cpsdx/tgcp.vue";
import jrcp from "@views/dhcx/cpsdx/jrcp.vue";

export default defineComponent({
  name: "cpsdx",
  components: {tgcp,jrcp},
  inject: ["khh","LastTradingDay"],
  props: ['dictArray'],
  data() {
    return {
      activeKey: "1",
    }
  },
  watch: {
  },
  methods: {
  },
  mounted() {
  },
});
</script>
<style scoped>
.sdxlx-tabs {
  padding: 0;
}

:deep(.ant-tabs-card .ant-tabs-tab) {
  background-color: #F4F5F7;
  border-radius: 4px !important;
  margin: 5px;
  border: unset;
}

:deep(.ant-tabs-card .ant-tabs-tab-active) {
  background-color: #B48A3B;
  color: #FFFFFF;
}

:deep(.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
  color: #FFFFFF !important;
}

:deep(.ant-tabs-tab-btn) {
  color: #333333;
}

:deep(.ant-tabs-tab:before) {
  display: inline-block;
  content: "";
  width: 1px;
  height: 24px;
  background-color: #eee;
  vertical-align: middle;
  margin-top: -2px;
  position: relative;
  left: -20px;
}

.dxsdx_cxtj {
  line-height: 32px;
  padding: 0 5px 3px 10px;
}

.dxsdx_cxtj .cxtj_item {
  white-space: nowrap;
  margin-bottom: 12px;
  font-size: 14px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 20px;
}

.dxsdx_cxtj .cxtj_item span {
  color: #888;
  display: inline-block;
  margin-right: 15px;
  vertical-align: middle;
}

.dxsdx_cxtj .cxtj_item input[type=text] {
  display: inline-block;
  vertical-align: middle;
  width: 340px;
  height: 32px;
  line-height: 30px;
  border: 1px solid #d6d6d6;
  border-radius: 4px;
  padding: 0 10px;
}

.dxsdx_cxtj .cxtj_item input[type=text]:focus {
  outline: 1px solid #d0ad6b;
}

a.btn {
  min-width: 80px;
  padding: 0 10px;
  margin-right: 10px;
  text-align: center;
  line-height: 32px;
  border-radius: 4px;
  background-color: #f6e5d1;
  color: #bf935f;
  cursor: pointer;
  display: inline-block;
  vertical-align: middle;
}

a.btn.fz {
  background-color: #fff;
  color: #777;
  border: 1px solid #d6d6d6;
  line-height: 30px;
}

a.btn:hover, a.btn.fz:Hover {
  background-color: #bf935f;
  color: #fff;
  border: none;
  line-height: 32px;
}

.dxsdx_zhsdx_nav {
  //height: calc(100%);
  border-radius: 8px;
  background-color: #fff;
  overflow-y: auto;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.dxsdx_khqjgk_title {
  line-height: 52px;
  margin: 0 10px;
  border-bottom: 1px solid #eee;
  padding-left: 8px;
  font-size: 14px;
}

.dxsdx_khqjgk_title:before {
  display: inline-block;
  vertical-align: middle;
  content: "";
  width: 4px;
  height: 18px;
  margin-top: -2px;
  border-radius: 2px;
  background-color: #b48a3b;
  margin-right: 10px;
}
</style>