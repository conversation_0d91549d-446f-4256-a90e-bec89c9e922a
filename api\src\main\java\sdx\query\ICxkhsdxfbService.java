package sdx.query;

import com.apex.sdx.api.req.compute.KhzscsReq;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.resp.query.KhsdxfbRes;
import com.apex.sdx.api.resp.query.KhzsDateRes;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR>
 * @Date 2025/5/9 13:53
 * @Description: 查询客户适当性分布
 */
public interface ICxkhsdxfbService {

    @LiveMethod(paramAsRequestBody = true, note = "客户总数查询")
    QueryResponse<KhsdxfbRes> khzs(KhzscsReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "最近一个月客户总数查询")
    QueryResponse<KhzsDateRes> zkhsDate(KhzscsReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "投资者分类信息查询")
    QueryResponse<KhsdxfbRes> getTzzflxx(KhzscsReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "风险承受能力信息查询")
    QueryResponse<KhsdxfbRes> getFxcsnlxx(KhzscsReq req) throws Exception;
    
    @LiveMethod(paramAsRequestBody = true, note = "ABC类投资者数据查询")
    QueryResponse<KhsdxfbRes> getAbcTzzflxx(KhzscsReq req) throws Exception;
}
