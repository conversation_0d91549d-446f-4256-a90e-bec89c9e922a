package com.apex.sdx.gateway.base.controller;

import com.alibaba.fastjson.JSONObject;
import com.apex.sdx.gateway.aas.modules.index.service.DefaultServiceHandler;
import com.apex.sdx.gateway.base.dao.CommonDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


@RequestMapping("/")
@Controller
@Slf4j
public class GlobalServiceController {

    private DefaultServiceHandler defaultServiceHandler = new DefaultServiceHandler();
    @Autowired
    CommonDao commonDao;

    @RequestMapping(value = {"/validateSession"})
    @ResponseBody
    public JSONObject validateSession(HttpServletRequest request, HttpServletResponse response){
        return new JSONObject();
    }

    @RequestMapping(value = {"/ams/{serviceId}/{serviceMethod}"})
    @ResponseBody
    public JSONObject serviceGrpc(@PathVariable String serviceId, @PathVariable String serviceMethod,
                                   HttpServletRequest request, HttpServletResponse response) throws IOException, Exception {
        JSONObject data = defaultServiceHandler.preHandle(serviceId, serviceMethod, request, response);
        return commonDao.execAmsService(serviceId, serviceMethod, data, "");
    }
} 
