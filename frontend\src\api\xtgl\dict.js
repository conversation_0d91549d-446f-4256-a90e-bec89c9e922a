import request from '@/utils/request'
import {compact, uniq} from "lodash";
export default {
    // 刷新指定缓存
    refresh(zdlx) {
        return request({
            url: `/service/sdx.xtgl.IFwhcsxService/refresh`,
            method: 'post',
            data: {zdlx: zdlx}
        })
    },
    // 获取字典
    getDictByFldm(fldm) {
        return request({
            url: `/service/sdx.xtgl.IDictService/getDictByFldm`,
            method: 'post',
            data: {fldm: fldm}
        })
    }
}
export const dictApi = {
    cxsjzd: async (params, config) => {
        const fldm = params.fldm || "";
        if (!fldm) return Promise.reject(false);
        const fldmArray = compact(fldm.replace(/\s+/g, "").split(";"));
        let needFetchFldmArray = []; // 需要下载的字典
        fldmArray.forEach((item) => {
            if (item === "JGZJLB" || item === "GRZJLB") {
                needFetchFldmArray.push("SDX_ZJLB");
            } else {
                needFetchFldmArray.push(item);
            }
        });
        needFetchFldmArray = uniq(compact(needFetchFldmArray));

        let sjzd = {};

        if (needFetchFldmArray.length) {
            const result = await request.post("/service/sdx.query.ISjzdService/cxsjzd", { fldm });
            if (result && result.code > 0) {
                sjzd = { ...sjzd, ...result.sjzd };
            }
        }
        return Promise.resolve({ sjzd: Object.assign({}, sjzd), code: 1 });
    },
};

