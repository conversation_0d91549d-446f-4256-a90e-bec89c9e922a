import axios from 'axios'

const versionKey = 'clientVersion'

const getContextPath = () => {
  let pathname = window.location.pathname
  console.info('pathname,', pathname)
  if (pathname.indexOf('#') > -1) {
    return pathname.substring(0, pathname.indexOf('#'))
  }
  if (pathname.endsWith('/')) {
    return pathname
  } else {
    return '/' + pathname
  }
}

const isNewVersion = () => {
  let contextPath = getContextPath()
  let url = `//${
    window.location.host
  }${contextPath}version.json?t=${new Date().getTime()}`
  // console.info('version url,', url)
  axios.get(url).then(res => {
    console.info('get version')
    if (res.status === 200) {
      let vueVersion = res.data.version
      let localVueVersion = localStorage.getItem(versionKey)
      if (localVueVersion && localVueVersion != vueVersion) {
        localStorage.setItem(versionKey, vueVersion)
        // alert('version update')
        window.location.reload()
        return
      } else {
        localStorage.setItem(versionKey, vueVersion)
      }
    }
  })
}

export default {
  isNewVersion
}
