<template>
  <div class="dxsdx_zhsdx_nav innerbox2">
    <a-skeleton :loading="loading" :paragraph="{ rows: 20 }" active >
      <div class="dxsdx_khqjgk_title">业务账户</div>
      <div class="dxsdx_zhsdx_item">
        <ul>
          <li class="jzjy" v-if="ywzhJzjyData">
            <p>集中交易 {{ ywzhJzjyData?.ywzh }}</p>
            <p><span>账户状态：</span>{{ getDcNote("SDX_KHZT", ywzhJzjyData?.zhzt, dictArray) }}</p>
            <p><span>营业部：</span>{{ getDcNote("YYB", ywzhJzjyData?.yyb, dictArray) }}</p>
            <p><span>开户日期：</span>{{ ywzhJzjyData?.khrq }}</p></li>
          <li class="rzrq" v-if="ywzhRzrqData">
            <p>融资融券 {{ ywzhRzrqData?.ywzh }}</p>
            <p><span>账户状态：</span>{{ getDcNote("SDX_KHZT", ywzhRzrqData?.zhzt, dictArray) }}</p>
            <p><span>营业部：</span>{{ getDcNote("YYB", ywzhRzrqData?.yyb, dictArray) }}</p>
            <p><span>开户日期：</span>{{ ywzhRzrqData?.khrq }}</p></li>
          <li class="gpqq" v-if="ywzhGpqqData">
            <p>股票期权 {{ ywzhGpqqData?.ywzh }}</p>
            <p><span>账户状态：</span>{{ getDcNote("SDX_KHZT", ywzhGpqqData?.zhzt, dictArray) }}</p>
            <p><span>营业部：</span>{{ getDcNote("YYB", ywzhGpqqData?.yyb, dictArray) }}</p>
            <p><span>开户日期：</span>{{ ywzhGpqqData?.khrq }}</p></li>
          <li class="cwyw" v-if="ywzhCwywData">
            <p>场外业务 {{ ywzhCwywData?.ywzh }}</p>
            <p><span>账户状态：</span>{{ getDcNote("SDX_KHZT", ywzhCwywData?.zhzt, dictArray) }}</p>
            <p><span>营业部：</span>{{ getDcNote("YYB", ywzhCwywData?.yyb, dictArray) }}</p>
            <p><span>开户日期：</span>{{ ywzhCwywData?.khrq }}</p></li>
        </ul>
      </div>
      <div class="dxsdx_khqjgk_title">信用账户信息</div>
      <div class="zhsdx-content">
        <a-descriptions :bordered="true"
                        :column="3"
                        size="small"
                        :label-style="{width: '14%'}"
                        :content-style="{width: '19%', padding: '10px'}">
          <a-descriptions-item label="信用等级">{{ xyzhxxData?.djsm }}</a-descriptions-item>
          <a-descriptions-item label="评级总分">{{ xyzhxxData?.pjzf }}</a-descriptions-item>
          <a-descriptions-item label="合同有效日期">{{ xyzhxxData?.ksrq }} - {{ xyzhxxData?.jsrq }}</a-descriptions-item>
          <a-descriptions-item label="合同书号">{{ xyzhxxData?.htbh }}</a-descriptions-item>
          <a-descriptions-item label="合同状态">{{ getDcNote("XY_HTZT", xyzhxxData?.htzt, dictArray)  }}</a-descriptions-item>
          <a-descriptions-item label="授信总额度(元)">{{ xyzhxxData?.sxzed }}</a-descriptions-item>
          <a-descriptions-item label="融资授信额度">{{ xyzhxxData?.rzsxed }}</a-descriptions-item>
          <a-descriptions-item label="融券授信额度">{{ xyzhxxData?.rqsxed }}</a-descriptions-item>
          <a-descriptions-item label="利息了结方式">{{  getDcNote("XY_LXLJFS", xyzhxxData?.lxljfs, dictArray) }}</a-descriptions-item>
          <a-descriptions-item label="合同控制属性">{{ getDcNote("XY_HTSX", xyzhxxData?.htsx, dictArray) }}</a-descriptions-item>
          <a-descriptions-item label="信用专属邮箱">{{ xyzhxxData?.email }}</a-descriptions-item>
        </a-descriptions>
      </div>
      <div v-if="qqzhxxData">
        <div class="dxsdx_khqjgk_title">期权账户信息</div>
        <div class="zhsdx-content">
          <a-descriptions :bordered="true"
                          :column="3"
                          size="small"
                          :label-style="{width: '14%'}"
                          :content-style="{width: '19%', padding: '10px'}">
            <a-descriptions-item label="投资者分类">{{ qqzhxxData?.tzzfl }}</a-descriptions-item>
            <a-descriptions-item label="评级总分">{{ qqzhxxData?.pjzf }}</a-descriptions-item>
            <a-descriptions-item label="期权业务账户">{{ qqzhxxData?.ywzh }}</a-descriptions-item>
            <a-descriptions-item label="合沪市限购额度上限">{{ qqzhxxData?.shxgedsx }}</a-descriptions-item>
            <a-descriptions-item label="深市限购额度上限">{{ qqzhxxData?.szxgedsx }}</a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </a-skeleton>

  </div>
</template>
<script>
import {defineComponent, inject} from "vue";
import {commonApi} from "@/api/common";
import {getDcNote} from "../../../utils/bussinessUtils";

export default defineComponent( {
  name: "zhsdx",
  inject: ["khh"],
  props: ["dictArray"],
  data() {
    return {
      ywzhJzjyData: null,
      ywzhRzrqData: null,
      ywzhGpqqData: null,
      ywzhCwywData: null,
      xyzhxxData: null,
      qqzhxxData: null,
      loading: true,
    }
  },
  mounted() {
    this.getYwzhxx();
    this.getXyzhxx();
    if (this.ywzhGpqqData != null) {
      this.getqqzhxx();
    }
  },
  methods: {
    getDcNote,
    getYwzhxx() {
      this.loading = true;
      commonApi.executeAMS(
        "sdx.query.ICxYwzhService",
        "execute",
        { khh: this.khh},
      ).then((res) => {
        this.ywzhJzjyData = res?.jzjy;
        this.ywzhRzrqData = res?.rzrq;
        this.ywzhGpqqData = res?.gpqq;
        this.ywzhCwywData = res?.cwyw;
      }).finally(() => {
        this.loading = false;
      })
    },
    getXyzhxx() {
      this.loading = true;
      commonApi.executeAMS(
        "sdx.query.ICxXyzhService",
        "execute",
        { khh: this.khh},
      ).then((res) => {
        this.xyzhxxData = res?.xyzhxx;
      }).finally(() => {
        this.loading = false;
      })
    },
    getqqzhxx() {
      this.loading = true;
      commonApi.executeAMS(
        "sdx.query.ICxQqzhService",
        "execute",
        { khh: this.khh},
      ).then((res) => {
        this.qqzhxxData = res;
      }).finally(() => {
        this.loading = false;
      })
    }
  },
});
</script>
<style scoped>
.dxsdx_khqjgk_title{ line-height: 52px; margin: 0 10px; border-bottom: 1px solid #eee; padding-left: 8px; font-size: 14px;}
.dxsdx_khqjgk_title:before{display: inline-block; vertical-align: middle; content: ""; width: 4px; height: 18px; margin-top: -2px; border-radius: 2px; background-color: #b48a3b; margin-right: 10px;}
.dxsdx_khqjgk_title font{display: inline-block; vertical-align: middle; margin-left: 10px; font-size: 12px; color: #aaa;}
.show_ywzh,.show_cpccsdx,.show_khqxsdx,.show_cyfwzxcp{ height: 303px;}
.dxsdx_khqjgk_title span{float: right; line-height: 52px; display: inline-block; vertical-align: top; padding-right: 10px;}
.dxsdx_khqjgk_title span a.cx_a{ color: #b48a3b; cursor: pointer;}
.dxsdx_khqjgk_title span a.cx_a:before{content: "\e611"; font-family: "iconfont" !important; display: inline-block; font-size: 19px; vertical-align: top; margin-right: 6px;}
.dxsdx_khqjgk_title span a.cx_a:hover{ color: #bf935f;}
.dxsdx_zhsdx_nav{height: calc(100%); border-radius: 8px; background-color: #fff; overflow-y: auto;box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);}
.dxsdx_zhsdx_item{padding: 15px 25px;}
.dxsdx_zhsdx_item ul li{display: inline-block; vertical-align: top; width: calc(25%); padding-left: 50px; line-height: 26px; font-size: 14px; color: #333; position: relative;}
.dxsdx_zhsdx_item ul li:before{font-family: "iconfont" !important; width: 30px; height: 30px; border-radius: 4px; text-align: center; font-size: 20px; line-height: 30px; position: absolute; top: 0; left: 5px;}
.dxsdx_zhsdx_item ul li.jzjy:before{content: "\e6a3";color: #36a3ff; background-color: #eaf5ff;}
.dxsdx_zhsdx_item ul li.rzrq:before{content: "\e601";color: #96cd2a; background-color: #f1f9e2;}
.dxsdx_zhsdx_item ul li.gpqq:before{content: "\e605";color: #8b36ff; background-color: #f5edff;}
.dxsdx_zhsdx_item ul li.cwyw:before{content: "\e600";color: #35b5ff; background-color: #edf7ff;}
.dxsdx_zhsdx_item ul li p:nth-child(1){line-height: 30px; margin-bottom: 10px;}
.dxsdx_zhsdx_item ul li p span{color: #888; display: inline-block; margin-right: 4px;}

.zhsdx-content {
  padding: 10px 20px 0 20px;
}

:deep(.ant-skeleton .ant-skeleton-content) {
  padding: 20px;
}
</style>