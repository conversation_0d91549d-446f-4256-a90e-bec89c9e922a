server:
  port: 9999
  livebos-path: /livebos
spring:
  datasource:
    url: jdbc:dm://**************:5237?schema=SDX
    username: SDX
    password: sdx@@2025
    driver-class-name: dm.jdbc.driver.DmDriver
    data-type: dm
  session:
    store-type: redis
  redis:
    #host: **************
    host: **************
    port: 6379
    timeout: 5000
    password: apexsoft
  cache:
    redis:
      time-to-live: 1800000
    type: redis
application:
  name: sdx
  # 开启后，微服务访问无需登录，用postman可以直接访问
  # 开启后，短信、邮箱和企业微信不发送
  debug: false
logging:
  config: classpath:log4j2.xml
live:
  livebos:
    username: "webuser"
    password: "000000"
    namespace: "livebos-sdx-server"
    algorithm: ""
    scheme: ""
    securityCode: ""
  swagger:
    enabled: true
  actuator:
    enabled: true
  service:
    security:
      exclude:
        protocols: none
    grpc:
      mode: proto #不可随意调整， 会导致LiveGateway注册出不同结构的接口
  cache:
    l2:
      enabled: true
  job:
    executor:
      enabled: true #执行器是否启用
  otlp:
    endpoint: http://***************:5081
    authorization: "Basic ****************************************************"
    traces:
      enabled: true
    logs:
      enabled: true
    metrics:
      enabled: true
lb:
  session:
    cluster:
      enabled: true
    namespace: livebos-sdx
    appId: livebos-sdx

mybatis-plus:
  ##MYSQL数据库映射文件扫描路径
  mapper-locations: classpath*:/mapper/common/**/*.xml,classpath*:/mapper/dm/**/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    banner: false
  ##达梦数据库映射文件扫描路径
  ##mapper-locations: classpath*:/mapper/dm/**/*.xml
  ##ORACLE数据库映射文件扫描路径
  ##mapper-locations: classpath*:/mapper/orcl/**/*.xml

esb:
  ip: ***************
  port: 5007
  user: admin
  pwd: 8EDF439406910FBCB79DD3A832D4AF2D
fileFolderPath: /home/<USER>/cifmedia
