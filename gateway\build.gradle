plugins {
    id 'java'
    //springBoot插件
    id 'org.springframework.boot' version "${springBootVersion}" apply false
    //spring依赖管理插件
    id 'io.spring.dependency-management' version "${springDependencyManagement}"
    id 'java-library'
}

dependencyManagement {
    imports {
        mavenBom org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        mavenBom "com.apexsoft:live-spring-boot2-bom:${liveVersion}"
    }
    resolutionStrategy{
        cacheChangingModulesFor 0, 'seconds'
    }
}
compileJava {
    options.compilerArgs << '-parameters'
}
compileTestJava{
    options.compilerArgs << '-parameters'
}
group = 'com.apexsoft'
version = '1.0.0'
//依赖
dependencies {
    //基础
    implementation "com.apexsoft:live-spring-boot-starter:${liveVersion}"
    implementation "com.apexsoft:live-service-grpc-exporter-starter:${liveVersion}"
    implementation  "com.apexsoft:live-spring-boot-actuator-starter:${liveVersion}"
    //注意！需要将业务服务的springboot版本升级到2.4.13以上，springcloud版本配套升级到 2020.0.5以上
    implementation "com.apexsoft:live-livebos-session-shared-starter:${liveVersion}"
    // 平台包
    //implementation group: 'com.apex.livebos', name: 'formbuilder', version: '6.0.0-SNAPSHOT'
    //log4j2 异步日志
    implementation "org.springframework.boot:spring-boot-starter-log4j2"

    compileOnly 'org.projectlombok:lombok:1.18.24'
    annotationProcessor 'org.projectlombok:lombok:1.18.24'
    implementation 'com.belerweb:pinyin4j:2.5.1'
    api group: 'com.alibaba', name: 'fastjson', version: '1.2.76'

    implementation fileTree(dir: '../libs', includes: ['*.jar'])
    implementation(project(":core"))
}
//单元测试
test {
    useJUnitPlatform()
}

//依赖缓存时间
configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}
configurations {
    all*.exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    all*.exclude group:"org.slf4j",module:"slf4j-log4j12"
}

//依赖缓存时间
configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}
configurations {
    all*.exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    all*.exclude group:"org.slf4j",module:"slf4j-log4j12"
}

jar {
    baseName 'sdx-gateway'
    version ''
}