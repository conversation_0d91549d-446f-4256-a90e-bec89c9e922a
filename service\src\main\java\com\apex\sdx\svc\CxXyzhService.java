package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.KhhReq;
import com.apex.sdx.api.resp.query.CxxyzhRes;
import com.apex.sdx.api.vo.query.XyzhVo;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhxyxxService;
import com.apex.sdx.core.mybatis.service.VxtdmService;
import com.apexsoft.LiveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.query.ICxXyzhService;

/**
 * <AUTHOR>
 * @Date 2025/2/7 15:01
 * @Description: TODO
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "查询信用账户信息")
public class CxXyzhService implements ICxXyzhService {

    @Autowired
    private TOdsYgtCifTkhxyxxService tOdsYgtCifTkhxyxxService;
    @Override
    public CxxyzhRes check(KhhReq req) {
        Assert.notNull(req, KhhReq::getKhh);
        return null;
    }

    @Override
    public CxxyzhRes execute(KhhReq req) {
        XyzhVo zyzhxx = tOdsYgtCifTkhxyxxService.getXyzhxx(req.getKhh());
        return CxxyzhRes.builder().xyzhxx(zyzhxx).build();

    }
}
