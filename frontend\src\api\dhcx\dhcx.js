import request from '@/utils/request'
export default {
    // 查询客户基础信息分页
    cxkhjbxxByKhh(khh) {
        return request({
            url: `/service/sdx.zhgl.IDhcxService/cxkhjbxx`,
            method: 'post',
            data: {khh: khh}
        })
    },
    // 查询客户详细信息分页
    cxkhxxxx(khh) {
        return request({
            url: `/service/sdx.zhgl.IDhcxService/cxkhxxxx`,
            method: 'post',
            data: {khh: khh}
        })
    }
}
