package com.apex.sdx.api.vo.query;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-03-05
 * @Description:
 */
@Setter
@Getter
public class CpccsdxVo {

    @LiveProperty(note = "日期", index = 1)
    private Integer rq;

    @LiveProperty(note = "客户号", index = 2)
    private String khh;

    @LiveProperty(note = "产品代码", index = 3)
    private String cpdm;

    @LiveProperty(note = "数据类型", index = 4)
    private Integer sjly;

    @LiveProperty(note = "客户风险等级", index = 5)
    private Long khfxdj;

    @LiveProperty(note = "客户投资品种", index = 6)
    private String khtzpz;

    @LiveProperty(note = "客户投资期限", index = 7)
    private Long khtzqx;

    @LiveProperty(note = "客户预期收益", index = 8)
    private String khyqsy;

    @LiveProperty(note = "产品风险等级", index = 9)
    private Long cpfxdj;

    @LiveProperty(note = "产品投资品种", index = 10)
    private Long cptzpz;

    @LiveProperty(note = "产品投资期限", index = 11)
    private Long cptzqx;

    @LiveProperty(note = "产品预期收益", index = 12)
    private Long cpyqsy;

    @LiveProperty(note = "风险等级适当性", index = 13)
    private Long fxdjsdx;

    @LiveProperty(note = "投资品种适当性", index = 14)
    private Long tzpzsdx;

    @LiveProperty(note = "投资期限适当性", index = 15)
    private Long tzqxsdx;

    @LiveProperty(note = "预期收益适当性", index = 16)
    private Long yqsysdx;

    @LiveProperty(note = "适当性结果", index = 17)
    private Long sdxjg;

    @LiveProperty(note = "产品名称", index = 18)
    private String cpmc;

    @LiveProperty(note = "开仓日期", index = 19)
    private String kcrq;

    /**
     * TA代码
     */
    @LiveProperty(note = "TA代码", index = 20)
    private String tadm;

    /**
     * TA名称
     */
    @LiveProperty(note = "TA名称", index = 21)
    private String tamc;

    /**
     * 基金账号
     */
    @LiveProperty(note = "基金账号", index = 22)
    private String jjzh;

    /**
     * 交易账号
     */
    @LiveProperty(note = "交易账号", index = 23)
    private String jyzh;

    /**
     * 产品状态
     */
    @LiveProperty(note = "产品状态", index = 24)
    private Long cpzt;
}
