package com.apex.sdx.api.vo.query;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-03-04
 * @Description:
 */
@Setter
@Getter
public class SdxsjDescVo {

    @LiveProperty(note = "数据类型", index = 1)
    private Integer sjlx;

    @LiveProperty(note = "要素代码", index = 2)
    private String ysdm;

    @LiveProperty(note = "要素名称", index = 3)
    private String ysmc;

    @LiveProperty(note = "父要素代码", index = 4)
    private String fysdm;

    @LiveProperty(note = "备注说明", index = 5)
    private String bzsm;

    @LiveProperty(note = "数据详情", index = 6)
    private String sjxq;

}
