<template>
  <div class="dxsdx_khqjgk_nav">
    <div class="dxsdx_khqjgk_title">交易权限
      <span><a class="cx_a" @click="open = true">交易权限明细</a></span>
      <jyqxmx-modal
          v-model:open="open"
          :dictArray="dictArray"
          title="交易权限详情"
      />
    </div>
    <div class="qxsdx-content">
      <a-table
          :columns="columns"
          :data-source="data"
          :scroll="{ x: 1200 }"
          :expand-column-width="50"
          :row-key="record => record.khh + record.ywzh + record.gdh + record.jyqx"
          :loading="loading1"
          :pagination="pagination1"
          @change="pageChange1"
      >
        <template #expandedRowRender="{ record }">
          <template v-if="record.lsrq && record.sdxjg && record.zy">
            <div class="dxsdx_qxsdx_qxsjz">
              <div class="qxsjz_item">
                <div class="sj_sjz innerbox2">
                  <ul>
                    <li v-for="(item,index) in record.lsrq.split(';')" :key="index">
                      <p class="sj_jd"></p>
                      <p>{{ formatDate(item) }}</p>
                      <div
                          :class="{'pp_sj':record.sdxjg.split(';')[index] == '1','bsd_sj':record.sdxjg.split(';')[index] == '0'}">
                        <p>{{ record.zy.split(';')[index] }}</p>
                        <p><em></em>{{ getDcNote("SDX_PPJG", record.sdxjg.split(';')[index], this.dictArray) }}</p>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </template>
        </template>
        <template #expandColumnTitle>
          <span></span>
        </template>
      </a-table>
    </div>
    <div class="dxsdx_khqjgk_title">
      权限适当性
      <span>
        <a-switch v-model:checked="checked" style="margin: 0 10px 0 20px" size="small"/>
        <span style="font-weight: 400;font-size: 12px;color: #333333; line-height: 55px">仅看不适当</span>
      </span>
    </div>
    <div class="qxsdx-content">
      <a-table
          :columns="columns2"
          :data-source="data2"
          :scroll="{ x: 2000 }"
          :loading="loading2"
          :pagination="pagination2"
          @change="pageChange2"
      ></a-table>
    </div>
  </div>
</template>
<script>
import {defineComponent} from "vue";
import JyqxmxModal from "@views/dhcx/qxsdx/jyqxmxModal.vue";
import {commonApi} from "@/api/common";
import {message} from "ant-design-vue";
import {formatDate, getDcNote} from "@utils/bussinessUtils";


export default defineComponent({
  name: "qxsdx",
  components: {JyqxmxModal},
  inject: ["khh"],
  props: ["dictArray"],
  data() {
    return {
      columns: [
        {title: '客户号', dataIndex: 'khh', key: 'khh', width: 200, fixed: true},
        {title: '业务系统', dataIndex: 'ywxtmc', key: 'ywxtmc', width: 150},
        {title: '业务账号', dataIndex: 'ywzh', key: 'ywzh', width: 150},
        {
          title: '交易所', dataIndex: 'jys', key: 'jys', width: 150,
          customRender: ({text}) => {
            return getDcNote("SDX_JYS", text, this.dictArray);
          }
        },
        {title: '股东号', dataIndex: 'gdh', key: 'gdh', width: 150},
        {
          title: '交易权限', dataIndex: 'jyqx', key: 'jyqx', width: 150,
          customRender: ({text}) => {
            return getDcNote("SDX_CXYWLB", text, this.dictArray);
          }
        },
      ],
      data: [],
      columns2: [
        {
          title: "创新业务类别", dataIndex: "cxywlb", key: "cxywlb",
          customRender: ({text}) => {
            return getDcNote("SDX_CXYWLB", text, this.dictArray);
          },
          fixed: 'left',
        },
        {
          title: "业务风险等级", dataIndex: "ywfxdj", key: "ywfxdj",
          customRender: ({text}) => {
            return getDcNote("SDX_YWFXDJ", text, this.dictArray)
          }
        },
        {
          title: "业务投资品种", dataIndex: "ywtzpz", key: "ywtzpz",
          customRender: ({text}) => {
            return getDcNote("SDX_TZPZ", text, this.dictArray)
          }
        },
        {
          title: "业务投资期限", dataIndex: "ywtzqx", key: "ywtzqx",
          customRender: ({text}) => {
            return getDcNote("SDX_TZQX", text, this.dictArray)
          }
        },
        {
          title: "业务预期收益", dataIndex: "ywyqsy", key: "ywyqsy",
          customRender: ({text}) => {
            return getDcNote("SDX_YQSY", text, this.dictArray)
          }
        },
        {
          title: "风险等级适当性", dataIndex: "fxdjsdx", key: "fxdjsdx",
          customRender: ({text}) => {
            if (text == 0) {
              return "不匹配";
            } else if (text == 1) {
              return "匹配"
            }
          }
        },
        {
          title: "投资品种适当性", dataIndex: "tzpzsdx", key: "tzpzsdx",
          customRender: ({text}) => {
            if (text == 0) {
              return "不匹配";
            } else if (text == 1) {
              return "匹配"
            }
          }
        },
        {
          title: "投资期限适当性", dataIndex: "tzqxsdx", key: "tzqxsdx",
          customRender: ({text}) => {
            if (text == 0) {
              return "不匹配";
            } else if (text == 1) {
              return "匹配"
            }
          }
        },

        {
          title: "预期收益适当性", dataIndex: "yqsysdx", key: "yqsysdx",
          customRender: ({text}) => {
            if (text == 0) {
              return "不匹配";
            } else if (text == 1) {
              return "匹配"
            }
          }
        },
        {
          title: "适当性结果", dataIndex: "sdxjg", key: "sdxjg",
          customRender: ({text}) => {
            return getDcNote("SDX_PPJG", text, this.dictArray);
          },
        },
      ],
      data2: [],
      checked: false,
      open: false,
      loading1: false,
      loading2: false,
      current1: 1,
      pageSize1: 10,
      total1: 0,
      current2: 1,
      pageSize2: 10,
      total2: 0,
    }
  },
  mounted() {
    this.getKhjyqxList();
    this.getYktywsdx();
  },
  computed: {
    pagination1() {
      return {
        total: this.total1,
        current: this.current1,
        pageSize: this.pageSize1,
        //showQuickJumper: true,
        showSizeChanger: true,
        showTotal(total) {
          return "共 " + total + " 条";
        },
      };
    },
    pagination2() {
      return {
        total: this.total2,
        current: this.current2,
        pageSize: this.pageSize2,
        //showQuickJumper: true,
        showSizeChanger: true,
        showTotal(total) {
          return "共 " + total + " 条";
        },
      };
    }
  },
  watch: {
    checked(newVal) {
      this.getYktywsdx();
    }
  },
  methods: {
    getDcNote,
    formatDate,
    pageChange1(page) {
      this.current1 = page.current;
      this.pageSize1 = page.pageSize;
      this.getKhjyqxList();
    },
    pageChange2(page) {
      this.current2 = page.current;
      this.pageSize2 = page.pageSize;
      this.getYktywsdx();
    },
    getKhjyqxList() {
      this.loading1 = true;
      commonApi.executeAMS(
          "sdx.query.IKhjyqxService",
          "khjyqxcx",
          {khh: this.khh, zt: 0, isSearchCount: true, pagenum: this.current1, pagesize: this.pageSize1},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.data = res.records || [];
        this.total1 = res.total;
      }).finally(() => {
        this.loading1 = false;
      })
    },
    getYktywsdx() {
      this.loading2 = true;
      commonApi.executeAMS(
          "sdx.query.IYktywsdxService",
          "yktywsdxcx",
          {khh: this.khh, onlysdx: this.checked, isSearchCount: true, pagenum: this.current2, pagesize: this.pageSize2},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.data2 = res.records || [];
        this.total2 = res.total;
      }).finally(() => {
        this.loading2 = false;
      })
    }
  },
});
</script>
<style scoped>
.dxsdx_khqjgk_nav {
  background-color: #fff;
  border-radius: 8px;
  height: 100%;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto
}

.dxsdx_khqjgk_title {
  line-height: 52px;
  margin: 0 10px;
  border-bottom: 1px solid #eee;
  padding-left: 8px;
  font-size: 14px;
}

.dxsdx_khqjgk_title:before {
  display: inline-block;
  vertical-align: middle;
  content: "";
  width: 4px;
  height: 18px;
  margin-top: -2px;
  border-radius: 2px;
  background-color: #b48a3b;
  margin-right: 10px;
}

.dxsdx_khqjgk_title font {
  display: inline-block;
  vertical-align: middle;
  margin-left: 10px;
  font-size: 12px;
  color: #aaa;
}

.show_ywzh, .show_cpccsdx, .show_khqxsdx, .show_cyfwzxcp {
  height: 303px;
}

.dxsdx_khqjgk_title span {
  float: right;
  line-height: 52px;
  display: inline-block;
  vertical-align: top;
  padding-right: 10px;
}

.dxsdx_khqjgk_title span a.cx_a {
  color: #b48a3b;
  cursor: pointer;
}

.dxsdx_khqjgk_title span a.cx_a:before {
  content: "\e611";
  font-family: "iconfont" !important;
  display: inline-block;
  font-size: 19px;
  vertical-align: top;
  margin-right: 6px;
}

.dxsdx_khqjgk_title span a.cx_a:hover {
  color: #bf935f;
}

.dxsdx_qxsdx_qxsjz {
  overflow-x: auto
}

.qxsjz_item {
}

.sj_sjz {
  background-color: #fafafa;
  min-height: 130px;
  white-space: nowrap
}

.sj_sjz ul li {
  width: 180px;
  display: inline-block;
  vertical-align: top;
  margin: 5px 0 0 0;
  color: #333;
  line-height: 30px;
  font-size: 14px;
  color: #333;
}

.sj_sjz ul li p:nth-child(1).sj_jd {
  position: relative;
  border-top: 1px dotted #e6e6e6;
  margin-top: 8px;
  left: 20px;
  width: calc(100% - 28px);
  margin-bottom: 20px;
}

.sj_sjz ul li p:nth-child(1).sj_jd:before {
  content: "";
  display: inline-block;
  position: absolute;
  left: -20px;
  top: -6px;
  width: 11px;
  height: 11px;
  border-radius: 5.5px;
  box-sizing: border-box;
  border: 2px solid #ecc9a4;
}

.sj_sjz ul li p:nth-child(2) {
  color: #888;
}

.sj_sjz ul li div p:nth-child(1) {
  line-height: 20px;
}

.sj_sjz ul li p em {
  display: inline-block;
  font-style: normal;
}

.sj_sjz ul li .pp_sj p:nth-child(2) {
  color: #00b42a;
  font-size: 14px;
}

.sj_sjz ul li .pp_sj p:nth-child(1) {
  white-space: break-spaces
}
.sj_sjz ul li .bsd_sj p:nth-child(1) {
  white-space: break-spaces
}

.sj_sjz ul li .pp_sj p em:before {
  content: "\e631";
  font-size: 16px;
  height: 16px;
  line-height: 16px;
  border-radius: 10px;
  background-color: #f8fffa;
  font-family: "iconfont" !important;
  display: inline-block;
  vertical-align: middle;
  margin-right: 4px;
}

.sj_sjz ul li .bsd_sj p:nth-child(2) {
  color: #ff1919;
  font-size: 14px;
}

.sj_sjz ul li .bsd_sj p em:before {
  content: "\e634";
  font-size: 16px;
  height: 16px;
  line-height: 16px;
  border-radius: 10px;
  background-color: #fff7f8;
  font-family: "iconfont" !important;
  display: inline-block;
  vertical-align: middle;
  margin-right: 4px;
}

.sj_sjz ul li:last-child p:nth-child(1).sj_jd {
  border-top: none;
}

.qxsdx-content {
  padding: 10px 10px 0 10px;
}

:deep(.ant-switch.ant-switch-checked) {
  background: #bf935f
}

:deep(.ant-switch.ant-switch-checked:hover:not(.ant-switch-disabled)) {
  background: #bf935f
}
</style>