package com.apex.sdx.core.logger;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2024/1/4 16:50
 */
@Slf4j
public class PointerLogger {

    public static void log(String account, String msg, long cost) {
        log.debug(account + msg + String.format("%-" + (100 - msg.length() - account.length()) + "s耗时:", " ") + cost + "ms");
    }

    public static void log(String msg, long cost) {
        log.debug(msg + String.format("%-" + (100 - msg.length()) + "s耗时:", " ") + cost + "ms");
    }
}
