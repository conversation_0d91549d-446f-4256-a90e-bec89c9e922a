package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.KhfxjslscxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.vo.query.KhfxjslsVo;
import com.apex.sdx.convert.KhfxjslsVoMapping;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.entity.Tkhfxjsls;
import com.apex.sdx.core.mybatis.service.TkhfxjslsService;
import com.apexsoft.LiveService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.query.IKhfxjslsService;

/**
 * <AUTHOR>
 * @Date 2025-03-05
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "客户风险警示流水查询服务")
public class KhfxjslsService implements IKhfxjslsService {

    @Autowired
    TkhfxjslsService tkhfxjslsService;

    @Autowired
    KhfxjslsVoMapping khfxjslsVoMapping;

    @Override
    public QueryPageResponse<KhfxjslsVo> khfxjslscx(KhfxjslscxReq req) throws Exception {
        Assert.notNull(req, KhfxjslscxReq::getKhh);
        QueryPageResponse<KhfxjslsVo> result = new QueryPageResponse<>(1, "查询成功");

        String khh = req.getKhh();
        Integer jsrq = req.getJsrq();
        Integer ksrq = req.getKsrq();
        String jsnr = req.getJsnr();
        Page<Tkhfxjsls> page = tkhfxjslsService.queryByCondition(khh, ksrq, jsrq, jsnr, req.isSearchCount(), req.getPagenum(), req.getPagesize());

        Page<KhfxjslsVo> khfxjslsVoPage = khfxjslsVoMapping.pageConver(page);

        result.page(khfxjslsVoPage);

        return result;
    }
}
