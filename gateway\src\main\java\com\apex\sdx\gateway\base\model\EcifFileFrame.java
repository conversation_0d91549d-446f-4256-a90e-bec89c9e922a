package com.apex.sdx.gateway.base.model;

import com.apexsoft.FileFrame;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class EcifFileFrame extends FileFrame {
    /**
     * 发起人
     */
    @LiveProperty(note = "发起人", index = 11)
    private String czgydm;

    public EcifFileFrame() {
        super();
    }

    public EcifFileFrame(String fileName, long fileLength, String contentType, String fqr) {
        this(fileName, fileLength, contentType);
        this.setCzgydm(fqr);
    }

    public EcifFileFrame(String fileName, long fileLength, String contentType) {
        super(fileName, fileLength, contentType);
    }

    public EcifFileFrame(byte[] bytes, int length) {
        super(bytes, length);
    }

    public EcifFileFrame(byte[] bytes) {
        this(bytes, bytes.length);
    }
}
