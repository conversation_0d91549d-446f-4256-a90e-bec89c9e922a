package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.core.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjWjcs;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTpjWjcsService;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTpjWjcsMapper;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class TOdsYgtCifTpjWjcsServiceImpl extends ServiceImpl<TOdsYgtCifTpjWjcsMapper, TOdsYgtCifTpjWjcs>
    implements TOdsYgtCifTpjWjcsService{

    @Override
    public TOdsYgtCifTpjWjcs getWjcsByWjid(Integer khdcwjid) {
        try {
            return this.baseMapper.selectWjcsByWjid(khdcwjid);
        } catch (Exception e) {
            String note = String.format("查询问卷参数异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }

    }
}




