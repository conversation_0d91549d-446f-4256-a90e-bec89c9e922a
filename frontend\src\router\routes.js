import Home from '@/views/Home'

export default [
    {
        path: '/',
        name: 'Home',
        component: Home
    },
    {
        path: '/login',
        name: 'login',
        component: () => import('@/views/login')
    },
    {
        path: '/czmx',
        name: 'czmx',
        component: () => import('@/views/sdxgl/query/czmx')
    },
    {
        path: '/xtgl/fwhcsx',
        name: 'fwhcsx',
        component: () => import('@/views/xtgl/fwhcsx')
    },
    {
        path: '/sdxgl/sdxConfig',
        name: 'sdxConfig',
        component: () => import('@/views/sdxgl/sdxConfig/sdxConfig')
    },
    {
        path: '/gzxx',
        name: 'gzxx',
        component: () => import('@/views/sdxgl/query/gzxx')
    },
    {
        path: '/dhcx',
        name: 'dhcx',
        redirect: '/dhcx/index',
        children: [
            {
                path: '/dhcx/index',
                name: 'dhcx_index',
                component: () => import('@/views/dhcx/index')
            },
            {
                path: '/dhcx/main/:khh',
                name: 'dhcx_main',
                component: () => import('@/views/dhcx/main')
            },
            {
                path: '/dhcx/sdxsj/sjxq',
                name: 'dhcx_main',
                component: () => import('@/views/dhcx/sdxsj/sjxq')
            },
        ]
    },
    {
        path: '/khqj',
        name: 'khqj',
        redirect: '/khqj/khsdxgk',
        component: () => import('@/views/dhcx/main'),
        children: [
            {
                path: 'khsdxgk',
                name: 'khsdxgk',
                component: () => import('@/views/dhcx/khsdxgk/index')
            },
            {
                path: 'zhsdx',
                name: 'zhsdx',
                component: () => import('@/views/dhcx/zhsdx/index')
            },
            {
                path: 'qxsdx',
                name: 'qxsdx',
                component: () => import('@/views/dhcx/qxsdx/index')
            },
            {
                path: 'cpsdx',
                name: 'cpsdx',
                component: () => import('@/views/dhcx/cpsdx/index')
            },
            {
                path: 'sdxsj',
                name: 'sdxsj',
                component: () => import('@/views/dhcx/sdxsj/index')
            },
            {
                path: 'sdxlh',
                name: 'sdxlh',
                component: () => import('@/views/dhcx/sdxlh/index')
            },
            {
                path: 'khzmcl',
                name: 'khzmcl',
                component: () => import('@/views/dhcx/khzmcl/index')
            },
        ]
    },
    {
        path: '/index/index',
        name: 'index',
        component: () => import('@/views/index/index')
    },
]
