package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.core.mybatis.entity.Tkhfxjsls;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *
 */
public interface TkhfxjslsService extends IService<Tkhfxjsls> {

    /**
     *
     * @param khh
     * @param ksrq
     * @param jsrq
     * @param jsnr
     * @param searchCount
     * @param pagenum
     * @param pagesize
     * @return
     */
    Page<Tkhfxjsls> queryByCondition(String khh, Integer ksrq, Integer jsrq, String jsnr, boolean searchCount, int pagenum, int pagesize);
}
