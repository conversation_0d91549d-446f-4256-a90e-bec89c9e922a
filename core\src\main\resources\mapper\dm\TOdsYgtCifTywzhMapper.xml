<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTywzhMapper">

    <resultMap id="BaseResultMap" type="com.apex.sdx.core.mybatis.entity.TOdsYgtCifTywzh">
            <id property="ywxt" column="YWXT" jdbcType="DECIMAL"/>
            <id property="ywzh" column="YWZH" jdbcType="VARCHAR"/>
            <result property="khh" column="KHH" jdbcType="VARCHAR"/>
            <result property="glywxt" column="GLYWXT" jdbcType="DECIMAL"/>
            <result property="glywzh" column="GLYWZH" jdbcType="VARCHAR"/>
            <result property="yyb" column="YYB" jdbcType="DECIMAL"/>
            <result property="khqz" column="KHQZ" jdbcType="DECIMAL"/>
            <result property="zffs" column="ZFFS" jdbcType="VARCHAR"/>
            <result property="zhzt" column="ZHZT" jdbcType="DECIMAL"/>
            <result property="khrq" column="KHRQ" jdbcType="DECIMAL"/>
            <result property="xhrq" column="XHRQ" jdbcType="DECIMAL"/>
            <result property="zjzh" column="ZJZH" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        YWXT,YWZH,KHH,
        GLYWXT,GLYWZH,YYB,
        KHQZ,ZFFS,ZHZT,
        KHRQ,XHRQ,ZJZH
    </sql>
</mapper>
