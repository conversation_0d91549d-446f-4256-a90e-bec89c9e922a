package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.CpbsdxyqslscxReq;
import com.apex.sdx.api.req.query.QxbsdxyqslscxReq;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.query.CpbsdxyqslscxVo;
import com.apex.sdx.api.vo.query.QxbsdxyqslscxVo;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhbsdxyqsCpService;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhbsdxyqsService;
import com.apexsoft.LiveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import sdx.query.ICxbsdxyqslsService;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/6 16:55
 * @Description: TODO
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "不适当协议签署流水查询服务")
@RequiredArgsConstructor
public class CxbsdxyqslsService implements ICxbsdxyqslsService {

    private final TOdsYgtCifTkhbsdxyqsService todsYgtCifTkhbsdxyqsService;
    private final TOdsYgtCifTkhbsdxyqsCpService todsYgtCifTkhbsdxyqsCpService;

    @Override
    public QueryResponse<QxbsdxyqslscxVo> qxbsdxyqslscx(QxbsdxyqslscxReq req) throws Exception {

        QueryResponse<QxbsdxyqslscxVo> result = new QueryResponse<>(1, "查询成功");
        String ksrq = req.getKsrq();
        String jsrq = req.getJsrq();
        String cxywlb = req.getCxywlb();
        String khh = req.getKhh();

        List<QxbsdxyqslscxVo> list = todsYgtCifTkhbsdxyqsService.qxbsdxyqslscx(khh,ksrq, jsrq, cxywlb);

        result.setRecords(list);
        return result;
    }

    @Override
    public QueryResponse<CpbsdxyqslscxVo> cpbsdxyqslscx(CpbsdxyqslscxReq req) throws Exception {
        QueryResponse<CpbsdxyqslscxVo> result = new QueryResponse<>(1, "查询成功");
        String ksrq = req.getKsrq();
        String jsrq = req.getJsrq();
        String cpdm = req.getCpdm();
        String khh = req.getKhh();
        List<CpbsdxyqslscxVo> list = todsYgtCifTkhbsdxyqsCpService.cpbsdxyqslscx(khh,ksrq, jsrq, cpdm);
        result.setRecords(list);
        return result;
    }
}
