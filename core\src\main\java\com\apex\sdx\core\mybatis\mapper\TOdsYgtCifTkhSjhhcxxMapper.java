package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.api.vo.khgl.SjhcxxVo;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhSjhhcxx;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @Entity com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhSjhhcxx
 */
public interface TOdsYgtCifTkhSjhhcxxMapper extends BaseMapper<TOdsYgtCifTkhSjhhcxx> {

    @Select({"<script> " +
            "select b.yyb, b.khmc, b.zjlb, b.zjbh, a.* " +
            "from ods.t_ods_ygt_cif_tkh_sjhhcxx a " +
            "left join sdx.tkhxx b on a.khh = b.khh " +
            "where b.khh = #{khh} " +
            "   <if test='ksrq != null'>" +
            "       and a.hcrq &gt;= #{ksrq}" +
            "   </if>" +
            "   <if test='jsrq != null'>" +
            "       and a.hcrq &lt;= #{jsrq}" +
            "   </if>" +
            "   <if test='hcjg != null'>" +
            "       and a.hcjg= #{hcjg}" +
            "   </if>" +
            "</script>"})
    Page<SjhcxxVo> selectSjhhcxx(Page<TOdsYgtCifTkhSjhhcxx> page, @Param("khh") String khh, @Param("ksrq") Integer ksrq, @Param("jsrq") Integer jsrq, @Param("hcjg") Integer hcjg);
}




