<template>
  <modal-component
    :title="title"
    :open="isOpen"
    :height="600"
    width="1050px"
    :on-cancel="handleOk"
  >
    <template #content>
      <div class="modal-container">
        <div class="cljg-item">
          <span>权限范围：</span>
          <a-select
            style="width: 200px"
            :options="cljgOptions"
            v-model:value="cljg"
            placeholder="请选择处理结果"
          />
          <div class="sdxsj-content">
            <a-table :columns="columns" :data-source="data" :pagination="pagination" :loading="loading" @change="pageChange">
            </a-table>
          </div>
        </div>
      </div>
    </template>
  </modal-component>
</template>
<script>
import {defineComponent, h} from "vue";
import ModalComponent from "@components/ModalComponent.vue";
import {commonApi} from "@/api/common";
import {message} from "ant-design-vue";
import {getDcNote} from "@utils/bussinessUtils";


export default defineComponent({
  name: "jyqxmxModal",
  components: { ModalComponent },
  inject: ['khh'],
  props: {
    open: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      required: true
    },
    dictArray: {
      type: Object,
    }
  },
  data() {
    return {
      isOpen: this.open,
      cljgOptions: [{label: '全部', value: 1}],
      cljg: 1,
      current: 1,
      pageSize: 10,
      total: 0,
      columns: [
        { title: '交易所', dataIndex: 'jys', key: 'jys',
          customRender: ({text}) => {
            return getDcNote("SDX_JYS", text, this.dictArray)
          }
        },
        { title: '股东号', dataIndex: 'gdh', key: 'gdh', },
        { title: '交易权限', dataIndex: 'jyqx', key: 'jyqx',
          customRender: ({text}) => {
            return getDcNote("SDX_CXYWLB", text, this.dictArray)
          }
        },
        { title: '权限状态', dataIndex: 'zt',  key: 'zt',
          customRender: ({text, record}) => {
            if (text === 0) {
              return h('span', {
                style: {
                  color: '#00B42A',
                  fontSize: '14px'
                }
              }, '正常')
            } else {
              return h('span', {
                style: {
                  color: '#FF1919',
                  fontSize: '14px'
                }
              }, '关闭')
            }
          }
        },
        { title: '开通日期', dataIndex: 'ktrq',  key: 'ktrq', },
        { title: '关闭日期', dataIndex: 'gbrq',  key: 'gbrq', },
      ],
      data: [],
      loading: false,
    }
  },
  computed: {
    pagination() {
      return {
        total: this.total,
        current: this.current,
        pageSize: this.pageSize,
        //showQuickJumper: true,
        showSizeChanger: true,
        showTotal(total) {
          return "共 " + total + " 条";
        },
      };
    },
  },
  watch: {
    open(newVal) {
      this.isOpen = newVal;
      this.getJyqxmx();
    }
  },
  mounted() {

  },
  methods: {
    pageChange(page) {
      this.current = page.current;
      this.pageSize = page.pageSize;
      this.getJyqxmx();
    },
    handleOk(){
      this.isOpen = false;
      this.$emit("update:open", false);
    },
    getJyqxmx() {
      this.loading = true;
      commonApi.executeAMS(
        "sdx.query.IKhjyqxService",
        "khjyqxcx",
        { khh: this.khh,isSearchCount: true, pagenum: this.current, pagesize: this.pageSize },
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.data = res.records || [];
        this.total = res.total;
      }).finally(() => {
        this.loading = false;
      })
    },
  }
});
</script>
<style scoped>
.modal-container {
  padding: 10px 10px 10px 20px;
  background-color: #FFFFFF;
  height: 100%;
  border-radius: 4px;
  line-height: 30px;
  color: #888888;
  overflow-y: auto;
}

.cljg-item {
  line-height: 28px;
}

.sdxsj-content {
  padding-top: 10px;
}
</style>