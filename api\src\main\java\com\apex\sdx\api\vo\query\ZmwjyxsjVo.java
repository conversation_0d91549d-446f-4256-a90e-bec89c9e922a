package com.apex.sdx.api.vo.query;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-03-10
 * @Description:
 */
@Getter
@Setter
public class ZmwjyxsjVo {

    @LiveProperty(note = "id", index = 1)
    private Long id;

    @LiveProperty(note = "客户号", index = 2)
    private String khh;

    /**
     * 证明文件ID;TZMWJ_YXLXDY.ID
     */
    @LiveProperty(note = "证明文件id", index = 3)
    private Long zmwjid;

    /**
     * 影像文件
     */
    @LiveProperty(note = "影像文件filepath", index = 4)
    private String filepath;

    @LiveProperty(note = "证明文件", index = 5)
    private String zmwj;
}
