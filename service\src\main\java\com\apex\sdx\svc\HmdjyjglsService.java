package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.HmdjyjglscxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.vo.query.HmdjyjglsVo;
import com.apex.sdx.convert.HmdjyjglsVoMapping;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.entity.Thmdjyjgls;
import com.apex.sdx.core.mybatis.service.ThmdjyjglsService;
import com.apexsoft.LiveService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.query.IHmdjyjglsService;

/**
 * <AUTHOR>
 * @Date 2025-03-06
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "黑名单校验结果流水查询服务")
public class HmdjyjglsService implements IHmdjyjglsService {

    @Autowired
    ThmdjyjglsService thmdjyjglsService;

    @Autowired
    HmdjyjglsVoMapping hmdjyjglsVoMapping;

    @Override
    public QueryPageResponse<HmdjyjglsVo> hmdjyjglscx(HmdjyjglscxReq req) throws Exception {
        Assert.notNull(req, HmdjyjglscxReq::getKhh);
        QueryPageResponse<HmdjyjglsVo> result = new QueryPageResponse<>(1, "查询成功");

        String khh = req.getKhh();
        Integer ksrq = req.getKsrq();
        Integer jsrq = req.getJsrq();
        Integer jymdlx = req.getJymdlx();
        Integer jydx = req.getJydx();
        Integer jymdjg = req.getJymdjg();

        Page<Thmdjyjgls> page = thmdjyjglsService.queryByCconditions(khh, ksrq, jsrq, jymdlx, jydx, jymdjg, req.isSearchCount(), req.getPagenum(), req.getPagesize());

        Page<HmdjyjglsVo> hmdjyjglsVoPage = hmdjyjglsVoMapping.pageConver(page);

        result.page(hmdjyjglsVoPage);

        return result;
    }
}
