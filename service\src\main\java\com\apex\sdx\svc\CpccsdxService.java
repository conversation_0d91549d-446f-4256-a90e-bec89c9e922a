package com.apex.sdx.svc;

import com.apex.sdx.api.req.compute.QtcccptjReq;
import com.apex.sdx.api.req.query.CpccsdxcxReq;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.compute.QtcccptjVo;
import com.apex.sdx.api.vo.query.CpccsdxVo;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.service.TsdxjgQtcccpService;
import com.apexsoft.LiveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import sdx.query.ICpccsdxService;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-03-05
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "产品持仓适当性查询服务")
@RequiredArgsConstructor
public class CpccsdxService implements ICpccsdxService {

    private final TsdxjgQtcccpService qtcccpService;

    @Override
    public QueryResponse<CpccsdxVo> cpccsdxcx(CpccsdxcxReq req) throws Exception {
        Assert.notNull(req,  CpccsdxcxReq::getKhh, CpccsdxcxReq::getSjlx);
        QueryResponse<CpccsdxVo> result = new QueryResponse<>(1, "查询成功");
        String khh = req.getKhh();
        String rq = req.getRq();
        Integer sjlx = req.getSjlx();
        boolean onlysdx = req.isOnlysdx();

        List<CpccsdxVo> list = qtcccpService.selectByKhhSjlxAndSdxjg(khh, sjlx, onlysdx, rq);

        result.setRecords(list);
        return result;
    }

    @Override
    public QueryResponse<QtcccptjVo> qtcccpSdxtj(QtcccptjReq req) throws Exception {
        QueryResponse<QtcccptjVo> result = new QueryResponse<>(1, "查询成功");
        String khh = req.getKhh();
        Integer ywxt = req.getYwxt();
        String rq = req.getRq();

        List<QtcccptjVo> list = qtcccpService.compute(khh, ywxt, rq);

        result.setRecords(list);
        return result;
    }

}
