<template>
  <div class="dxsdx_cxtj">
    <div class="cxtj_item">
      <span>开始日期：</span>
      <a-date-picker v-model:value="ksrq" style="width: 200px" placeholder="开始日期" />
    </div>
    <div class="cxtj_item">
      <span>结束日期：</span>
      <a-date-picker v-model:value="jsrq" style="width: 200px" placeholder="结束日期" />
    </div>
    <div class="cxtj_item">
      <span>姓名核查结果：</span>
      <a-select v-model:value="xmhcjg" :options="xmhcjgOptions" allow-clear style="width: 200px" placeholder="姓名核查结果" />
    </div>
    <div class="cxtj_item">
      <span>证件核查结果：</span>
      <a-select v-model:value="zjhcjg" :options="zjhcjgOptions" allow-clear style="width: 200px" placeholder="证件核查结果" />
    </div>
    <div class="cxtj_item">
      <span>查询类型：</span>
      <a-select v-model:value="cxlx" :options="dictArray.SFYZ_CZLB" allow-clear :field-names="{ label: 'note', value: 'ibm' }" style="width: 200px" placeholder="查询类型" />
    </div>
    <div class="cxtj_item">
      <span>操作类别：</span>
      <a-select v-model:value="czlb" :options="dictArray.SFYZ_CXLX" allow-clear :field-names="{ label: 'note', value: 'ibm' }" style="width: 200px" placeholder="操作类别" />
    </div>
    <div class="cxtj_item">
      <span>人像比对结果：</span>
      <a-select v-model:value="rxbdjg" :options="rxbdjgOptions" allow-clear style="width: 200px" placeholder="人像比对结果" />
    </div>
    <div class="cxtj_item">
      <a class="btn" @click="getGmsfyzls">查询</a>
      <a class="btn fz" @click="reset">重置</a>
    </div>
  </div>
  <a-divider style="margin: 12px 0"/>
  <div>
    <a-table
      :columns="columns"
      :data-source="data"
      :loading="loading"
      :pagination="pagination"
      @change="pageChange"
      :scroll="{x: 1500}"
    ></a-table>
  </div>
</template>
<script>
import {defineComponent} from "vue";
import {getDcNote} from "@utils/bussinessUtils";
import {commonApi} from "@/api/common";
import dayjs from "dayjs";
import {message} from "ant-design-vue";

export default defineComponent({
  name: "gmsfyz",
  inject: ["khh"],
  props: ["dictArray", "khjbxx"],
  data() {
    return {
      ksrq: null,
      jsrq: null,
      xmhcjg: null,
      zjhcjg: null,
      rxbdjg: null,
      cxlx: null,
      czlb: null,
      xmhcjgOptions: [{label: '一致', value: '一致'}, {label: '不一致', value: '不一致'}],
      zjhcjgOptions: [{label: '一致', value: '一致'}, {label: '不一致', value: '不一致'}],
      rxbdjgOptions: [{label: '一致', value: '一致'}, {label: '不一致', value: '不一致'}],
      columns: [
        { title: '申请日期', dataIndex: 'sqrq', key: 'sqrq', fixed: true, width: 100,},
        { title: '申请时间', dataIndex: 'sqsj', key: 'sqsj', width: 100,},
        { title: '证件编号', dataIndex: 'zjbh', key: 'zjbh', width: 200,},
        { title: '申请姓名', dataIndex: 'sqxm',  key: 'sqxm',  width: 100,},
        { title: '营业部', dataIndex: 'yyb',  key: 'yyb',  width: 150,
          customRender: ({ text }) => {
            return getDcNote("YYB", text, this.dictArray);
          }
        },
        { title: '查询类型', dataIndex: 'cxlx',  key: 'cxlx', width: 150,
          customRender: ({ text }) => {
            return getDcNote("SFYZ_CXLX", text, this.dictArray);
          }
        },
        { title: '出生日期', dataIndex: 'csrq',  key: 'csrq', width: 100,},
        { title: '姓名', dataIndex: 'xm',  key: 'xm', width: 100,},
        { title: '证件号码核查结果', dataIndex: 'zjbhhcjg',  key: 'zjbhhcjg', width: 100,},
        { title: '姓名核查结果', dataIndex: 'xmhcjg',  key: 'xmhcjg', width: 100,},
        { title: '处理结果', dataIndex: 'cljg',  key: 'cljg', width: 100,
          customRender: ({ text }) => {
            return getDcNote("SFYZ_CLJG", text, this.dictArray);
          }
        },
        { title: '结果说明', dataIndex: 'jgsm',  key: 'jgsm', width: 100,},
        { title: '处理时间', dataIndex: 'clsj',  key: 'clsj', width: 100,},
        { title: '操作类别', dataIndex: 'czlb',  key: 'czlb', width: 150,
          customRender: ({ text }) => {
            return getDcNote("SFYZ_CZLB", text, this.dictArray);
          }
        },
        { title: '比对分值', dataIndex: 'bdfz',  key: 'bdfz', width: 100,},
        { title: '比对流水号', dataIndex: 'bdlsh',  key: 'bdlsh', width: 120,},
        { title: '人像比对结果', dataIndex: 'rxbdjg',  key: 'rxbdjg', width: 100,},
      ],
      data: [],
      open: false,
      loading: false,
      current: 1,
      pageSize: 10,
      total: 0,
    }
  },
  computed: {
    pagination() {
      return {
        total: this.total,
        current: this.current,
        pageSize: this.pageSize,
        //showQuickJumper: true,
        showSizeChanger: true,
        showTotal(total) {
          return "共 " + total + " 条";
        },
      };
    }
  },
  methods: {
    pageChange(page){
      this.current = page.current;
      this.pageSize = page.pageSize;
      this.getGmsfyzls();
    },
    reset(){
      this.ksrq = null;
      this.jsrq = null;
      this.xmhcjg = null;
      this.zjhcjg = null;
      this.rxbdjg = null;
      this.cxlx = null;
      this.czlb = null;
    },
    getGmsfyzls() {
      this.loading = true;
      commonApi.executeAMS(
        "sdx.query.IGmsfcxsqService",
        "Gmsfzyzsqcx",
        {
          khxm: this.khjbxx.khmc,
          zjlb: this.khjbxx.zjlb,
          zjbh: this.khjbxx.zjbh,
          ksrq: this.ksrq ? dayjs(this.ksrq).format("YYYYMMDD") : null,
          jsrq: this.jsrq ? dayjs(this.jsrq).format("YYYYMMDD") : null,
          xmhcjg: this.xmhcjg,
          zjhcjg: this.zjhcjg,
          rxbdjg: this.rxbdjg,
          cxlx: this.cxlx,
          czlb: this.czlb,
          isSearchCount: true,
          pagenum: this.current,
          pagesize: this.pageSize
        },
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.data = res.records || [];
        this.total = res.total;
      }).finally(() => {
        this.loading = false;
      })
    },
  }
});
</script>
<style scoped>
.dxsdx_cxtj{line-height: 32px; padding: 0 5px 3px 10px;}
.dxsdx_cxtj .cxtj_item{ white-space: nowrap;margin-bottom: 12px; font-size: 14px; display: inline-block; vertical-align: middle; margin-right: 20px;}
.dxsdx_cxtj .cxtj_item span{color: #888; display: inline-block; margin-right: 15px; vertical-align: middle;}
.dxsdx_cxtj .cxtj_item input[type=text]{display: inline-block; vertical-align: middle; width: 340px; height: 32px; line-height: 30px; border:1px solid #d6d6d6; border-radius: 4px; padding: 0 10px;}
.dxsdx_cxtj .cxtj_item input[type=text]:focus{outline:1px solid #d0ad6b;}
a.btn{min-width: 80px; padding: 0 10px; margin-right: 10px; text-align: center; line-height: 32px;border-radius: 4px; background-color: #f6e5d1; color: #bf935f; cursor: pointer; display: inline-block; vertical-align: middle;}
a.btn.fz{background-color: #fff; color: #777;border: 1px solid #d6d6d6; line-height: 30px;}
a.btn:hover,a.btn.fz:Hover{ background-color: #bf935f; color: #fff; border: none; line-height: 32px;}
</style>