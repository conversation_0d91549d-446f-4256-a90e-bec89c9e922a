<template>
  <div>
    <div :id="chartsId" style=" width:50%; height:100px;">
    </div>
  </div>

</template>

<script>
import {defineComponent} from 'vue';

let barCharts = [];
export default defineComponent({
  props: ["dataset", "yAxis", "color", "title"],
  data() {
    return {
      labels: [],//名称
      values: [],//占比
      values1: [],//个数
      attackSourcesColor: [
        /*new this.$echarts.graphic.LinearGradient(0, 1, 1, 1, [
          {offset: 0, color: '#FF7800'},
          {offset: 1, color: '#FF1919'},
        ]),
        new this.$echarts.graphic.LinearGradient(0, 1, 1, 1, [
          {offset: 0, color: '#3686FD'},
          {offset: 1, color: '#00B42A'},
        ]),*/
        new this.$echarts.graphic.LinearGradient(0, 1, 1, 1, [
          {offset: 0, color: '#FFC730'},
        ]),
        new this.$echarts.graphic.LinearGradient(0, 1, 1, 1, [
          {offset: 0, color: '#36A6FC'},
        ]),
      ],
    }
  },
  methods: {
    dataFormat(data) {
      let arr = [];
      let _this = this
      data.forEach(function (item, i) {
        let itemStyle = {
          color: _this.attackSourcesColor[i],
          borderRadius: [50, 50, 50, 50],
        };
        arr.push({
          value: item,
          itemStyle: itemStyle,
        });
      });
      return arr;
    },
    initBarChart() {
      let el = document.getElementById(this.chartsId)
      barCharts[this.chartsId] = this.$echarts.init(el);
      this.setOption();
    },
    setOption() {
      this.labels = this.$props.dataset[0];
      this.values = this.$props.dataset[1];
      if(this.$props.dataset[2]!=null){
        this.values1 = this.$props.dataset[2];
      }
      let option = this.getOption();
      barCharts[this.chartsId].setOption(option)
    },
    getOption() {
      let _this = this
      let option = {
        grid: {
          x: 0,
          y: 25,
          x2: 0,
          y2: 0,
        //  containLabel: true
        },
        textStyle: {
          color: '#888888'
        },
        legend: {
          textStyle: {
            color: '#888888'
          }
        },
        yAxis: [{
          type: 'category',
          axisLabel: {
            padding: [0, 0, 35, -8],
            inside: true,
            fontSize: 13,
            fontWeight: 500,
            color: '#333333',//_this.$props.color,//'#c5d1ed',
            align: 'left',
            formatter: '{value}',
            rich: {
              a: {
                color: 'transparent',
                lineHeight: 13,
                fontSize: 13,
                shadowColor: 'rgba(0, 0, 0, 1)',
                shadowBlur: 10,
              },
            },
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          data: this.labels
        }, {
          type: 'category',
          axisTick: 'none',
          axisLine: 'none',
          show: true,
          axisLabel: {
            color: '#888888',
            fontSize: '12',
            verticalAlign: 'bottom',
            align: 'right',
            padding: [0, 10, 10, 0],
            formatter: function (value) {
              return '{x|' + value + '}' + '%';
            },
            rich: {
              y: {
                color: '#888888',//_this.$props.color,//'#EED65B',
                fontSize: 13,
              },
              x: {
                color: '#888888',//_this.$props.color,//'#EED65B',
                fontSize: 13,
              },
            },
          },
          data: this.values
        },{
          type: 'category',
          axisTick: 'none',
          axisLine: 'none',
          show: this.$props.dataset[2]!=null?true:false,
          axisLabel: {
            color: '#333333',
            fontSize: '12',
            verticalAlign: 'bottom',
            align: 'right',
            padding: [0, 110, 10, 0],
            formatter: function (value) {
              return '{x|' + value + '}';
            },
            rich: {
              y: {
                color: '#333333',//_this.$props.color,//'#EED65B',
                fontSize: 13,
              },
              x: {
                color: '#333333',//_this.$props.color,//'#EED65B',
                fontSize: 13,
              },
            },
          },
          data: this.values1
        }],
        xAxis: {
          type: 'value',
          show: false,
          min: 0,
          max: 100, // 计算最大值
        },
        series: this.seriesData,
      };
      return option;
    }
  },
  mounted() {
    if (this.$props.dataset && this.$props.dataset.length > 0) {
      this.initBarChart()
    }
  },
  computed: {
    chartsId() {
      return "id_" + this.title
    },
    seriesData() {
      let data = [];
      data.push({
        z: 1,
        barWidth: '20%',
        // 调整柱状图之间的间隔
        barCategoryGap: '100%',
        // 调整同一系列中柱状图之间的间隔
        barGap: '200%',
        type: 'bar',
        data: this.dataFormat(this.values),
        align: 'center',
        emphasis: {
          focus: 'series',
          label: {
            show: false,
            color: '#c6e2ff'
          }
        },
        showBackground: true,
        backgroundStyle: {
          borderRadius: [50, 50, 50, 50]
        },
      });
      return data;
    },
  },
  watch: {
    dataset(newValue, oldValue) {
      if (newValue && newValue.length > 0 && JSON.stringify(newValue) != JSON.stringify(oldValue)) {
        if (barCharts[this.chartsId]) {
          this.setOption();
        } else {
          this.initBarChart();
        }
      }
    }
  },
  unmounted() {
    barCharts = [];
  },
});
</script>

<style scoped>
</style>