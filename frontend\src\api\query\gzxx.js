import request from '@/utils/request'
export default {
    // 查询规则信息
    findSdxGzxx(param) {
        return request({
            url: `ygt.query.ICzmxcxService/khczmxcx`,//TODO 原/bss/sdx/json/findSdxGzxx.sdo
            method: 'post',
            data: param
        })
    },
    // 查询协议信息产品问卷
    findXyxxCpwjPage(param) {
        return request({
            url: `ygt.query.ICzmxcxService/khczmxcx2`,//TODO 原/bss/xyxx/json/findXyxxCpwjPage.sdo
            method: 'post',
            data: param
        })
    },
}
