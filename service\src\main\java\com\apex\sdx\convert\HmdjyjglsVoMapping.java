package com.apex.sdx.convert;

import com.apex.sdx.api.vo.query.HmdjyjglsVo;
import com.apex.sdx.core.converter.IMapping;
import com.apex.sdx.core.converter.MapStructConfig;
import com.apex.sdx.core.mybatis.entity.Thmdjyjgls;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025-03-06
 * @Description:
 */

@Mapper(config = MapStructConfig.class)
@Component
public interface HmdjyjglsVoMapping extends IMapping<HmdjyjglsVo, Thmdjyjgls> {
}
