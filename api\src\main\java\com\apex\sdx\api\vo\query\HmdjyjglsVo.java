package com.apex.sdx.api.vo.query;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-03-06
 * @Description:
 */
@Setter
@Getter
public class HmdjyjglsVo {

    @LiveProperty(note = "id", index = 1)
    private Long id;

    @LiveProperty(note = "校验名单类型", index = 2)
    private Long jymdlx;

    @LiveProperty(note = "校验对象", index = 3)
    private Long jydx;

    @LiveProperty(note = "客户号", index = 4)
    private String khh;

    @LiveProperty(note = "证件类别", index = 5)
    private Long zjlb;

    @LiveProperty(note = "证件编号", index = 6)
    private String zjbh;

    @LiveProperty(note = "客户姓名", index = 7)
    private String khxm;

    @LiveProperty(note = "客户全程", index = 8)
    private String khqc;

    @LiveProperty(note = "客户类别", index = 9)
    private Long khlb;

    @LiveProperty(note = "性别", index = 10)
    private Long xb;

    @LiveProperty(note = "国籍", index = 11)
    private Long gj;

    @LiveProperty(note = "出生日期", index = 12)
    private Integer csrq;

    @LiveProperty(note = "名单客户姓名", index = 13)
    private String mdkhxm;

    @LiveProperty(note = "名单证件类别", index = 14)
    private Long mdzjlb;

    @LiveProperty(note = "名单证件编号", index = 15)
    private String mdzjbh;

    @LiveProperty(note = "名单性别", index = 16)
    private Long mdxb;

    @LiveProperty(note = "名单国籍", index = 17)
    private Long mdgj;

    @LiveProperty(note = "名单出生日期", index = 18)
    private Integer mdcsrq;

    @LiveProperty(note = "名单外部id", index = 19)
    private String mdwbid;

    @LiveProperty(note = "黑名单id", index = 20)
    private Long hmdid;

    @LiveProperty(note = "校验名单结果", index = 21)
    private Long jymdjg;

    @LiveProperty(note = "业务代码", index = 22)
    private String ywdm;

    @LiveProperty(note = "日期", index = 23)
    private Integer rq;

    @LiveProperty(note = "非主体对象id", index = 24)
    private String fztdxid;
}
