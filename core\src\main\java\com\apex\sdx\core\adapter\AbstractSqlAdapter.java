package com.apex.sdx.core.adapter;

import org.apache.commons.lang3.StringUtils;

/**
 * sql语句适配器抽象类
 *
 * <AUTHOR>
 * @date 2023/3/29
 */
public abstract class AbstractSqlAdapter implements ISqlAdapter {

    @Override
    public String instr(String source, String target, String operation, String value, String concatStr) {
        if (StringUtils.isNotBlank(concatStr)) {
            StringBuilder instr = new StringBuilder(" instr");
            //例:mysql instr(concat(concat(';',source),';'),concat(concat(';',target),';'))>0
            //  oracle instr(';' || source || ';' , ';' || target || ';')>0
            instr.append("(").append("concat(concat('").append(concatStr).append("',").append(source).append("),'").append(concatStr).append("')").append(",").append("concat(concat('").append(concatStr).append("',").append(target).append("),'").append(concatStr).append("')").append(")").append(operation).append(" ").append(value).append(" ");
            return instr.toString();
        }
        return " instr(" + source + "," + target + ") " + operation + " " + value + " ";
    }

    @Override
    public String max(String field, String fieldMpp) {
        if (StringUtils.isNotBlank(fieldMpp)) {
            return String.format("max(%s) as %s", field, fieldMpp);
        } else {
            return String.format("max(%s)", field);
        }
    }

    @Override
    public String nvl(String field, String fieldMpp, String defaultValue) {
        if (defaultValue == null) {
            //默认值为空时，设置为空字符串
            defaultValue = "";
        }
        if (StringUtils.isNotBlank(fieldMpp)) {
            return String.format("nvl(%s,%s) as %s", field, defaultValue, fieldMpp);
        } else {
            return String.format("nvl(%s,%s)", defaultValue, field);
        }
    }

    @Override
    public String concat(String str1, String... str2) {
        for(String str: str2) {
            str1 += (" || " + str);
        }
        return str1;
    }
}
