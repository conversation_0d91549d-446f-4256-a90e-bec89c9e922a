package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @TableName t_ods_ygt_cif_tkhqqxx
 */
@TableName(value ="t_ods_ygt_cif_tkhqqxx", schema = "ods")
@Data
public class TOdsYgtCifTkhqqxx implements Serializable {
    private String khh;

    private Integer ywxt;

    private String ywzh;

    private Integer glywxt;

    private String glywzh;

    private Integer tzzfj;

    private Integer tzzfl;

    private BigDecimal zdbzj;

    private BigDecimal zjgmsx;

    private BigDecimal pjzf;

    private BigDecimal shxgedsx;

    private BigDecimal szxgedsx;

    private String email;

    private String sj;

    private static final long serialVersionUID = 1L;
}