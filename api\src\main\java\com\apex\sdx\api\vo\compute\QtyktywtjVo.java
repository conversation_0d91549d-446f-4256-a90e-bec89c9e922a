package com.apex.sdx.api.vo.compute;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> wu<PERSON><PERSON>in
 * @create 2025/5/12 19:06
 */
@Setter
@Getter
public class QtyktywtjVo {

    @LiveProperty(note = "创新业务类别", index = 1)
    private Integer cxywlb;

    @LiveProperty(note = "创新业务名称", index = 2)
    private String cxywmc;

    @LiveProperty(note = "开通数量", index = 3)
    private Integer ktsl;

    @LiveProperty(note = "适当数量", index = 4)
    private Integer sdsl;

    @LiveProperty(note = "适当占比", index = 5)
    private Double sdzb;

    @LiveProperty(note = "不适当数量", index = 6)
    private Integer bsdsl;

    @LiveProperty(note = "不适当占比", index = 7)
    private Double bsdzb;

    @LiveProperty(note = "不通过数量", index = 8)
    private Integer btgsl;

    @LiveProperty(note = "不通过占比", index = 9)
    private Double btgzb;

    @LiveProperty(note = "未签署不适当协议数量", index = 10)
    private Integer wqsbsdxysl;

    @LiveProperty(note = "已签署不适当协议数量", index = 11)
    private Integer yqsbsdxysl;

    @LiveProperty(note = "客户数量", index = 12)
    private Integer khsl;

    @LiveProperty(note = "客户号", index = 13)
    private String khh;

    @LiveProperty(note = "签署状态", index = 14)
    private String qsbsdxy;

    @LiveProperty(note = "适当性结果", index = 15)
    private String sdxjg;

    @LiveProperty(note = "日期", index = 16)
    private String rq;

}
