import axios from 'axios'
import { message, Modal } from 'ant-design-vue'
import store from '@/store'
import debounce from 'lodash/debounce';

const service = axios.create({
    baseURL: '/api', // url = base url + request url
    timeout: 18000, // request timeout
    headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/json'
    }
})

service.interceptors.request.use(
    config => {
        return config
    },
    error => {
        console.log(error) // for debug
        return Promise.reject(error)
    }
)

service.interceptors.response.use(
    response => {
        const res = response.data
        // 请求异常
        if (res.code < 0) {
            message.error(res.note || 'Error', 5);
            //拦截
            return Promise.reject(new Error(res.note || 'Error'))
        } else {
            //放行
            return res
        }
    },
    error => {
        debugger
        console.log('err' + error) // for debug
        // 会话已过期
        if(error.response.status == 403) {
            debounce(() => {
                Modal.warning({
                    title: '提示',
                    content: '未登录或会话已过期',
                    onOk: function (){
                        store.dispatch('user/logout')
                        top.location.href = '/'
                    }
                });
            }, 1000 * 5, {leading: true})()
        } else {
            message.error(error.message, 5)
        }
        return Promise.reject(error)
    }
)

export default service
