package com.apex.sdx.core.utils;

import com.alibaba.fastjson.JSONObject;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/15 10:36
 * @Description: TODO
 */
public class JSONUtil {


    /**
     * json key转大写
     * @param list
     * @return
     */
    public static List<JSONObject> upperCaseList(List<JSONObject> list){
        List<JSONObject> result = new ArrayList<>();
        for(JSONObject obj: list) {
            JSONObject json = new JSONObject();
            for(String key: obj.keySet()) {
                json.put(key.toUpperCase(), obj.get(key));
            }
            result.add(json);
        }
        return result;
    }

    /**
     * json key转小写
     * @param list
     * @return
     */
    public static List<JSONObject> lowerCaseList(List<JSONObject> list){
        List<JSONObject> result = new ArrayList<>();
        for(JSONObject obj: list) {
            JSONObject json = new JSONObject();
            for(String key: obj.keySet()) {
                json.put(key.toLowerCase(), obj.get(key));
            }
            result.add(json);
        }
        return result;
    }

    public static BigDecimal optBigDecimal(JSONObject obj, String key, BigDecimal defualtVal){
        try {
            BigDecimal value = obj.getBigDecimal(key);
            if (value == null) {
                return defualtVal;
            }
            return value;
        } catch (Exception e) {
        }
        return defualtVal;
    }

    public static BigDecimal optBigDecimal(JSONObject obj, String key){
        return optBigDecimal(obj, key, null);
    }
}
