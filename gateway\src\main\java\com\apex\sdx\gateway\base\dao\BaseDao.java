package com.apex.sdx.gateway.base.dao;

import com.alibaba.fastjson.JSONObject;
import com.apex.ams.client.dynamic.ServerReflectionLoader;
import com.apex.sdx.gateway.base.model.EcifFileFrame;
import com.apexsoft.RequestObserver;
import com.apexsoft.ResponseObserver;
import com.apexsoft.grpc.GrpcLiveServiceStub;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

@Slf4j
public class BaseDao {

    @Value("${ams.server.namespace}")
    private String namespace;

    @Autowired
    private GrpcLiveServiceStub grpcLiveServiceStub;

    /**
     * GRPC微服务公共调用方法（JSON调用）
     *
     * @param func
     * @param method
     * @param jsonData
     * @return
     */
    public JSONObject execAmsService(String func, String method, JSONObject jsonData, String namespaceTemp, boolean retry) {
        JSONObject resp = new JSONObject();
        JSONObject json = this.prepareReqData(jsonData);
        String namespaceFinal = "";
        try {
            namespaceFinal = StringUtils.isEmpty(namespaceTemp)?namespace:namespaceTemp;
            String result = grpcLiveServiceStub.exec(func, method, json.toString(), namespaceFinal, "");
            if (!result.isEmpty()) {
                resp = JSONObject.parseObject(result);
            }
            log.info("调用服务[{}]的[{}]方法,请求参数[{}],响应参数[{}]", func, method, json.toJSONString(), result);
        } catch (Exception e) {
            log.error("调用服务[{}]的[{}]方法异常,请求参数[{}]", func, method, json, e);
            resp.put("code",-99);
            resp.put("note",String.format("调用服务[%s]的[%s]方法异常,请求参数[%s],异常信息[%s]",func, method, json, e.getMessage()));
            //如果是没找到服务的方法，刷新服务
            if(e instanceof IllegalArgumentException){
                ServerReflectionLoader.getInstance().refresh(namespaceFinal, func);
                //刷新服务缓存后再调用一次
                if(!retry){
                    resp = execAmsService( func, method, jsonData, namespaceTemp, true);
                }
            }
        }
        return resp;
    }

    public JSONObject execAmsService(String func, String method, JSONObject jsonData, String namespaceTemp) {
        return execAmsService(func, method, jsonData, namespaceTemp, false);
    }

    public JSONObject execAmsService(String func, String method, JSONObject jsonData) {
        return execAmsService(func, method, jsonData, null);
    }

    /**
     * 文件上传服务
     * @param func
     * @param method
     * @param file
     * @param fileName
     * @param czgydm
     * @return
     */
    public JSONObject streamUploadService(String func, String method, File file, String fileName, String czgydm){
        JSONObject resp = new JSONObject();
        final JSONObject[] result = new JSONObject[1];
        final Throwable[] exception = new Throwable[1];

        try{
            //上传文件
            CountDownLatch lock = new CountDownLatch(1);
            ResponseObserver<String> responseObserver = new ResponseObserver<String>() {
                @Override
                public void onNext(String value) {
                    result[0] = JSONObject.parseObject(value);
                }

                @Override
                public void onError(Throwable t) {
                    //生产者反馈回来的错误
                    exception[0] = t;
                    lock.countDown();
                }

                @Override
                public void onCompleted() {
                    lock.countDown();
                }
            };
            //通用消费者上传
            RequestObserver<EcifFileFrame> requestObserver = grpcLiveServiceStub.stream(func,method, EcifFileFrame.class, String.class, namespace,"",responseObserver);

            FileInputStream fis = null;
            try{
                fis = new FileInputStream(file);
                requestObserver.onNext(new EcifFileFrame(fileName, fis.available(), null, czgydm));
                byte[] bytes = new byte[64*1204];
                int length;
                while ((length = fis.read(bytes)) != -1) {
                    EcifFileFrame fileFrame = new EcifFileFrame(bytes, length);
                    requestObserver.onNext(fileFrame);
                }
                requestObserver.onCompleted();
            }finally {
                if(fis!=null){
                    fis.close();
                }
                file.delete();
            }
            lock.await(120, TimeUnit.SECONDS);

            resp = result[0];
            if(resp == null && exception[0]!=null){
                throw exception[0];
            }
        }catch (Throwable e) {
            log.error(e.getMessage(),e);
            resp.put("code", -99);
            resp.put("note",String.format("调用服务[%s]的[%s]方法异常",func, method));
        }

        return resp;
    }


    /**
     * 文件下载服务
     * @param func
     * @param method
     * @param filepath
     * @return
     */
    public JSONObject streamDownloadService(String func, String method, String filepath){
        JSONObject resp = new JSONObject();
        final Throwable[] exception = new Throwable[1];
        final File[] file = new File[1];
        final String[] fileName = {""};
        try{
            CountDownLatch lock = new CountDownLatch(1);
            ResponseObserver<EcifFileFrame> responseObserver = new ResponseObserver<EcifFileFrame>() {
                FileOutputStream fos;
                @Override
                public void onNext(EcifFileFrame value) {
                    if(!StringUtils.isEmpty(value.getFileName())) {
                        fileName[0] = value.getFileName();
                        file[0] = new File(new File("").getAbsoluteFile()+File.separator+ UUID.randomUUID());
                        try {
                            if (!file[0].exists()) {
                                file[0].createNewFile();
                            }
                            fos = new FileOutputStream(file[0]);
                        } catch (Exception e) {
                            onError(e);
                        }
                    } else {
                        try {
                            fos.write(value.getBytes());
                        } catch (IOException e) {
                            onError(e);
                        }
                    }
                }

                @Override
                public void onError(Throwable t) {
                    //生产者反馈回来的错误
                    exception[0] = t;
                    if (fos != null) {
                        try {
                            fos.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    lock.countDown();
                }

                @Override
                public void onCompleted() {
                    //响应报文发送结束
                    if (fos != null) {
                        try {
                            fos.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    lock.countDown();
                }
            };

            RequestObserver<String> requestObserver = grpcLiveServiceStub.stream(func,method, String.class, EcifFileFrame.class,namespace,"",responseObserver);
            requestObserver.onNext(filepath);
            requestObserver.onCompleted();

            //等待响应
            lock.await(10, TimeUnit.SECONDS);
            if (exception[0] != null) {
                throw exception[0];
            }
            resp.put("code",1);
            resp.put("note","下载成功");
            resp.put("filename", fileName[0]);
            resp.put("file", file[0]);
        }catch (Throwable e){
            log.error("文件下载失败:"+e.getMessage(),e);
            resp.put("code", -99);
            resp.put("note",String.format("调用服务[%s]的[%s]方法异常",func, method));
        }

        return resp;
    }

    /**
     * 准备公共参数。
     *
     * @param data 待补充公共参数的数据
     * @return 该数据
     */
    public JSONObject prepareReqData(JSONObject data) {
        /*String ip = SystemTool.getSessionCzzd(UserSession.getRequestHolder());
        if (ip.trim().equals("")) {
            ip = AddressUtils.getIntranetIp(UserSession.getRequestHolder());
        }
        data.put("czzd", ip);
        //获取柜员信息
        String czgydm = data.getString("czgydm");
        if(StringUtils.isEmpty(czgydm)){
            AuthUser<JSONObject> authUser = null;
            try{
                authUser = UserSession.getUserSession();
            }catch (Exception e){
                //没登录调用会异常
            }
            JSONObject obj = authUser == null ? null : authUser.getUser();
            if (obj != null) {
                data.put("czgydm", obj.get("id"));
            } else {
                data.put("czgydm", "0");
            }
        }

        data.put("fqqd", Constant.FQQD_5);*/
        //data.put("blms", Constant.BLMS_3);
        return data;
    }

}
