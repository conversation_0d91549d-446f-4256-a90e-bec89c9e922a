package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.api.vo.compute.SdxsjtjVo;
import com.apex.sdx.api.vo.query.SdxsjDescVo;
import com.apex.sdx.api.vo.query.SdxsjVo;
import com.apex.sdx.core.mybatis.entity.TsdxSj;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 */
public interface TsdxSjService extends IService<TsdxSj> {

    /**
     * @param khh
     * @param clzt
     * @param isSearchCount
     * @param pagesize
     * @param pagenum
     * @return
     */
    Page<SdxsjVo> queryByKhhAndClzt(String khh, String clzt, boolean isSearchCount, int pagesize, int pagenum);
    /**
     * 适当性事件详情
     * @param sjid sjid
     * @return 详情列表
     */
    List<SdxsjDescVo> querySdxsjDesc(Long sjid);

    Page<SdxsjVo> queryByKhhAndClztAndRq(String khh, String ksrq, String jsrq, String clzt, boolean isSearchCount, int pagesize, int pagenum);

    Page<SdxsjtjVo> compute(String ksrq, String jsrq, String khh, String sjbm, boolean isSearchCount, int pagesize, int pagenum);

    Page<SdxsjtjVo> compute4Sdxsj(String ksrq, String jsrq, String khh, String sjbm, boolean isSearchCount, int pagesize, int pagenum);

}
