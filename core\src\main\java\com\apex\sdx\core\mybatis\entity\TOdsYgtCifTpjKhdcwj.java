package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName t_ods_ygt_cif_tpj_khdcwj
 */
@TableName(value ="t_ods_ygt_cif_tpj_khdcwj", schema = "ods")
@Data
public class TOdsYgtCifTpjKhdcwj implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 
     */
    private Integer dcrq;

    /**
     * 
     */
    private String dcsj;

    /**
     * 
     */
    private Long paperid;

    /**
     * 
     */
    private Long zjlb;

    /**
     * 
     */
    private String zjbh;

    /**
     * 
     */
    private String khh;

    /**
     * 
     */
    private String khjc;

    /**
     * 
     */
    private Long yyb;

    /**
     * 
     */
    private Long wjdf;

    /**
     * 
     */
    private Long cpdj;

    /**
     * 
     */
    private Long dcr;

    /**
     * 
     */
    private Long fqqd;

    /**
     * 
     */
    private Long ywqqid;

    /**
     * 
     */
    private String tzpz;

    /**
     * 
     */
    private Long tzqx;

    /**
     * 
     */
    private Long czmxid;

    /**
     * 
     */
    private String yqsy;

    /**
     * 
     */
    private String pcsm;

    /**
     * 
     */
    private Integer zscptgts;

    /**
     * 
     */
    private String cid;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TOdsYgtCifTpjKhdcwj other = (TOdsYgtCifTpjKhdcwj) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getDcrq() == null ? other.getDcrq() == null : this.getDcrq().equals(other.getDcrq()))
            && (this.getDcsj() == null ? other.getDcsj() == null : this.getDcsj().equals(other.getDcsj()))
            && (this.getPaperid() == null ? other.getPaperid() == null : this.getPaperid().equals(other.getPaperid()))
            && (this.getZjlb() == null ? other.getZjlb() == null : this.getZjlb().equals(other.getZjlb()))
            && (this.getZjbh() == null ? other.getZjbh() == null : this.getZjbh().equals(other.getZjbh()))
            && (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
            && (this.getKhjc() == null ? other.getKhjc() == null : this.getKhjc().equals(other.getKhjc()))
            && (this.getYyb() == null ? other.getYyb() == null : this.getYyb().equals(other.getYyb()))
            && (this.getWjdf() == null ? other.getWjdf() == null : this.getWjdf().equals(other.getWjdf()))
            && (this.getCpdj() == null ? other.getCpdj() == null : this.getCpdj().equals(other.getCpdj()))
            && (this.getDcr() == null ? other.getDcr() == null : this.getDcr().equals(other.getDcr()))
            && (this.getFqqd() == null ? other.getFqqd() == null : this.getFqqd().equals(other.getFqqd()))
            && (this.getYwqqid() == null ? other.getYwqqid() == null : this.getYwqqid().equals(other.getYwqqid()))
            && (this.getTzpz() == null ? other.getTzpz() == null : this.getTzpz().equals(other.getTzpz()))
            && (this.getTzqx() == null ? other.getTzqx() == null : this.getTzqx().equals(other.getTzqx()))
            && (this.getCzmxid() == null ? other.getCzmxid() == null : this.getCzmxid().equals(other.getCzmxid()))
            && (this.getYqsy() == null ? other.getYqsy() == null : this.getYqsy().equals(other.getYqsy()))
            && (this.getPcsm() == null ? other.getPcsm() == null : this.getPcsm().equals(other.getPcsm()))
            && (this.getZscptgts() == null ? other.getZscptgts() == null : this.getZscptgts().equals(other.getZscptgts()))
            && (this.getCid() == null ? other.getCid() == null : this.getCid().equals(other.getCid()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getDcrq() == null) ? 0 : getDcrq().hashCode());
        result = prime * result + ((getDcsj() == null) ? 0 : getDcsj().hashCode());
        result = prime * result + ((getPaperid() == null) ? 0 : getPaperid().hashCode());
        result = prime * result + ((getZjlb() == null) ? 0 : getZjlb().hashCode());
        result = prime * result + ((getZjbh() == null) ? 0 : getZjbh().hashCode());
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getKhjc() == null) ? 0 : getKhjc().hashCode());
        result = prime * result + ((getYyb() == null) ? 0 : getYyb().hashCode());
        result = prime * result + ((getWjdf() == null) ? 0 : getWjdf().hashCode());
        result = prime * result + ((getCpdj() == null) ? 0 : getCpdj().hashCode());
        result = prime * result + ((getDcr() == null) ? 0 : getDcr().hashCode());
        result = prime * result + ((getFqqd() == null) ? 0 : getFqqd().hashCode());
        result = prime * result + ((getYwqqid() == null) ? 0 : getYwqqid().hashCode());
        result = prime * result + ((getTzpz() == null) ? 0 : getTzpz().hashCode());
        result = prime * result + ((getTzqx() == null) ? 0 : getTzqx().hashCode());
        result = prime * result + ((getCzmxid() == null) ? 0 : getCzmxid().hashCode());
        result = prime * result + ((getYqsy() == null) ? 0 : getYqsy().hashCode());
        result = prime * result + ((getPcsm() == null) ? 0 : getPcsm().hashCode());
        result = prime * result + ((getZscptgts() == null) ? 0 : getZscptgts().hashCode());
        result = prime * result + ((getCid() == null) ? 0 : getCid().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", dcrq=").append(dcrq);
        sb.append(", dcsj=").append(dcsj);
        sb.append(", paperid=").append(paperid);
        sb.append(", zjlb=").append(zjlb);
        sb.append(", zjbh=").append(zjbh);
        sb.append(", khh=").append(khh);
        sb.append(", khjc=").append(khjc);
        sb.append(", yyb=").append(yyb);
        sb.append(", wjdf=").append(wjdf);
        sb.append(", cpdj=").append(cpdj);
        sb.append(", dcr=").append(dcr);
        sb.append(", fqqd=").append(fqqd);
        sb.append(", ywqqid=").append(ywqqid);
        sb.append(", tzpz=").append(tzpz);
        sb.append(", tzqx=").append(tzqx);
        sb.append(", czmxid=").append(czmxid);
        sb.append(", yqsy=").append(yqsy);
        sb.append(", pcsm=").append(pcsm);
        sb.append(", zscptgts=").append(zscptgts);
        sb.append(", cid=").append(cid);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}