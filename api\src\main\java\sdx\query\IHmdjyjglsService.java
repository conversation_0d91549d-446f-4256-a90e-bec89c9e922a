package sdx.query;

import com.apex.sdx.api.req.query.HmdjyjglscxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.vo.query.HmdjyjglsVo;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR>
 * @Date 2025-03-06
 * @Description:
 */
public interface IHmdjyjglsService {

    @LiveMethod(paramAsRequestBody = true, note = "黑名单校验结果流水查询")
    QueryPageResponse<HmdjyjglsVo> hmdjyjglscx(HmdjyjglscxReq req) throws Exception;
}
