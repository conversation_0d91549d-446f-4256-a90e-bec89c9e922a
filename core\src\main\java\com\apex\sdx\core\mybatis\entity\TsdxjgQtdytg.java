package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

/**
 * 全体已订阅投顾适当性
 * @TableName tsdxjg_qtdytg
 */
@TableName(value ="tsdxjg_qtdytg")
public class TsdxjgQtdytg implements Serializable {
    /**
     * 日期
     */
    private Integer rq;

    /**
     * 客户号
     */
    private String khh;

    /**
     * 产品代码;TCP_TGCPXX.CPDM
     */
    @TableId
    private Long cpdm;

    /**
     * 营业部
     */
    private Long yyb;

    /**
     * 订单渠道;1|客户线上;2|运营线下
     */
    private Long ddqd;

    /**
     * 订单日期
     */
    private Integer ddrq;

    /**
     * 订单状态;1|解约中;2|签约成功;3|解约成功
     */
    private Long ddzt;

    /**
     * 产品名称
     */
    private String cpmc;

    /**
     * 客户风险等级;SDX_FXCSNL
     */
    private Long khfxdj;

    /**
     * 产品风险等级;SDX_CPFXDJ
     */
    private Long cpfxdj;

    /**
     * 风险等级适当性;SDX_PPJG，-1|不通过;0|不适当;1|匹配
     */
    private Long fxdjsdx;

    /**
     * 适当性结果;SDX_PPJG，-1|不通过;0|不适当;1|匹配
     */
    private Long sdxjg;

    /**
     * 数据来源;TSYSTEM.YWXT，1000;1003
     */
    private Integer sjly;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 日期
     */
    public Integer getRq() {
        return rq;
    }

    /**
     * 日期
     */
    public void setRq(Integer rq) {
        this.rq = rq;
    }

    /**
     * 客户号
     */
    public String getKhh() {
        return khh;
    }

    /**
     * 客户号
     */
    public void setKhh(String khh) {
        this.khh = khh;
    }

    /**
     * 产品代码;TCP_TGCPXX.CPDM
     */
    public Long getCpdm() {
        return cpdm;
    }

    /**
     * 产品代码;TCP_TGCPXX.CPDM
     */
    public void setCpdm(Long cpdm) {
        this.cpdm = cpdm;
    }

    /**
     * 营业部
     */
    public Long getYyb() {
        return yyb;
    }

    /**
     * 营业部
     */
    public void setYyb(Long yyb) {
        this.yyb = yyb;
    }

    /**
     * 订单渠道;1|客户线上;2|运营线下
     */
    public Long getDdqd() {
        return ddqd;
    }

    /**
     * 订单渠道;1|客户线上;2|运营线下
     */
    public void setDdqd(Long ddqd) {
        this.ddqd = ddqd;
    }

    /**
     * 订单日期
     */
    public Integer getDdrq() {
        return ddrq;
    }

    /**
     * 订单日期
     */
    public void setDdrq(Integer ddrq) {
        this.ddrq = ddrq;
    }

    /**
     * 订单状态;1|解约中;2|签约成功;3|解约成功
     */
    public Long getDdzt() {
        return ddzt;
    }

    /**
     * 订单状态;1|解约中;2|签约成功;3|解约成功
     */
    public void setDdzt(Long ddzt) {
        this.ddzt = ddzt;
    }

    /**
     * 产品名称
     */
    public String getCpmc() {
        return cpmc;
    }

    /**
     * 产品名称
     */
    public void setCpmc(String cpmc) {
        this.cpmc = cpmc;
    }

    /**
     * 客户风险等级;SDX_FXCSNL
     */
    public Long getKhfxdj() {
        return khfxdj;
    }

    /**
     * 客户风险等级;SDX_FXCSNL
     */
    public void setKhfxdj(Long khfxdj) {
        this.khfxdj = khfxdj;
    }

    /**
     * 产品风险等级;SDX_CPFXDJ
     */
    public Long getCpfxdj() {
        return cpfxdj;
    }

    /**
     * 产品风险等级;SDX_CPFXDJ
     */
    public void setCpfxdj(Long cpfxdj) {
        this.cpfxdj = cpfxdj;
    }

    /**
     * 风险等级适当性;SDX_PPJG，-1|不通过;0|不适当;1|匹配
     */
    public Long getFxdjsdx() {
        return fxdjsdx;
    }

    /**
     * 风险等级适当性;SDX_PPJG，-1|不通过;0|不适当;1|匹配
     */
    public void setFxdjsdx(Long fxdjsdx) {
        this.fxdjsdx = fxdjsdx;
    }

    /**
     * 适当性结果;SDX_PPJG，-1|不通过;0|不适当;1|匹配
     */
    public Long getSdxjg() {
        return sdxjg;
    }

    /**
     * 适当性结果;SDX_PPJG，-1|不通过;0|不适当;1|匹配
     */
    public void setSdxjg(Long sdxjg) {
        this.sdxjg = sdxjg;
    }

    /**
     * 数据来源;TSYSTEM.YWXT，1000;1003
     */
    public Integer getSjly() {
        return sjly;
    }

    /**
     * 数据来源;TSYSTEM.YWXT，1000;1003
     */
    public void setSjly(Integer sjly) {
        this.sjly = sjly;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TsdxjgQtdytg other = (TsdxjgQtdytg) that;
        return (this.getRq() == null ? other.getRq() == null : this.getRq().equals(other.getRq()))
            && (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
            && (this.getCpdm() == null ? other.getCpdm() == null : this.getCpdm().equals(other.getCpdm()))
            && (this.getYyb() == null ? other.getYyb() == null : this.getYyb().equals(other.getYyb()))
            && (this.getDdqd() == null ? other.getDdqd() == null : this.getDdqd().equals(other.getDdqd()))
            && (this.getDdrq() == null ? other.getDdrq() == null : this.getDdrq().equals(other.getDdrq()))
            && (this.getDdzt() == null ? other.getDdzt() == null : this.getDdzt().equals(other.getDdzt()))
            && (this.getCpmc() == null ? other.getCpmc() == null : this.getCpmc().equals(other.getCpmc()))
            && (this.getKhfxdj() == null ? other.getKhfxdj() == null : this.getKhfxdj().equals(other.getKhfxdj()))
            && (this.getCpfxdj() == null ? other.getCpfxdj() == null : this.getCpfxdj().equals(other.getCpfxdj()))
            && (this.getFxdjsdx() == null ? other.getFxdjsdx() == null : this.getFxdjsdx().equals(other.getFxdjsdx()))
            && (this.getSdxjg() == null ? other.getSdxjg() == null : this.getSdxjg().equals(other.getSdxjg()))
            && (this.getSjly() == null ? other.getSjly() == null : this.getSjly().equals(other.getSjly()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getRq() == null) ? 0 : getRq().hashCode());
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getCpdm() == null) ? 0 : getCpdm().hashCode());
        result = prime * result + ((getYyb() == null) ? 0 : getYyb().hashCode());
        result = prime * result + ((getDdqd() == null) ? 0 : getDdqd().hashCode());
        result = prime * result + ((getDdrq() == null) ? 0 : getDdrq().hashCode());
        result = prime * result + ((getDdzt() == null) ? 0 : getDdzt().hashCode());
        result = prime * result + ((getCpmc() == null) ? 0 : getCpmc().hashCode());
        result = prime * result + ((getKhfxdj() == null) ? 0 : getKhfxdj().hashCode());
        result = prime * result + ((getCpfxdj() == null) ? 0 : getCpfxdj().hashCode());
        result = prime * result + ((getFxdjsdx() == null) ? 0 : getFxdjsdx().hashCode());
        result = prime * result + ((getSdxjg() == null) ? 0 : getSdxjg().hashCode());
        result = prime * result + ((getSjly() == null) ? 0 : getSjly().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", rq=").append(rq);
        sb.append(", khh=").append(khh);
        sb.append(", cpdm=").append(cpdm);
        sb.append(", yyb=").append(yyb);
        sb.append(", ddqd=").append(ddqd);
        sb.append(", ddrq=").append(ddrq);
        sb.append(", ddzt=").append(ddzt);
        sb.append(", cpmc=").append(cpmc);
        sb.append(", khfxdj=").append(khfxdj);
        sb.append(", cpfxdj=").append(cpfxdj);
        sb.append(", fxdjsdx=").append(fxdjsdx);
        sb.append(", sdxjg=").append(sdxjg);
        sb.append(", sjly=").append(sjly);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}