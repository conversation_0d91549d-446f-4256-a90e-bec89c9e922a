package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.vo.query.ZmwjyxsjVo;
import com.apex.sdx.core.exception.BusinessException;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TzmwjYxsj;
import com.apex.sdx.core.mybatis.service.TzmwjYxsjService;
import com.apex.sdx.core.mybatis.mapper.TzmwjYxsjMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class TzmwjYxsjServiceImpl extends ServiceImpl<TzmwjYxsjMapper, TzmwjYxsj>
    implements TzmwjYxsjService{

    @Override
    public List<ZmwjyxsjVo> queryByKhh(String khh) {
        try {
            return this.baseMapper.selectZmwjyxsj(khh);
        } catch (Exception e) {
            String note = String.format("证明文件影像数据查询失败：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }
}




