<template xmlns="http://www.w3.org/1999/html">
  <!-- 服务缓存刷新-->
  <div style="background: #ececec;padding: 10px">
    <div>
      <a-card>
        <template #title>
          <a-row>
            <a-col :span="12">
              <div>
                <div class="titleStyle"></div>
                <span class="titleTetxStyle">客户适当性分布</span>

              </div>
            </a-col>
            <a-col :span="12" style="text-align: right">
              <div>
                <!--                <span class="titleTetxStyle">数据日期:&nbsp;&nbsp;</span>
                                <a-date-picker v-model:value="LastTradingDay" disabled format="YYYY-MM-DD" valueFormat="YYYYMMDD"
                                               style="width: 180px"/>-->
                <span style="padding: 0 10px; color: #888888;">数据时间: {{LastTradingDay.replace(/(\d{4})(\d{2})(\d{2})/, "$1-$2-$3")}}</span>
              </div>
            </a-col>
          </a-row>
        </template>
        <div style="height: 170px;background: linear-gradient(to bottom, #ffffff, #f6faff);margin: 0 0 10px;">
          <a-row>
            <a-col :span="8" style="display: flex">
              <div style="width: 40%;padding-left: 40px;padding-top: 25px;">
                <p style="line-height: 42px;">
                  <span class="khsumIcon" style="margin-right: 15px;"></span>
                  <span style="font-size: 16px;color: #333333">总客户数 </span>
                  <span style="color: #999999">(人)</span>
                </p>
                <p style="line-height: 18px;margin: 15px 0;">
                  <span style="font-weight: bold;font-size: 28px;color: #333333;">{{
                      Number(zkhs.zs || 0).toLocaleString()
                    }}</span>
                </p>
                <p style="">
                  <span class="rzz">日增长<span class="zzsl">+{{ zkhs.rzz }}</span><span><CaretUpOutlined
                      style="color: #FF0000"/></span>
                  </span><br>
                  <span class="yzz">月增长<span class="zzsl">+{{ zkhs.yzz }}</span><span><CaretUpOutlined
                      style="color: #FF0000"/></span>
                  </span>
                </p>
              </div>
              <div style="width: 60%">
                <khSdxfbLineChart :dataset="zkhsLine" v-if="zkhsLine.length>0"
                                  :title="'总客户数'"></khSdxfbLineChart>
              </div>
            </a-col>
            <a-divider type="vertical" style="height: 160px;position: absolute;left: 33%;top:11%;"/>
            <a-col :span="8" style="display: flex">
              <div style="width: 40%;padding-left: 40px;padding-top: 25px;">
                <div>
                  <div style="margin-bottom: 10px;">
                    <span class="zhlxIcon" style="margin-right: 10px;"></span>
                    <span style="font-size: 14px;color: #999999">账户类型 </span>
                    <span style="color: #999999">(人/占比)</span>
                  </div>
                  <a-select style="width: 130px" v-model:value="zhlx" size="small" @change="handleZhlxChange"
                            :options="tzzflOptions" placeholder="请选择账户类型" class="khsdxselect"
                            :field-names="{ label: 'note', value: 'ibm' }"
                            option-filter-prop="children"></a-select>
                </div>
                <p style="line-height: 18px;margin: 20px 0;">
                  <span style="font-weight: bold;font-size: 28px;color: #333333;">{{
                      Number(zhlxMap.zs || 0).toLocaleString()
                    }}</span>
                  <span style="font-size: 16px;color: #888888;"> / {{
                      parseFloat((zhlxMap.zb * 100).toString()).toFixed(2)
                    }}%</span>
                </p>
                <!-- 专业投资者显示日增长和月增长 - 横向排列 -->
                <div style="display: flex; gap: 15px; margin-top: 10px;" v-if="zhlx !== '0' && zhlxMap.rzz !== undefined && zhlxMap.yzz !== undefined">
                  <span class="rzz">日增长<span class="zzsl">+{{ zhlxMap.rzz || 0 }}</span><span><CaretUpOutlined
                      style="color: #FF0000"/></span>
                  </span>
                  <span class="yzz">月增长<span class="zzsl">+{{ zhlxMap.yzz || 0 }}</span><span><CaretUpOutlined
                      style="color: #FF0000"/></span>
                  </span>
                </div>
              </div>
              <div style="width: 50%">
                <KhSdxfbPieChart :dataset="zhlxPieDataset" v-if="zhlxPieDataset.length>0"
                                 :title="'账户类型'"
                                 :colorList="['#F7AF52','#55C9FF']"></KhSdxfbPieChart>
              </div>
            </a-col>
            <a-divider type="vertical" style="height: 160px;position: absolute;left: 66%;top:11%;"/>
            <a-col :span="8" style="display: flex">
              <div style="width: 40%;padding-left: 40px;padding-top: 25px;">
                <div>
                  <div style="margin-bottom: 10px;">
                    <span style="width: 42px;height: 42px"> <span class="fxdjIcon"
                                                                  style="margin-right: 10px;"></span></span>

                    <span style="font-size: 14px;color: #999999">风险等级 </span>
                    <span style="color: #999999">(人/占比)</span>
                  </div>
                  <a-select style="width: 130px" v-model:value="fxdj" size="small" @change="handleFxdjChange"
                            :options="dictArray?.SDX_FXCSNL" placeholder="请选择风险等级" class="khsdxselect"
                            :field-names="{ label: 'note', value: 'ibm' }"
                            option-filter-prop="children"></a-select>
                </div>
                <p style="line-height: 18px;margin: 20px 0;">
                  <span style="font-weight: bold;font-size: 28px;color: #333333;">{{
                      Number(fxdjMap.zs || 0).toLocaleString()
                    }}</span>
                  <span style="font-size: 16px;color: #888888;"> / {{
                      parseFloat((fxdjMap.zb * 100).toString()).toFixed(2)
                    }}%</span>
                </p>
              </div>
              <div style="width: 50%">
                <KhSdxfbPieChart :dataset="fxdjPieDataset" v-if="fxdjPieDataset.length>0"
                                 :title="'风险等级'"
                                 :colorList="['#8B46FD','#55C9FF']"></KhSdxfbPieChart>
              </div>
            </a-col>
          </a-row>


        </div>
        <div style="padding: 0 14px;">
          <div class="title2Style"></div>
          <span class="titleTetxStyle">投资者构成及分布</span>
        </div>
        <div>
          <AccountBarChart
              :dataset="pttzzfxdjfbArr"
              :dataset1="zytzzfxdjfbArr"
              :legend="tzzfxdjfbName" v-if="pttzzfxdjfbArr.length>0"
              :title="'投资者风险等级分布'"
              :hideProfessional="true"
          />

        </div>
      </a-card>
    </div>


    <div style="margin-top: 10px">
      <a-row>
        <a-col :span="12">
          <a-card class="zhsdxqk">
            <template #title>
              <div>
                <div class="titleStyle"></div>
                <span class="titleTetxStyle">账户适当性情况</span>
              </div>
            </template>
            <div style="height: 260px;display: flex;flex-wrap: wrap;">
              <div style="width: 33%">
                <div style="margin-left: 50px;">
                  <p style="display: inline-flex;align-items: center;margin-bottom: 15px;">
                    <span style="width: 5px;height: 5px;background: #B48A3B;opacity: 0.3;display: block;"></span>
                    <span style="line-height: 30px;margin-left: 10px">账户状态</span>
                    <a style="margin-left: 10px" @click="openMore('zhzt')">
                      <ContainerOutlined style="color: #B48A3B"/>
                    </a>
                  </p>
                  <p class="zc">正常：<span class="zctb"></span>
                    <span class="sdxqkValue">{{ zhztzcgs }}
                    </span> / <span class="sdxqkPercent">{{ parseFloat((zhztzczb * 100).toString()).toFixed(2) }}%
                      </span>
                  </p>
                  <p class="yc">异常：<span class="yctb"></span>
                    <span class="sdxqkValue">{{ zhztycgs }}
                    </span> / <span class="sdxqkPercent">{{ parseFloat((zhztyczb * 100).toString()).toFixed(2) }}%
                      </span>
                  </p>
                </div>
              </div>
              <div style="width: 33%; display: none;">
                <div style="margin-left: 50px;">
                  <p style="display: inline-flex;align-items: center;margin-bottom: 15px;">
                    <span style="width: 5px;height: 5px;background: #B48A3B;opacity: 0.3;display: block;"></span>
                    <span style="line-height: 30px;margin-left: 10px">反洗钱风险等级</span>
                    <a style="margin-left: 10px" @click="openMore('fxqfxdj')">
                      <ContainerOutlined style="color: #B48A3B"/>
                    </a>
                  </p>
                  <p class="zc">正常：<span class="zctb"></span>
                    <span class="sdxqkValue">{{ zhfxqfxdjzcgs }}
                      </span> / <span class="sdxqkPercent">{{
                        parseFloat((zhfxqfxdjzczb * 100).toString()).toFixed(2)
                      }}%</span></p>
                  <p class="yc">异常：<span class="yctb"></span>
                    <span class="sdxqkValue">{{ zhfxqfxdjycgs }}
                      </span> / <span class="sdxqkPercent">{{
                        parseFloat((zhfxqfxdjyczb * 100).toString()).toFixed(2)
                      }}%</span></p>
                </div>
              </div>

              <div style="width: 33%">
                <div style="margin-left: 50px;">
                  <p style="display: inline-flex;align-items: center;margin-bottom: 15px;">
                    <span style="width: 5px;height: 5px;background: #B48A3B;opacity: 0.3;display: block;"></span>
                    <span style="line-height: 30px;margin-left: 10px">证件有效期</span>
                    <a style="margin-left: 10px" @click="openMore('zjyxq')">
                      <ContainerOutlined style="color: #B48A3B"/>
                    </a>
                  </p>
                  <p class="zc">正常：<span class="zctb"></span><span class="sdxqkValue">{{ zjyxqzcgs }}
                      </span> /
                    <span class="sdxqkPercent">{{
                        parseFloat((zjyxqzczb * 100).toString()).toFixed(2)
                      }}%</span></p>
                  <p class="yc">过期：<span class="yctb"></span><span class="sdxqkValue">{{ zjyxqycgs }}
                      </span> /
                    <span class="sdxqkPercent">{{
                        parseFloat((zjyxqyczb * 100).toString()).toFixed(2)
                      }}%</span>
                  </p>
                </div>
              </div>
              <div style="width: 33%">
                <div style="margin-left: 50px;">
                  <p style="display: inline-flex;align-items: center;margin-bottom: 15px;">
                    <span style="width: 5px;height: 5px;background: #B48A3B;opacity: 0.3;display: block;"></span>
                    <span style="line-height: 30px;margin-left: 10px">风险测评有效期</span>
                    <a style="margin-left: 10px" @click="openMore('fxcpyxq')">
                      <ContainerOutlined style="color: #B48A3B"/>
                    </a>
                  </p>
                  <p class="zc">正常：<span class="zctb"></span>
                    <span class="sdxqkValue">{{ fxcpyxzcgs }}
                    </span> /
                    <span class="sdxqkPercent">{{ parseFloat((fxcpyxzczb * 100).toString()).toFixed(2) }}%
                      </span>
                  </p>
                  <p class="yc">过期：<span class="yctb"></span><span class="sdxqkValue">{{ fxcpyxycgs }}
                      </span> / <span class="sdxqkPercent">{{
                      parseFloat((fxcpyxyczb * 100).toString()).toFixed(2)
                    }}%</span></p>
                </div>
              </div>
              <div style="width: 33%">
                <div style="margin-left: 50px;">
                  <p style="display: inline-flex;align-items: center;margin-bottom: 15px;">
                    <span style="width: 5px;height: 5px;background: #B48A3B;opacity: 0.3;display: block;"></span>
                    <span style="line-height: 30px;margin-left: 10px">其他信息</span>
                    <a style="margin-left: 10px" @click="openMore('qtxx')">
                      <ContainerOutlined style="color: #B48A3B"/>
                    </a>
                  </p>
                  <p class="zc">正常：<span class="zctb"></span><span class="sdxqkValue">{{ zhqtxxzcgs }}
                      </span> /
                    <span class="sdxqkPercent">{{
                        parseFloat((zhqtxxzczb * 100).toString()).toFixed(2)
                      }}%</span></p>
                  <p class="yc">异常：<span class="yctb"></span><span class="sdxqkValue">{{ zhqtxxycgs }}
                      </span> /
                    <span class="sdxqkPercent">{{
                        parseFloat((zhqtxxyczb * 100).toString()).toFixed(2)
                      }}%</span></p>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card class="ywzhsdxqk">
            <template #title>
              <div>
                <div class="titleStyle"></div>
                <span class="titleTetxStyle">业务账户适当性情况</span>
              </div>
            </template>
            <div style="height: 260px;display: flex;flex-wrap: wrap;">
              <div style="width: 50%">
                <div style="margin: 5px 0 0 30px;">
                  <p><span class="rzrqIcon"></span>
                    <span style="display: inline-block;vertical-align: middle;margin-left: 10px;">
                        融资融券
                        <span style="color: #999999">(个)</span><br>
                    <span style="display: block;line-height: 30px;font-weight: bold;font-size: 18px;">{{
                        rzrqArr.total
                      }}</span>
                    </span>
                  </p>
                  <div>
                    <YwzhSdxPolarChart :dataset="rzrqArr.num" v-if="rzrqArr.num.length>0"
                                       :title="'融资融券'"></YwzhSdxPolarChart>
                  </div>
                </div>
              </div>
              <div style="width: 50%">
                <div style="margin: 5px 0 0 30px;">
                  <p><span class="gpqqIcon"></span>
                    <span style="display: inline-block;vertical-align: middle;margin-left: 10px;">
                        股票期权
                        <span style="color: #999999">(个)</span><br>
                    <span style="display: block;line-height: 30px;font-weight: bold;font-size: 18px;">{{
                        gpqqArr.total
                      }}</span>
                    </span>
                  </p>
                  <div>
                    <YwzhSdxPolarChart :dataset="gpqqArr.num" v-if="gpqqArr.num.length>0"
                                       :title="'股票期权'"></YwzhSdxPolarChart>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <div style="margin-top: 10px">
      <a-card>
        <template #title>
          <div>
            <div class="titleStyle"></div>
            <a @click="openSdxReport()" class="clickable-title">
              <span class="titleTetxStyle">业务适当性情况</span>
              <LinkOutlined class="link-icon" />
            </a>
          </div>
        </template>
        <div style="height: 200px;display: flex;">
          <div style="width: 100%">
            <div style="padding: 10px 14px;">
              <div class="title2Style"></div>
              <span class="titleTetxStyle">业务适当性整体情况 TOP 6</span>
              <div style="float: right;margin-right: 40px;">
                <a style="color: #BF935F;" @click="openMore('ztqk')">更多
                </a>
              </div>
            </div>
            <div style="width: 100%;display: flex;position: relative;">
              <div style="width: 50%">
                <SdxBarChart
                    :dataset="sdxztqkArr1" v-if="sdxztqkArr1.length>0" :type="1"
                    :title="'权限适当性整体情况top1-3'">
                </SdxBarChart>
              </div>
              <a-divider type="vertical" style="height: 120px;margin: 0 10px;align-self: center;"/>
              <div style="width: 50%">
                <SdxBarChart
                    :dataset="sdxztqkArr2" v-if="sdxztqkArr2.length>0" :type="2"
                    :title="'权限适当性整体情况top4-6'">
                </SdxBarChart>
              </div>
            </div>
          </div>
          <!-- 业务不适当协议签署情况整个区域已隐藏 -->
        </div>
      </a-card>
    </div>

    <div style="margin-top: 10px">
      <a-card>
        <template #title>
          <div>
            <div class="titleStyle"></div>
            <a @click="openCpccSdxReport()" class="clickable-title">
              <span class="titleTetxStyle">产品持仓适当性情况</span>
              <LinkOutlined class="link-icon" />
            </a>
          </div>
        </template>
        <div style="height: 270px;">
          <a-row>
            <a-col :span="12">
              <div style="padding: 4px 14px;">
                <div class="title2Style"></div>
                <span class="titleTetxStyle">场内外持仓适当性匹配</span>
              </div>
              <div style="height: 100%;padding: 10px 0 0 28px;">
                <a-row>
                  <a-col :span="7">
                    <div style="padding: 25px 10px;">
                      <div style="display: flex;margin-bottom: 20px;">
                        <i class="iconfont icon-cpcckhzs" style="font-size: 46px;color: #454A55;"></i>
                        <div style="margin-left: 10px;line-height: 35px;">
                          <p style="">
                          <span>
                            产品持仓客户总数
                          </span>
                            <span style="color: #999999;">（人）</span>
                          </p>
                          <p><span style="font-weight: bold;font-size: 18px;">{{ cpcckhzs }}</span></p>
                        </div>
                      </div>
                      <div style="display: flex;">
                        <i class="iconfont icon-cccps" style="font-size: 46px;color: #454A55;"></i>
                        <div style="margin-left: 10px;line-height: 35px;">
                          <p style="">
                          <span>
                            持仓产品数
                          </span>
                            <span style="color: #999999;">（只）</span>
                          </p>
                          <p><span style="font-weight: bold;font-size: 18px;">{{ cccps }}</span></p>
                        </div>
                      </div>
                    </div>
                  </a-col>
                  <a-col :span="17">
                    <div>
                      <CnwccSdxppBarChart :dataset="cnwccSdx" :title="'场内外持仓适当性匹配'" v-if="cnwccSdx.length > 0"
                      ></CnwccSdxppBarChart>
                    </div>
                  </a-col>
                </a-row>
              </div>
            </a-col>
            <a-divider type="vertical" style="height: 230px;position: absolute;left: 50%;top: 20%;"/>
            <a-col :span="12">
              <div style="padding: 4px 28px;">
                <div class="title2Style"></div>
                <span class="titleTetxStyle">持有服务咨询产品</span>
              </div>
              <div style="height: 100%;padding: 10px 0 0 42px;">
                <a-row>
                  <a-col :span="7">
                    <div style="padding: 25px 10px;">
                      <div style="display: flex;margin-bottom: 20px;">
                        <i class="iconfont icon-ydyfwkhs" style="font-size: 46px;color: #454A55;"></i>
                        <div style="margin-left: 15px;line-height: 35px;">
                          <p style="">
                          <span>
                            已订阅服务客户总数
                          </span>
                            <span style="color: #999999;">(人)</span>
                          </p>
                          <p><span style="font-weight: bold;font-size: 18px;">{{ ydyfwkhzs }}</span></p>
                        </div>
                      </div>
                      <div style="display: flex;">
                        <i class="iconfont icon-ydyfws" style="font-size: 46px;color: #454A55;"></i>
                        <div style="margin-left: 15px;line-height: 35px;">
                          <p style="">
                          <span>
                            已订阅服务数
                          </span>
                            <span style="color: #999999;">(个)</span>
                          </p>
                          <p><span style="font-weight: bold;font-size: 18px;">{{ ydyfws }}</span></p>
                        </div>
                      </div>
                    </div>

                  </a-col>
                  <a-col :span="15">
                    <div>
                      <a-row>
                        <a-col :span="12">
                          <div style="height: 180px">
                            <CpPieChart :dataset="tgcpSdx" v-if="tgcpSdx.length > 0"
                                        :title="'投顾产品'"></CpPieChart>
                          </div>

                        </a-col>
                        <a-col :span="12">
                          <div style="height: 180px">
                            <CpPieChart :dataset="gmtgcpSdx" v-if="gmtgcpSdx.length > 0"
                                        :title="'公募投顾产品'"></CpPieChart>
                          </div>

                        </a-col>
                      </a-row>
                    </div>
                  </a-col>
                </a-row>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>

    <div style="margin-top: 10px">
      <a-card>
        <template #title>
          <div>
            <div class="titleStyle"></div>
            <a @click="openSdxSjReport()" class="clickable-title">
              <span class="titleTetxStyle">适当性事件</span>
              <LinkOutlined class="link-icon" />
            </a>
          </div>
        </template>
        <div style="height: 260px;">
          <a-row>
            <a-col :span="5">
              <div style="margin-left: 40px;">
                <div style="font-size: 14px;line-height: 30px;margin-bottom: 15px;">
                  <span>事件分布</span>
                  <span style="margin:0 5px">总数量：</span>
                  <span style="font-weight: bold;">{{ sdxsjtj.sjsl }}</span>
                </div>
                <div>
                  <div>
                    <a-row>
                      <a-col :span="6">
                        <span class="yclIcon" style="margin-bottom: 10px;"></span>
                      </a-col>
                      <a-col :span="18">
                        <p>已处理</p>
                        <p>
                          <span style="font-weight: bold;font-size: 18px;margin-right: 10px">{{ sdxsjtj.clsl }}</span>
                          <span style="color: #999999;">/{{
                              parseFloat((sdxsjtj.clzb * 100).toString()).toFixed(2)
                            }}%</span>
                        </p>
                      </a-col>
                    </a-row>
                  </div>
                  <span>
                    <a-progress :percent="sdxsjtj.clzb * 100" style="width: 80%"
                                :stroke-color="{ '0%': '#3686FD','100%': '#00B42A',}"
                                :show-info="false"/>
                  </span>
                </div>
                <div style="margin-top: 15px;">
                  <div>
                    <a-row>
                      <a-col :span="6">
                        <span class="dclIcon" style="margin-bottom: 10px;"></span>
                      </a-col>
                      <a-col :span="18">
                        <p>待处理</p>
                        <p>
                            <span style="font-weight: bold;font-size: 18px;margin-right: 10px">
                              {{ sdxsjtj.dclsl }}</span>
                          <span style="color: #999999;">/{{
                              parseFloat((sdxsjtj.dclzb * 100).toString()).toFixed(2)
                            }}%</span>
                        </p>
                      </a-col>
                    </a-row>
                  </div>
                  <span>
                    <a-progress :percent="sdxsjtj.dclzb * 100" style="width: 80%"
                                :stroke-color="{ '0%': '#FF7800','100%': '#FF1919',}"
                                :show-info="false"/>
                  </span>
                </div>
              </div>
            </a-col>
            <a-col :span="19">
              <div>
                <SdxSjPieChart :dataset="sdxsjArrDcl"
                               :title="'适当性事件'"
                               :pieTitle="sdxsjArrZl" v-if="sdxsjArrDcl.length!=0">
                </SdxSjPieChart>
              </div>
            </a-col>
          </a-row>
        </div>
        <div style="position: relative;float: right;bottom: 10px;right: 15px;">
          <a style="color: #BF935F;" @click="openMore('sj')">更多
            <span class="zkIcon" style="float: right;margin-top: 1px;"></span></a>
        </div>
      </a-card>

    </div>

    <DetailModal v-model:open="modalVisible"
                 :title="modalTitle" :lastTradingDay="LastTradingDay"
                 :type="modalType"></DetailModal>
  </div>
</template>

<script>
import {CaretUpOutlined, ContainerOutlined, LinkOutlined} from '@ant-design/icons-vue';
import horizontalBarChart from "./HorizontalBarChart";
import AccountBarChart from "@views/index/AccountBarChart.vue";
import CpPieChart from "@views/index/CpPieChart.vue";
import SdxSjPieChart from "@views/index/SdxSjPieChart.vue";
import SdxBarChart from "@views/index/SdxBarChart.vue";
import KhSdxfbPieChart from "@views/index/KhSdxfbPieChart.vue";
import khSdxfbLineChart from "@views/index/KhSdxfbLineChart.vue";
import DetailModal from "@views/index/DetailsModal.vue";
import {commonApi} from "@/api/common";
import {message} from "ant-design-vue";
import "@/assets/css/iconfont.css";
import CnwccSdxppBarChart from "@views/index/CnwccSdxppBarChart.vue";
import {dictApi} from "@/api/xtgl/dict";
import YwzhSdxPolarChart from "@views/index/YwzhSdxPolarChart.vue";
import dayjs from "dayjs";

export default {
  name: "index",
  components: {
    YwzhSdxPolarChart,
    CnwccSdxppBarChart,
    KhSdxfbPieChart,
    khSdxfbLineChart,
    SdxBarChart,
    SdxSjPieChart,
    CpPieChart,
    AccountBarChart,
    ContainerOutlined,
    CaretUpOutlined,
    LinkOutlined,
    horizontalBarChart,
    DetailModal,
  },
  data() {
    return {
      modalVisible: false,
      modalTitle: '',
      modalType: '',
      sdxNameArr: ['不适当', '适当'],//适当性事件名称数组
      sdxsjtj: {sjsl: 0, clsl: 0, clzb: 0, dclsl: 0, dclzb: 0},//事件分布 适当性事件统计
      sdxsjArr: [{sjid: 0, sjsl: 0, clsl: 0, clzb: 0, dclsl: 0, dclzb: 0},],//事件分布 适当性事件统计数组
      sdxsjArrDcl: [/*{name:'待处理',value:100},{name:'待处理',value:100},{name:'待处理',value:100},{name:'待处理',value:100},{name:'待处理',value:100},*/],//事件分布 适当性事件统计数组-待处理
      sdxsjArrZl: [/*{name:'待处理总量',value:100},{name:'待处理总量',value:100},{name:'待处理总量',value:100},{name:'待处理总量',value:100},{name:'待处理总量',value:100},*/],//事件分布 适当性事件统计数组-总量
      cpcckhzs: 0,//产品持仓客户总数
      cccps: 0,//持仓产品数
      cnwccSdx: [],//场内外持仓适当性匹配
      ydyfwkhzs: 0,//已订阅服务客户总数
      ydyfws: 0,//已订阅服务数
      tgcpSdx: [],//投顾产品持仓-适当性事件
      gmtgcpSdx: [],//公募投顾产品持仓-适当性事件
      zkhs: {zs: 0, rzz: 0, yzz: 0},//客户适当性分布-总客户数
      zkhsLine: [],//客户适当性分布-总客户数折线图数据
      zhlxMap: {zs: 0, zb: 0},//客户适当性分布-账户类型
      fxdjMap: {zs: 0, zb: 0},//客户适当性分布-风险等级
      sdxztqkArr1: [],//适当性总体情况Top1~3
      sdxztqkArr2: [],//适当性总体情况Top4~6
      zhlx: "1",//账户类型下拉框值，默认A类专业投资者
      fxdj: "1",//风险等级下拉框值，默认选择1-保守型最低类别
      zhztzczb: 0, //账户状态正常指标
      zhztyczb: 0, //账户状态异常指标
      zhztzcgs: 1110, //账户状态正常数量
      zhztycgs: 0, //账户状态异常数量
      zhfxqfxdjzczb: 0, //反洗钱风险等级正常指标
      zhfxqfxdjyczb: 0, //反洗钱风险等级异常指标
      zhfxqfxdjzcgs: 1110, //反洗钱风险等级正常数量
      zhfxqfxdjycgs: 0, //反洗钱风险等级异常数量

      zjyxqzczb: 0, //证件有效期正常指标
      zjyxqyczb: 0, //证件有效期异常指标
      zjyxqzcgs: 0, //证件有效期正常数量
      zjyxqycgs: 0, //证件有效期异常数量
      fxcpyxzczb: 0, //风险测评有效正常指标
      fxcpyxyczb: 0, //风险测评有效异常指标
      fxcpyxzcgs: 0, //风险测评有效正常数量
      fxcpyxycgs: 0, //风险测评有效异常数量
      zhqtxxzczb: 0, //其他信息信息正常指标
      zhqtxxyczb: 0, //其他信息信息异常指标
      zhqtxxzcgs: 0, //其他信息信息正常数量
      zhqtxxycgs: 0, //其他信息信息异常数量
      bsdkhs: 0,//不适当客户数
      ywbsds: 0,//业务不适当数
      bsdxyyqs: 0,//不适当协议签署-已签署
      bsdxydqs: 0,//不适当协议签署-待签署
      khh: "",//客户号
      zytzzfxdjfbArr: [],//[['专业投资者风险等级分布'], [8,10,1,6,13,49], [1.68,2,0.21,1.26,2.73,4.13]],//专业投资者风险等级分布
      pttzzfxdjfbArr: [],//[['普通投资者风险等级分布'], [8,10,1,6,13,49], [1.68,2,0.21,1.26,2.73,4.13]],//普通投资者风险等级分布
      tzzfxdjfbName: [],//["保守型(最低类别)", "保守型", "谨慎型", "稳健型", "积极型", "激进型"],//投资者风险等级分布名称
      dictArray: [],//字典数组
      rzrqArr: {total: 0, num: [0, 0, 0]},//业务账户适当性情况-融资融券
      gpqqArr: {total: 0, num: [0, 0, 0]},//业务账户适当性情况-股票期权
      currDate: dayjs().format("YYYYMMDD"),//当天日期
      LastTradingDay: dayjs().subtract(2, 'day').format('YYYYMMDD') + '',//全局上个交易日 默认昨天
      abcTzzData: [], // A/B/C类专业投资者数据
    };
  },
  computed: {
    // 过滤掉普通投资者选项
    tzzflOptions() {
      return this.dictArray?.SDX_TZZFL?.filter(item => item.ibm !== "0") || [];
    },
    //客户适当性分布-账户类型饼图数据
    zhlxPieDataset() {
      let arr = [];
      if (this.zhlxMap && this.zhlxMap.zs >= 0 && this.abcTzzData.length > 0) {
        // 获取当前选中的投资者类型名称
        let name = 'C类专业投资者';
        this.dictArray?.SDX_TZZFL?.forEach((item) => {
          if (item.ibm == this.zhlx) {
            name = item.note;
          }
        });

        // 计算专业投资者总数(A+B+C类)
        let totalProfessional = 0;
        this.abcTzzData.forEach(item => {
          totalProfessional += Number(item.zs || 0);
        });

        // 获取当前选中类型的数量
        const currentValue = Number(this.zhlxMap.zs);

        arr.push({name: name, value: currentValue});
        arr.push({name: '非' + name, value: (totalProfessional - currentValue)});
      }
      return arr;
    },
    //客户适当性分布-风险等级饼图数据
    fxdjPieDataset() {
      let arr = [];
      if (this.fxdjMap && this.zkhs && this.fxdjMap.zs >= 0 && this.zkhs.zs > 0) {
        let name = 'C1-保守型';
        this.dictArray?.SDX_FXCSNL?.forEach((item) => {
          if (item.ibm == this.fxdj) {
            name = item.note;
          }
        });
        arr.push({name: name, value: this.fxdjMap.zs});
        arr.push({name: '非' + name, value: (Number(this.zkhs.zs) - Number(this.fxdjMap.zs))});
      }
      return arr;
    }
  },
  methods: {
    //打开模态窗口
    openMore(type) {
      if (type == 'sj') {
        this.modalTitle = '适当性事件情况';
      } else if (type == 'ztqk') {
        this.modalTitle = '业务适当性整体情况';
      } else if (type == 'zhzt') {
        this.modalTitle = '账户状态';
      } else if (type == 'fxqfxdj') {
        this.modalTitle = '反洗钱风险等级';

      } else if (type == 'zjyxq') {
        this.modalTitle = '证件有效期';
      } else if (type == 'fxcpyxq') {
        this.modalTitle = '风险测评有效';
      } else if (type == 'qtxx') {
        this.modalTitle = '其他信息';
      } else if (type == 'ywbsdxeqsqk') {
        this.modalTitle = '业务不适当协议签署情况';
      }
      this.modalType = type;
      this.modalVisible = true;
    },
    getSdxsjArr() {
      //获取产品持仓适当性情况
      commonApi.executeAMS(
          "sdx.query.ISdxsjService",
          "sdxsjtj",
          {tjwd: "2", ksrq: this.currDate, jsrq: this.currDate},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.sdxsjArr = res.records || []
        if (this.sdxsjArr.length >= 5 && !this.modalVisible) { //前五条数据&&不处于模态框状态
          for (let i = 0; i < 5; i++) {
            this.sdxsjArrDcl.push({
              name: '待处理',
              value: this.sdxsjArr[i].dclsl
            });
            this.sdxsjArrZl.push({
              name: this.sdxsjArr[i].sjmc,
              value: this.sdxsjArr[i].clsl,//已处理数量
            });
          }
        }
      }).finally(() => {
      })
    },
    //适当性事件-事件分布(查询适当性事件统计数据)
    getSdxsj() {
      commonApi.executeAMS(
          "sdx.query.ISdxsjService",
          "sdxsjtj",
          {ksrq: this.currDate, jsrq: this.currDate},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.sdxsjtj = res.records[0] || []
      }).finally(() => {
      })
    },
    //持仓-产品持仓适当性情况(查询其他持仓产品适当性事件统计)
    getQtcccptj() {
      commonApi.executeAMS(
          "sdx.query.ICpccsdxService",
          "qtcccpSdxtj",
          {rq: this.LastTradingDay},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        let data = res.records || [];
        if (data.length > 0) {
          let nameArr = [];
          let sdslArr = [];
          let bsdslArr = [];
          data.forEach((item) => {
            //     let valueArr = [item.bsdsl, item.sdsl];
            let sdArr = {name: '适当', value: item.sdsl};
            let bsdArr = {name: '不适当', value: item.bsdsl};
            let ywxt = item.ywxt;
            if (ywxt == '1003') {
              this.cpcckhzs += item.khsl;
              this.cccps += item.ccsl;
              sdslArr.push(item.sdsl);
              bsdslArr.push(item.bsdsl);
              nameArr.push('场外产品持仓');
            } else if (ywxt == '1000') {
              this.cpcckhzs += item.khsl;
              this.cccps += item.ccsl;
              sdslArr.push(item.sdsl);
              bsdslArr.push(item.bsdsl);
              nameArr.push('开放式基金持仓');
            } else if (ywxt == '1004') {
              this.tgcpSdx = [sdArr, bsdArr];
              this.ydyfwkhzs += item.khsl;
              this.ydyfws += item.ccsl;
            } else if (ywxt == '1005') {
              this.gmtgcpSdx = [sdArr, bsdArr];
              this.ydyfwkhzs += item.khsl;
              this.ydyfws += item.ccsl;
            }
          });
          this.cnwccSdx = [nameArr, [sdslArr, bsdslArr]];
        }
      }).finally(() => {
      })
    },
    //客户适当性分布(查询客户总数)
    getKhzs() {
      commonApi.executeAMS(
          "sdx.query.ICxkhsdxfbService",
          "khzs",
          {type: 'KHZH'},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.zkhs = res.records[0] || [];
      }).finally(() => {
      })
    },
    //客户适当性分布(查询账户类型)
    getZhlx() {
      commonApi.executeAMS(
          "sdx.query.ICxkhsdxfbService",
          "getTzzflxx",
          {type: this.zhlx},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.zhlxMap = res.records[0] || [];
      }).finally(() => {
      })
    },
    //客户适当性分布(查询风险等级)
    getFxdj() {
      commonApi.executeAMS(
          "sdx.query.ICxkhsdxfbService",
          "getFxcsnlxx",
          {type: this.fxdj},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.fxdjMap = res.records[0] || [];
      }).finally(() => {
      })
    },
    //客户适当性分布(客户总数折线图数据)
    getZkhsDate() {
      commonApi.executeAMS(
          "sdx.query.ICxkhsdxfbService",
          "zkhsDate",
          {type: 'KHZH_SL'},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        let lineData = res.records || [];
        let nameArr = [];
        let valueArr = [];
        lineData.forEach((item) => {
          nameArr.push(item.rq);
          valueArr.push(Number(item.result));
        });
        this.zkhsLine.push(nameArr, valueArr)
      }).finally(() => {
      })
    },
    //权限适当性整体情况top6
    getSdxztqkTop6() {
      commonApi.executeAMS(
          "sdx.query.IYktywsdxService",
          "ywktSdxtj4cxywlb",
          {rq: this.LastTradingDay},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        let records = res.records || [];
        if (records.length >= 6) {
          const top1to3 = records.slice(0, 3).reverse();
          const top4to6 = records.slice(3, 6).reverse();
          let sdxNameArr1 = [], sdxNameArr2 = [];
          let sdArr1 = [], sdArr2 = [], sdslArr1 = [], sdslArr2 = [];
          let bsdArr1 = [], bsdArr2 = [], bsdslArr1 = [], bsdslArr2 = [];
          let btgArr1 = [], btgArr2 = [], btgslArr1 = [], btgslArr2 = [];
          let ktslArr1 = [], ktslArr2 = [];
          top1to3.forEach((item) => {
            sdxNameArr1.push(item.cxywmc);
            sdArr1.push(parseFloat((item.sdzb * 100).toString()).toFixed(2));
            bsdArr1.push(parseFloat((item.bsdzb * 100).toString()).toFixed(2));
            btgArr1.push(parseFloat((item.btgzb * 100).toString()).toFixed(2));
            ktslArr1.push(item.ktsl);
            sdslArr1.push(item.sdsl);
            bsdslArr1.push(item.bsdsl);
            btgslArr1.push(item.btgsl);
          });
          this.sdxztqkArr1.push(sdxNameArr1, [sdArr1, bsdArr1, btgArr1], [sdslArr1, bsdslArr1, btgslArr1, ktslArr1]);

          top4to6.forEach((item) => {
            sdxNameArr2.push(item.cxywmc);
            sdArr2.push(parseFloat((item.sdzb * 100).toString()).toFixed(2));
            bsdArr2.push(parseFloat((item.bsdzb * 100).toString()).toFixed(2));
            btgArr2.push(parseFloat((item.btgzb * 100).toString()).toFixed(2));
            ktslArr2.push(item.ktsl);
            sdslArr2.push(item.sdsl);
            bsdslArr2.push(item.bsdsl);
            btgslArr2.push(item.btgsl);
          });
          this.sdxztqkArr2.push(sdxNameArr2, [sdArr2, bsdArr2, btgArr2], [sdslArr2, bsdslArr2, btgslArr2, ktslArr2]);
        }

      }).finally(() => {
      })
    },
    //账户适当性情况
    getZhsdxqk() {
      commonApi.executeAMS(
          "sdx.khgl.IKhxxcxService",
          "khxxtj",
          {},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        let record = res.records[0] || [];
        this.zhztzczb = record.zhztzczb; //账户状态正常指标
        this.zhztyczb = record.zhztyczb;//账户状态异常指标
        this.zhztzcgs = record.zhztzcgs;//账户状态正常数量
        this.zhztycgs = record.zhztycgs;//账户状态异常数量
        this.zhfxqfxdjzczb = record.zhfxqfxdjzczb;//反洗钱风险等级正常指标
        this.zhfxqfxdjyczb = record.zhfxqfxdjyczb;//反洗钱风险等级异常指标
        this.zhfxqfxdjzcgs = record.zhfxqfxdjzcgs;//账户反洗钱风险等级正常数量
        this.zhfxqfxdjycgs = record.zhfxqfxdjycgs;//账户反洗钱风险等级异常数量

        this.zjyxqzczb = record.zjyxqzczb;//证件有效期正常指标
        this.zjyxqyczb = record.zjyxqyczb;//证件有效期异常指标
        this.zjyxqzcgs = record.zjyxqzcgs;//证件有效期正常数量
        this.zjyxqycgs = record.zjyxqycgs;//证件有效期异常数量
        this.fxcpyxzczb = record.fxcpyxzczb;//风险测评有效正常指标
        this.fxcpyxyczb = record.fxcpyxyczb;//风险测评有效异常指标
        this.fxcpyxzcgs = record.fxcpyxzcgs;//风险测评有效正常数量
        this.fxcpyxycgs = record.fxcpyxycgs;//风险测评有效异常数量
        this.zhqtxxzczb = record.zhqtxxzczb;//其他信息信息正常指标
        this.zhqtxxyczb = record.zhqtxxyczb;//其他信息信息异常指标
        this.zhqtxxzcgs = record.zhqtxxzcgs;//其他信息信息正常数量
        this.zhqtxxycgs = record.zhqtxxycgs;//其他信息信息异常数量
      }).finally(() => {
      })
    },
    //业务不适当协议签署情况
    getYwbsdxyqsqk() {
      commonApi.executeAMS(
          "sdx.query.IYktywsdxService",
          "qtyktywBsdxtj",
          {rq: this.LastTradingDay},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        let record = res.records[0] || [];
        this.bsdxydqs = record.wqsbsdxysl;
        this.bsdxyyqs = record.yqsbsdxysl;
        this.ywbsds = record.ktsl;
        this.bsdkhs = record.khsl;
      }).finally(() => {
      })
    },
    //投资者风险等级分布
    getTzzfxdjfb() {
      commonApi.executeAMS(
          "sdx.khgl.IKhxxcxService",
          "tzzcfbcx",
          {},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        } else {
          let record = res.records[0] || [];
          this.tzzfxdjfbName = record.fxcsnlArray;
          this.pttzzfxdjfbArr.push(['普通投资者风险等级分布'], record.pttzrCountArray, record.pttzrPercentArray);
          this.zytzzfxdjfbArr.push(['专业投资者风险等级分布'], record.zytzrCountArray, record.zytzrPercentArray);
        }
      }).finally(() => {
      })
    },

    //业务账户适当性情况
    getYwzhsdxqk() {
      commonApi.executeAMS(
          "sdx.query.IYwxtsdxService",
          "tjYwxtSdxByRq",
          {rq: this.LastTradingDay},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        } else {
          let records = res.records || [];
          records.forEach((item) => {
            let numArr = [item.sdNum, item.bsdNum, item.btgNum];
            let total = item.total;
            if (item.ywxt === 1001) {//融资融券
              this.rzrqArr.total = total;
              this.rzrqArr.num = numArr;
            } else if (item.ywxt === 1002) {//股票期权
              this.gpqqArr.total = total;
              this.gpqqArr.num = numArr;
            }
          });
        }
      }).finally(() => {
      })
    },
    //打开业务适当性报表
    openSdxReport() {
      const url = `/UIProcessor?Table=xnQTYKTYWSDX`;
      window.open(url, '_blank');
    },

    //打开产品持仓适当性报表
    openCpccSdxReport() {
      const url = `/UIProcessor?Table=QTYCCCPSDX`;
      window.open(url, '_blank');
    },

    //打开适当性事件报表
    openSdxSjReport() {
      const url = `/UIProcessor?Table=VSDXSJ_CX`;
      window.open(url, '_blank');
    },

    //获取上一个交易日
    getJyr() {
      commonApi.executeAMS(
          "sdx.query.IJyrService",
          "cxjyr",
          {zrr: this.currDate, ts: -1},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        } else {
          this.LastTradingDay = res?.jyr + '' || dayjs().subtract(1, 'day').format('YYYYMMDD') + '';
        }
      }).finally(() => {
        this.getSjzd();
        this.getSdxsj();
        this.getSdxsjArr();
        this.getQtcccptj();
        this.getKhzs();
        this.getFxdj();
        this.getZkhsDate();
        this.getSdxztqkTop6();
        this.getZhsdxqk();
        this.getYwbsdxyqsqk();
        this.getTzzfxdjfb();
        this.getYwzhsdxqk();
      })
    },

    //账户类型切换下拉框
    handleZhlxChange() {
      this.getZhlx();
    },
    //风险等级切换下拉框
    handleFxdjChange() {
      this.getFxdj();
    },
    getSjzd() {
      dictApi.cxsjzd({fldm: "SDX_FXCSNL;SDX_TZZFL"}).then((res) => {
        if (res.code > 0) {
          this.dictArray = res.sjzd;
          // 默认选择第一个专业投资者类型
          if (this.tzzflOptions.length > 0) {
            this.zhlx = this.tzzflOptions[0].ibm;
            this.getZhlx();
          }
        }
      });
    },
    // 获取A/B/C类专业投资者数据
    getAbcTzzData() {
      commonApi.executeAMS(
          "sdx.query.ICxkhsdxfbService",
          "getAbcTzzflxx",
          {},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.abcTzzData = res.records || [];
      }).finally(() => {
      });
    },
  },
  mounted() {
  },
  created() {
    this.getJyr();
    this.getAbcTzzData(); // 获取A/B/C类专业投资者数据
  }
}
</script>

<style scoped>
.ywbsdxyqsqk {
  width: 180px;
  height: 120px;
  border-radius: 6px;
  padding: 20px 0 0 20px;
  position: relative;
}

.ywbsdxyqsqk i {
  font-size: 75px;
  position: absolute;
  right: 0px;
  bottom: -25px;
}

.qxsdx_name {
  color: #FFFFFF;
  line-height: 22px;
  margin: 10px 0 0 15px;
}

.qxsdx_value {
  color: #FFFFFF;
  line-height: 22px;
  margin: 0 0 0 15px;
}

.qxsdx_sd {
  width: 130px;
  height: 60px;
  background: linear-gradient(90deg, #3686FD 0%, #00B42A 99%);
  border-radius: 4px;
}

.qxsdx_bsd {
  width: 130px;
  height: 60px;
  background: linear-gradient(90deg, #FF7800 0%, #FF1919 99%);
  border-radius: 4px;
}

.titleStyle {
  float: left;
  margin-top: 2px;

  width: 4px;
  height: 18px;
  background: #B48A3B;
  border-radius: 2px;
}

.title2Style {
  width: 10px;
  height: 10px;
  background: #BF935F;
  float: left;
  margin-top: 5px;
  transform: rotate(45deg);
}

.titleTetxStyle {
  font-weight: 400;
  color: #333333;
  line-height: 18px;
  margin-left: 10px;
}

.zc {
  font-weight: 400;
  font-size: 14px;
  color: #888888;
  line-height: 30px;
  margin-left: 15px;
}

.yc {
  font-weight: 400;
  font-size: 14px;
  color: #888888;
  line-height: 30px;
  margin-left: 15px;
}

.sdxqkValue {
  color: #333333;
  text-align: right;
  width: 50px;
  display: inline-block;
  vertical-align: middle;
}

.sdxqkPercent {
  font-weight: 400;
  font-size: 14px;
  color: #888888;
  text-align: right;
  width: 50px;
  display: inline-block;
  vertical-align: middle;
}

.zctb {
  width: 8px;
  height: 8px;
  /*background: linear-gradient(90deg, #3686FD 0%, #00B42A 99%);*/
  background: #36A6FC;
  border-radius: 4px;
  display: inline-block;
  margin-right: 5px;
}

.yctb {
  width: 8px;
  height: 8px;
  /*background: linear-gradient(90deg, #FF7800 0%, #FF1919 99%);*/
  background: #FFC730;
  border-radius: 4px;
  display: inline-block;
  margin-right: 5px;
}

.rzz {
  font-weight: 400;
  font-size: 12px;
  color: #888888;
  line-height: 18px;
}

.yzz {
  font-weight: 400;
  font-size: 12px;
  color: #888888;
  line-height: 18px;
}

.zzsl {
  font-weight: 400;
  font-size: 14px;
  color: #FF0000;
  line-height: 18px;
  margin: 0 5px;
}

.khsumIcon {
  width: 42px; /* 或你需要的尺寸 */
  height: 42px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/khsum.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  float: left;
  background-color: #FFF7EF;
  border-radius: 8px;
  background-size: 56%;
}

.zhlxIcon {
  width: 42px; /* 或你需要的尺寸 */
  height: 42px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/zhlx.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  float: left;
  background-color: #EFFBFF;
  border-radius: 8px;
  background-size: 56%;
}

.fxdjIcon {
  width: 42px; /* 或你需要的尺寸 */
  height: 42px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/fxdj.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  float: left;
  background-color: #F7EFFF;
  border-radius: 8px;
  background-size: 56%;
}

.jyqxSumIcon {
  width: 30px; /* 或你需要的尺寸 */
  height: 30px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/jyqxsum.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  float: left;
}

.lrqxSumIcon {
  width: 25px; /* 或你需要的尺寸 */
  height: 25px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/lrqxsum.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  float: left;
}

.zkIcon {
  width: 19px; /* 或你需要的尺寸 */
  height: 19px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/zk.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
}

.rzrqIcon {
  width: 44px; /* 或你需要的尺寸 */
  height: 44px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/rzrq.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
  vertical-align: middle;
}

.gpqqIcon {
  width: 44px; /* 或你需要的尺寸 */
  height: 44px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/gpqq.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
  vertical-align: middle;
}

.cwyeIcon {
  width: 22px; /* 或你需要的尺寸 */
  height: 22px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/cwyw.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
  vertical-align: middle;
}

.yclIcon {
  width: 50px; /* 或你需要的尺寸 */
  height: 50px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/ycl.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: block;
}

.dclIcon {
  width: 50px; /* 或你需要的尺寸 */
  height: 50px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/dcl.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: block;
}

.cckhzl {
  width: 46px; /* 或你需要的尺寸 */
  height: 42px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/khzs.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: block;
}

.cwcpcc {
  width: 46px; /* 或你需要的尺寸 */
  height: 42px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/cpcc.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: block;
}

.kfsjjcc {
  width: 46px; /* 或你需要的尺寸 */
  height: 42px; /* 或你需要的尺寸 */
  background-image: url("~@/assets/images/jjcc.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: block;
}

.zhsdxqk {
  border-bottom: none;
  border-left: none;
  border-top: none;
  border-radius: 8px 0 0 8px;
}

.ywzhsdxqk {
  border: none;
  border-radius: 0 8px 8px 0;
}

:deep(.khsdxselect .ant-select-selector) {
  background-color: rgb(242, 243, 245);
  border-radius: 10px;
}

:deep(.ant-card-body) {
  padding: 10px;
}

:deep(.ant-card-head) {
  min-height: 35px;
}

:deep(.ant-card .ant-card-head-title ) {
  padding: 13px 0;
}

.clickable-title {
  color: #BF935F;
  text-decoration: none;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.clickable-title:hover {
  text-decoration: underline;
}

.link-icon {
  margin-left: 5px;
  font-size: 14px;
}
</style>
