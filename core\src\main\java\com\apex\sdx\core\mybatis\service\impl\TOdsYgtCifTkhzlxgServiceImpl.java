package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.resp.common.Response;
import com.apex.sdx.core.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhzlxg;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhzlxgService;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTkhzlxgMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class TOdsYgtCifTkhzlxgServiceImpl extends ServiceImpl<TOdsYgtCifTkhzlxgMapper, TOdsYgtCifTkhzlxg>
    implements TOdsYgtCifTkhzlxgService{

    @Override
    public Page<TOdsYgtCifTkhzlxg> queryByKhh(String khh, Integer ksrq, Integer jsrq, boolean isSearchCount, int pagesize, int pagenum) {
        Page<TOdsYgtCifTkhzlxg> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);

        try {
            LambdaQueryWrapper<TOdsYgtCifTkhzlxg> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TOdsYgtCifTkhzlxg::getKhh, khh);
            wrapper.ge(ksrq != null, TOdsYgtCifTkhzlxg::getXgrq, ksrq);
            wrapper.le(jsrq != null, TOdsYgtCifTkhzlxg::getXgrq, jsrq);
            wrapper.orderByDesc(TOdsYgtCifTkhzlxg::getId);
            this.page(page, wrapper);
            return page;

        } catch (Exception e) {
            String note = String.format("获取客户资料变更流水失败[%s]", e.getMessage());
            log.error(note, e);
            throw new BusinessException(Response.buildErrorMsg(-601, note));
        }
    }
}




