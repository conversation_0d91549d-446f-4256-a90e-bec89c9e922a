package com.apex.sdx.core.exception;

import com.apex.sdx.core.result.R;
import com.apex.sdx.core.result.ResponseEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @Date 2023/8/24 13:31
 * @Description: TODO
 */
@Slf4j
@Component
@RestControllerAdvice
public class UnifiedExceptionHandler {
    /**
     * 未定义异常
     */
    @ExceptionHandler(value = Exception.class)
    public R handleException(Exception e) {
        log.error(e.getMessage(), e);
        return R.error();
    }

    /**
     * 特定异常
     */
    @ExceptionHandler(BusinessException.class)
    public R handleBadSqlGrammarException(BusinessException e){
        log.error(e.getMessage(), e);
        return R.setResult(ResponseEnum.ERROR_BUSINESS);
    }
}
