ams:
  config:
    enabled: false
  # 证书信任域
  authority: livebos-server
  # 根证书
  ca:
    certFile: classpath:cert/ca.pem
  # 客户端证书
  client:
    tls:
      protocols: TLSv1.2
    certFile: classpath:cert/client.pem
    keyFile: classpath:cert/client.pkcs8.pem
    keepAliveWithoutCalls: true #开启心跳检测，TCP长连接在长期空闲的状态下，会被强制回收。
    keepAliveTime: 600 #建议设置为合理数值，要小于现场空闲网络资源的回收时间
  # 服务端证书
  server:
    certFile: classpath:cert/server.pem
    keyFile: classpath:cert/server.pkcs8.pem
    namespace: sdx #ams注册服务的默认命名
    #namespace：ecif #服务部署命名空间
    minPingTime: 300 #服务端允许客户端发送ping信号最短时间，单位秒,默认值300秒(5分钟).配置时间要小于服务消费端的keepAliveTime时间
    port: 52276
  #注册中心
  registry:
    protocol: zk
    #开发环境
    address: ***************:2181
    #本机测试
    #address: ***********:2181
    #registry:
    #protocol: eureka
    #address: http://************:7001/eureka
  metrics:
    prometheus:
      sd:
        type: consul
      port: 40002
  #      trust-scrape-ips: ************
  namespace: sdx #ams调用服务的默认命名
