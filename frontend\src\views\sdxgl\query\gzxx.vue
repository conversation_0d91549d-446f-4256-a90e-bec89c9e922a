<template>
  <div id="root" class="sdxtc_nav container_div">
    <div class="sdx_bus_con">
      <a-form ref="ruleForm" :labelCol="{ style: 'width: 80px' }" :model="sdxInfo" :rules="rules" layout="inline">
        <a-form-item label="产品代码" prop="cpgzdmName">
          <a-input v-model="sdxInfo.cpgzdmName" allowClear class="inputClass" placeholder="请选择产品代码"
                   @click="dialogCpgzdmOpen">
            <template #suffix>
              <search-outlined/>
            </template>
          </a-input>
        </a-form-item>
        <a-form-item label="投资者分类">
          <a-select ref="tzzflRef" v-model="sdxInfo.tzzfl" allowClear class="inputClass" placeholder="请选择投资者分类">
            <a-select-option value="0">普通投资者</a-select-option>
            <a-select-option value="1">专业投资者</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="客户类别">
          <a-select v-model="sdxInfo.khlb" allowClear class="inputClass" placeholder="请选择客户类别">
            <a-select-option
                v-for="khlb in khlbList"
                :key="khlb.IBM.toString()"
                :value="khlb.IBM.toString()"
            >{{ khlb.NOTE }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space size="large">
            <a-button shape="round" type="primary" @click="searchClick">查询</a-button>
            <a-button shape="round" type="primary" @click="exportExl">导出</a-button>
          </a-space>
        </a-form-item>
      </a-form>
      <a-table
          :columns="tableColumns"
          :data-source="tableList"
          :pagination="{position:'bottom',showQuickJumper:true,showSizeChanger:true}"
          :row-selection="{ selectedRowKeys: currentRowKeys,onChange: handleSelectionChange,selections : true }"
      />
    </div>

    <a-modal :visible="cpgzdmVisible" cancelText="取消" okText="确定" title="产品代码" width="70%"
             @cancel="cpgzdmVisible = false" @ok="dialogCpgzdmClose">
      <a-form layout="inline">
        <a-form-item>
          <a-input v-model="cpdm" allowClear placeholder="请输入产品代码"></a-input>
        </a-form-item>
        <a-form-item>
          <a-input v-model="cpmc" allowClear placeholder="请输入产品名称"></a-input>
        </a-form-item>
        <a-form-item>
          <a-button round type="primary" @click="searchClickCpdm">查询</a-button>
        </a-form-item>
      </a-form>
      <a-table
          :columns="cpdmColumns"
          :data-source="cpgzdmList"
          :pagination="{position:'bottom',showQuickJumper:true,showSizeChanger:true}"
          :row-selection="{ onChange: handleSelectionChangeCpdm,selections : true ,type:'radio'}"
      />
    </a-modal>

  </div>
</template>

<script>
import '../../../assets/css/sdxConfig.css';
import $ from 'jquery';
import gzxx from "@/api/query/gzxx";

export default {
  name: "gzxx",
  data() {
    return {
      currentRowKeys: [],
      tableColumns: [
        {
          title: '产品名称',
          dataIndex: 'cpmc',
          key: 'cpmc',
        },
        {
          title: '投资者分类名称',
          dataIndex: 'tzzflmc',
          key: 'tzzflmc',
        },
        {
          title: '适用范围名称',
          dataIndex: 'syfwmc',
          key: 'syfwmc',
        },
        {
          title: '参数代码',
          dataIndex: 'csdm',
          key: 'csdm',
        },
        {
          title: '参数名称',
          dataIndex: 'csmc',
          key: 'csmc',
        },
        {
          title: '运算符名称',
          dataIndex: 'ysfmc',
          key: 'ysfmc',
        },
        {
          title: '参数值',
          dataIndex: 'csz',
          key: 'csz',
        }],
      cpdmColumns: [
        {
          title: '产品代码',
          dataIndex: 'CPDM',
          key: 'CPDM',
        },
        {
          title: '产品名称',
          dataIndex: 'CPMC',
          key: 'CPMC',
        }],
      tableList: [],
      khlbList: [],
      cpgzdmList: [],
      currentRow: '',
      cpgzdmVisible: false,
      czzd: '',
      fqr: '',
      sdxInfo: {
        cpgzdmName: "",
        tzzfl: "",
        khlb: "",
        cpgzdm: ''
      },
      cpdm: "",
      cpmc: "",
      exportExlFlag: false,
      rules: {
        cpgzdmName: [
          {required: true, message: '请选择产品代码', trigger: 'blur'},
        ]
      }
    };
  },
  methods: {
    //产品代码
    dialogCpgzdmOpen: function () {
      this.cpgzdmVisible = true;
    },
    dialogCpgzdmClose: function () {
      if (this.currentRow) {
        var _this = this;
        _this.sdxInfo.cpgzdm = _this.currentRow.CPDM;
        _this.sdxInfo.cpgzdmName = _this.currentRow.CPDM + '-' + _this.currentRow.CPMC;

      }
      this.$refs.ruleForm.validateField('cpgzdmName');
      this.cpgzdmVisible = false;
    },
    handleSelectionChangeCpdm: function (selectedRowKeys, selectedRows) {
      this.currentRow = selectedRows;
    },
    searchClickCpdm: function () {
      this.findCpxxPage();
    },
    findCpxxPage: function () {
      let _this = this;
      gzxx.findXyxxCpxxPage({cpmc: _this.cpmc, cpdm: _this.cpdm}).then((ret) => {
        if (ret.code < 0) {
          _this.$message.error(ret.note || ret.message);
        } else {
          _this.cpgzdmList = ret.data.cpdmList;
        }
      });
    },

    searchClick: function () {
      this.$refs.ruleForm.validate((valid) => {
        if (!valid) {
          return false;
        }
        this.findSdxGzxx();
        this.exportExlFlag = true;
      });

    },
    exportExl: function () {
      if (this.exportExlFlag) {
        this.exportExlData();
      } else {
        this.$message.error('请先进行查询之后，在进行导出');
      }
    },
    exportExlData: function () {
      var _this = this;
      var exportList = {
        cpdm: _this.sdxInfo.cpgzdm,
        tzzfl: _this.sdxInfo.tzzfl,
        khlb: _this.sdxInfo.khlb,
        fqr: _this.fqr,
        czzd: _this.czzd,
        cpgzdmName: _this.sdxInfo.cpgzdmName.replace("-", ""),
        tzzflName: _this.$refs.tzzflRef.selectedLabel,
        khlbName: _this.$refs.khlbRef.selectedLabel
      };
      var url = "/bss/sdx/json/exportExlData.sdo";//TODO
      var form = $("<form></form>").attr("action", url).attr("method", "post");
      form.append($("<input></input>").attr("type", "hidden").attr("name", "exportList").attr("value", JSON.stringify(exportList)));
      form.appendTo('body').submit().remove();
    },
    handleSelectionChange: function (selectedRowKeys, selectedRows) {
      this.multipleSelection = selectedRows;
      this.currentRowKeys = selectedRowKeys;
    },
    findSdxGzxx: function () {
      let _this = this;
      gzxx.findSdxGzxx({
        cpdm: _this.sdxInfo.cpgzdm,
        tzzfl: _this.sdxInfo.tzzfl,
        khlb: _this.sdxInfo.khlb,
        fqr: _this.fqr,
        czzd: _this.czzd
      }).then((ret) => {
        if (ret.code < 0) {
          _this.$message.error(ret.note || ret.message);
        } else {
          _this.tableList = ret.data.list;
        }
      });
    },
    initData: function () {
      this.cpgzdmList = [];//TODO  数据初始化
      this.khlbList = [];//TODO  数据初始化
      this.fqr = '';
      this.czzd = '';
    },
  },
  mounted() {
    this.initData()
  },
/*  watch: {
    sdxInfo: {
      handler() {
        this.exportExlFlag = false;
      },
      deep: true
    }
  }*/
}
</script>

<style scoped>
.inputClass {
  width: 280px;
}
</style>