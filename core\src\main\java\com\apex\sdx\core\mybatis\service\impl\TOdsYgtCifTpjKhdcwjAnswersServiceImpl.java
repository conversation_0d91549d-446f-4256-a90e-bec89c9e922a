package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.core.exception.BusinessException;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjKhdcwjAnswers;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTpjKhdcwjAnswersService;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTpjKhdcwjAnswersMapper;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class TOdsYgtCifTpjKhdcwjAnswersServiceImpl extends ServiceImpl<TOdsYgtCifTpjKhdcwjAnswersMapper, TOdsYgtCifTpjKhdcwjAnswers>
    implements TOdsYgtCifTpjKhdcwjAnswersService{

    @Override
    public String getDacByWjid(Integer khdcwjid) {
        try {
            return this.baseMapper.selectDacByWjid(khdcwjid);
        } catch (Exception e) {
            String note = String.format("查询答案串异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }
}




