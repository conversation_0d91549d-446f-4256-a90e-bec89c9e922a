<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.TxtdmMapper">

    <resultMap id="BaseResultMap" type="com.apex.sdx.core.mybatis.entity.Txtdm">
        <result property="fldm" column="FLDM" jdbcType="VARCHAR"/>
        <result property="flmc" column="FLMC" jdbcType="VARCHAR"/>
        <result property="ibm" column="IBM" jdbcType="DECIMAL"/>
        <result property="cbm" column="CBM" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="flag" column="FLAG" jdbcType="INTEGER"/>
        <result property="type" column="TYPE" jdbcType="INTEGER"/>
        <result property="category" column="CATEGORY" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        FLDM
        ,FLMC,IBM,
        CBM,NOTE,FLAG,
        TYPE,CATEGORY
    </sql>
    <select id="queryDict" resultType="com.apex.sdx.core.mybatis.entity.Txtdm">
        SELECT xtdm.FLDM AS fldm, xtdm.FLMC AS flmc, xtdm.IBM, xtdm.CBM AS cbm
        , xtdm.NOTE AS note, xtdm.FLAG AS flag, xtdm.TYPE AS type, xtdm.CATEGORY AS category
        FROM doamp.txtdm xtdm
        <where>
            <!-- 其他条件 -->
            <if test="fldm != null">
                AND xtdm.fldm = #{fldm}
            </if>
        </where>
    </select>
</mapper>
