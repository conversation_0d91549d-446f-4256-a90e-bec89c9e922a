package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.vo.compute.QtcccptjVo;
import com.apex.sdx.api.vo.query.CpccsdxVo;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.mybatis.entity.TsdxjgQtcccp;
import com.apex.sdx.core.mybatis.mapper.TsdxjgQtcccpMapper;
import com.apex.sdx.core.mybatis.service.TsdxjgQtcccpService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class TsdxjgQtcccpServiceImpl extends ServiceImpl<TsdxjgQtcccpMapper, TsdxjgQtcccp>
    implements TsdxjgQtcccpService {

    @Override
    public List<CpccsdxVo> selectByKhhSjlxAndSdxjg(String khh, Integer sjlx, boolean onlysdx,String rq) {
        try {
            return this.baseMapper.selectByKhhSjlxAndSdxjg(khh, sjlx, onlysdx, rq);
        } catch (Exception e) {
            String note = String.format("查询产品持仓适当性异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }

    @Override
    public List<QtcccptjVo> compute(String khh, Integer ywxt, String rq) {
        try {
            return this.baseMapper.compute(khh, ywxt, rq);
        } catch (Exception e) {
            String note = String.format("统计全体持仓产品适当性异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-602, note);
        }
    }
}




