<template>
  <div>
    <div :id="chartsId" style=" width:100%; height:210px;">
    </div>
  </div>

</template>

<script>
import {defineComponent} from 'vue';

let barCharts = [];
export default defineComponent({
  props: ["dataset", "yAxis", "color", "title"],
  data() {
    return {
      values: [],//值
      legendData:["适当", "不适当", "不通过"],
      max:0,
      colorData:['#36A6FC','#F7C12E','#F54320'],
    }
  },
  methods: {
    initBarChart() {
      let el = document.getElementById(this.chartsId)
      barCharts[this.chartsId] = this.$echarts.init(el);
      this.setOption();
    },
    setOption() {
      this.values = this.$props.dataset;
      this.values.forEach((item, index) => {
        this.max += item;
      });
      let option = this.getOption();
      barCharts[this.chartsId].setOption(option)
    },
    getOption() {
      let _this = this
      let option = {
        legend: {
          bottom: 10,
          icon: "circle",
          itemHeight: 10,
          show: true,
          data: _this.legendData,
          selectedMode: false,

        },
        polar: {
          radius: [80, '30%'],
          center: ['50%', '40%'],
        },
        angleAxis: {
          max: _this.max,
          startAngle: 90,
          axisLine: false,
          splitLine: false,
        },
        radiusAxis: {
          type: 'category',
          axisLine: false,
          splitLine: {
            show: true,
          },
        },
        tooltip: {
          show: true,
          trigger: 'item',//axis
          /*axisPointer: {// 坐标轴指示器，坐标轴触发有效
            type: 'shadow',
          },*/
          className: 'myTooltip', // 指定自定义类名
          formatter: function (params) {
            let str = '<div style="width:200px;padding: 10px;background-color: #D9E1F0;border-radius: 10px;">' +
                '           <div style="margin-bottom: 5px"><span style="color: #454A55;">' + _this.title + '</span></div>' +
                '          <div style="padding: 0 0 0 10px;background-color: #fff;border-radius: 10px;">';
            let max = _this.max;
            if(max === 0){
              max = 1;//防止除数为0
            }
            for(let i = 0; i < _this.values.length; i++){//强制渲染成全部的
              let percent = parseFloat((_this.values[i] / max  * 100).toString()).toFixed(2);
              str += '            <p>' +
                  '              <span style="color: #454A55;width: 33%;display: inline-block;">' + _this.legendData[i] + '</span>' +
                  '              <span style="color: #454A55;width: 33%;display: inline-block;">' + percent + '%</span>' +
                  '              <span style="color: #454A55;width: 33%;display: inline-block;">' + _this.values[i] + '</span>' +
                  '            </p>'
            }
            return  str;
          }
        },
        series: this.seriesData,
      };
      return option;
    }
  },
  mounted() {
    if (this.$props.dataset && this.$props.dataset.length > 0) {
      this.initBarChart()
    }
  },
  computed: {
    chartsId() {
      return "id_" + this.title
    },
    seriesData() {
      let data = [];
      let _this = this;
      this.legendData.forEach((item, index) => {
        data.push({
          type: 'bar',
          barWidth: "60%",
          data: index== 0 ? [this.values[index],0,0] : index == 1 ?[0,this.values[index],0]  : [0,0,this.values[index]],
          stack: "polar",
          name: item,
          coordinateSystem: 'polar',
          itemStyle: {
            color: _this.colorData[index],
            barBorderRadius: 10,
          },
        });
      });
      return data;
    },
  },
  watch: {
    dataset(newValue, oldValue) {
      if (newValue && newValue.length > 0 && JSON.stringify(newValue) != JSON.stringify(oldValue)) {
        if (barCharts[this.chartsId]) {
          this.setOption();
        } else {
          this.initBarChart();
        }
      }
    }
  },
  unmounted() {
    if (barCharts[this.chartsId]) {
      barCharts[this.chartsId].dispose();
      delete barCharts[this.chartsId];
    }
  },
});
</script>

<style scoped>
:deep(.myTooltip){
  padding: 0 !important;
  border-radius: 10px !important;
  border-width: 0px !important;
}
</style>