package com.apex.sdx.api.vo.query;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-03-04
 * @Description:
 */
@Setter
@Getter
public class SdxsjVo {

    @LiveProperty(note = "id", index = 1)
    private Long id;

    @LiveProperty(note = "生成日期", index = 2)
    private Integer scrq;

    @LiveProperty(note = "生成时间", index = 3)
    private String scsj;

    @LiveProperty(note = "客户号", index = 4)
    private String khh;

    @LiveProperty(note = "事件名称", index = 5)
    private String sjmc;

    @LiveProperty(note = "事件详情", index = 6)
    private String sjxq;

    @LiveProperty(note = "通知方式", index = 7)
    private String tzfs;

    @LiveProperty(note = "通知日期", index = 8)
    private Integer tzrq;

    @LiveProperty(note = "通知时间", index = 9)
    private String tzsj;

    @LiveProperty(note = "通知状态", index = 10)
    private Integer tzzt;

    @LiveProperty(note = "处理状态", index = 11)
    private Integer clzt;

}
