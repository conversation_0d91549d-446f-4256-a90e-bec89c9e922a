package com.apex.sdx.core.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTywzh;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTywzhService;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTywzhMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_ods_ygt_cif_tywzh】的数据库操作Service实现
* @createDate 2025-01-23 10:17:08
*/
@Service
public class TOdsYgtCifTywzhServiceImpl extends ServiceImpl<TOdsYgtCifTywzhMapper, TOdsYgtCifTywzh>
    implements TOdsYgtCifTywzhService{

    @Override
    public List<TOdsYgtCifTywzh> getYwzhjbxx(String khh) {
        LambdaQueryWrapper<TOdsYgtCifTywzh> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(
                TOdsYgtCifTywzh::getYwxt,
                TOdsYgtCifTywzh::getYwzh,
                TOdsYgtCifTywzh::getKhrq,
                TOdsYgtCifTywzh::getYyb,
                TOdsYgtCifTywzh::getZhzt)
                .eq(TOdsYgtCifTywzh::getKhh, khh);
        return this.list(lambdaQueryWrapper);
    }
}




