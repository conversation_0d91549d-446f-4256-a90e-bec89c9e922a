package com.apex.sdx.api.resp.query;

import com.apex.sdx.api.resp.common.R;
import com.apex.sdx.api.vo.query.SdxsjDescDataVo;
import com.apex.sdx.api.vo.query.SdxsjDescVo;
import com.apex.sdx.api.vo.xtcs.XtdmVo;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-01-16
 * @Description:
 */
@Setter
@Getter
@SuperBuilder
public class SdxsjDescRes extends R {

    @LiveProperty(note = "结果说明", index = 1)
    private String jgsm;

    @LiveProperty(note = "数据详情", index = 2)
    private String sjxq;

    @LiveProperty(note = "适当性事件详情", index = 3)
    private List<SdxsjDescDataVo> descList;



}