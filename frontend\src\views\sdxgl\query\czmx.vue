<template>
  <a-table :columns="columns" :data-source="data">
<!--    <a slot="name" slot-scope="text">{{ text }}</a>-->
    <a>{{ text }}</a>
  </a-table>
</template>

<script>
import czmx from "@/api/query/czmx";
const columns = [
  {
    title: '客户号',
    dataIndex: 'khh',
    key: 'khh',
    scopedSlots: { customRender: 'khh' },
  },
  {
    title: '客户名称',
    dataIndex: 'khjc',
    key: 'khjc',
  },
  {
    title: '业务科目',
    dataIndex: 'ywkm',
    key: 'ywkm',
    ellipsis: true,
  },
  {
    title: '操作柜员代码',
    dataIndex: 'czgydm',
    key: 'czgydm',
    ellipsis: true,
  }
];
const data = [
];
export default {
  name: "czmx",
  data() {
    return {
      data,
      columns,
    };
  },
  mounted() {
    czmx.khczmxcx().then(response => {
      this.data = response.records
    })
  }
}
</script>

<style scoped>

</style>