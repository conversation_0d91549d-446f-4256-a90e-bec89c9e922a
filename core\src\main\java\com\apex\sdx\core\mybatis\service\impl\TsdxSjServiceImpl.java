package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.vo.compute.SdxsjtjVo;
import com.apex.sdx.api.vo.query.SdxsjDescVo;
import com.apex.sdx.api.vo.query.SdxsjVo;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.mybatis.entity.TsdxSj;
import com.apex.sdx.core.mybatis.mapper.TsdxSjMapper;
import com.apex.sdx.core.mybatis.service.TsdxSjService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class TsdxSjServiceImpl extends ServiceImpl<TsdxSjMapper, TsdxSj>
        implements TsdxSjService {

    @Override
    public Page<SdxsjVo> queryByKhhAndClzt(String khh, String clzt, boolean isSearchCount, int pagesize, int pagenum) {
        Page<SdxsjVo> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);
        try {
            return this.baseMapper.query4sdxsj(page, khh, null,null, clzt);
        } catch (Exception e) {
            String note = String.format("查询适当性事件失败：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }

    @Override
    public List<SdxsjDescVo> querySdxsjDesc(Long sjid) {
        return this.baseMapper.querySdxsjDesc(sjid);
    }

    @Override
    public Page<SdxsjVo> queryByKhhAndClztAndRq(String khh, String ksrq, String jsrq, String clzt, boolean isSearchCount, int pagesize, int pagenum) {
        Page<SdxsjVo> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);
        try {
            return this.baseMapper.query4sdxsj(page, khh, ksrq, jsrq, clzt);
        } catch (Exception e) {
            String note = String.format("查询适当性事件失败：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-602, note);
        }
    }

    @Override
    public Page<SdxsjtjVo> compute(String ksrq, String jsrq, String khh, String sjbm, boolean isSearchCount, int pagesize, int pagenum) {
        Page<SdxsjtjVo> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);
        try {
            return this.baseMapper.compute(page, ksrq, jsrq, khh, sjbm);
        } catch (Exception e) {
            String note = String.format("统计适当性事件失败：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-603, note);
        }
    }

    @Override
    public Page<SdxsjtjVo> compute4Sdxsj(String ksrq, String jsrq, String khh, String sjbm, boolean isSearchCount, int pagesize, int pagenum) {
        Page<SdxsjtjVo> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);
        try {
            return this.baseMapper.compute4Sdxsj(page, ksrq, jsrq, khh, sjbm);
        } catch (Exception e) {
            String note = String.format("统计适当性事件明细失败：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-604, note);
        }
    }
}




