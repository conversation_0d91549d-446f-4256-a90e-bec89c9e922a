package com.apex.sdx.api.vo.khgl;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-02-14
 * @Description:
 */
@Setter
@Getter
public class SjhcxxVo {

    @LiveProperty(note = "营业部", index = 1)
    private Long yyb;

    @LiveProperty(note = "客户号", index = 2)
    private String khh;

    @LiveProperty(note = "客户名称", index = 3)
    private String khmc;

    @LiveProperty(note = "证件类别", index = 4)
    private Long zjlb;

    @LiveProperty(note = "证件编号", index = 5)
    private String zjbh;

    @LiveProperty(note = "手机号码", index = 6)
    private String sj;

    @LiveProperty(note = "机主是否本人", index = 7)
    private Integer jzsfbr;

    @LiveProperty(note = "机主证件类别", index = 8)
    private Integer jzzjlb;

    @LiveProperty(note = "机主证件编号", index = 9)
    private String jzzjbh;

    @LiveProperty(note = "手机状态", index = 10)
    private Integer sjzt;

    @LiveProperty(note = "核查类别", index = 11)
    private Integer hclb;

    @LiveProperty(note = "核查结果", index = 12)
    private Integer hcjg;

    @LiveProperty(note = "结果说明", index = 13)
    private String jgsm;

    @LiveProperty(note = "核查日期", index = 14)
    private Integer hcrq;

    @LiveProperty(note = "机主与客户关系", index = 15)
    private Integer jzykhgx;
}
