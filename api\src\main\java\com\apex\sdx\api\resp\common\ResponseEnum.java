package com.apex.sdx.api.resp.common;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@Getter
@AllArgsConstructor
@ToString
public enum ResponseEnum {
    SUCCESS(1, "成功"),
    ERROR_1(-1, "请求失败"),
    ERROR_404(-404, "文件路径不存在"),
    ERROR_405(-405, "参数不能为空"),
    ERROR_406(-406, "参数不合法"),
    ;

    // 响应状态码
    private Integer code;
    // 响应信息
    private String note;

}
