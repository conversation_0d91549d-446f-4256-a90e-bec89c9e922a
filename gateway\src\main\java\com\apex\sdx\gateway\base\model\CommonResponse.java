package com.apex.sdx.gateway.base.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;

/**
 * Created by xfh on 2018/5/23.
 */
@ApiModel(description = "响应报文")
public class CommonResponse extends JSONResponse {

    private JSONObject data = new JSONObject();
    private JSONArray records = new JSONArray();
    private int count;

    public CommonResponse() {
        super();
    }

    public JSONObject getData() {
        return data;
    }

    public void setData(JSONObject data) {
        this.data = data;
    }

    public JSONArray getRecords() {
        return records;
    }

    public void setRecords(JSONArray records) {
        this.records = records;
        this.count = records.size();
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public CommonResponse(int code, String note) {
        super.setCode(code);
        super.setNote(note);
    }

    @Override
    public String toString() {
        return "CommonResponse{" +
                "data=" + (data != null ? data.toJSONString() : "") +
                ", records=" + (records != null ? records.toJSONString() : "") +
                ", count=" + count +
                '}';
    }
}
