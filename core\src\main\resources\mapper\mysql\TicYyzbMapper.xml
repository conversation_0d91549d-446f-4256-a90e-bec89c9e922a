<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.TicYyzbMapper">

    <resultMap id="BaseResultMap" type="com.apex.sdx.core.mybatis.entity.TicYyzb">
            <id property="rq" column="RQ" jdbcType="DECIMAL"/>
            <id property="idxId" column="IDX_ID" jdbcType="DECIMAL"/>
            <result property="idxCode" column="IDX_CODE" jdbcType="VARCHAR"/>
            <result property="result" column="RESULT" jdbcType="DECIMAL"/>
            <result property="cycle" column="CYCLE" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        RQ,IDX_ID,IDX_CODE,
        RESULT,CYCLE
    </sql>
    <select id="khzs" resultType="com.apex.sdx.api.resp.query.KhsdxfbRes">
        select
        CAST(TRUNCATE(max(CASE WHEN IDX_CODE = concat(#{type},'_SL') THEN result END), 0)AS CHAR) AS zs,
        CAST(TRUNCATE(max(CASE WHEN IDX_CODE = concat(#{type},'_RZJ') THEN result END), 0)AS CHAR) AS rzz,
        CAST(TRUNCATE(max(CASE WHEN IDX_CODE = concat(#{type},'_YZJ') THEN result END), 0)AS CHAR) AS yzz,
        CAST(TRUNCATE(max(CASE WHEN IDX_CODE = concat(#{type},'_ZB') THEN result END), 4)AS CHAR) AS zb
        from sdx.TIC_YYZB where RQ = (
        select max(RQ) from( select * from sdx.TIC_YYZB where IDX_CODE like concat(#{type},'%')
        <if test="rq != null and rq != ''">
            AND RQ = #{rq}
        </if>
        ) a
        )
    </select>
    <select id="zkhsDate" resultType="com.apex.sdx.api.resp.query.KhzsDateRes">
        select rq, result
        from sdx.TIC_YYZB
        where IDX_CODE = #{type}
        <if test="rq != null and rq != ''">
            and rq > #{preRq}
            and rq <![CDATA[<=]]> #{rq}
        </if>
        ORDER BY rq;
    </select>
</mapper>
