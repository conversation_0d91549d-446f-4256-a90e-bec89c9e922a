package com.apex.sdx.api.vo.query;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-03-05
 * @Description:
 */
@Setter
@Getter
public class YktywsdxVo {

    @LiveProperty(note = "客户号", index = 1)
    private String khh;

    @LiveProperty(note = "日期", index = 2)
    private Integer rq;

    @LiveProperty(note = "创新业务类别", index = 3)
    private Long cxywlb;

    @LiveProperty(note = "客户风险等级", index = 4)
    private String khfxdj;

    @LiveProperty(note = "客户投资品种", index = 5)
    private String khtzpz;

    @LiveProperty(note = "客户投资期限", index = 6)
    private Long khtzqx;

    @LiveProperty(note = "客户预期收益", index = 7)
    private String khyqsy;

    @LiveProperty(note = "业务风险等级", index = 8)
    private Long ywfxdj;

    @LiveProperty(note = "业务投资品种", index = 9)
    private Long ywtzpz;

    @LiveProperty(note = "业务投资期限", index = 10)
    private Long ywtzqx;

    @LiveProperty(note = "业务预期收益", index = 11)
    private Long ywyqsy;

    @LiveProperty(note = "风险等级适当性", index = 12)
    private Long fxdjsdx;

    @LiveProperty(note = "投资品种适当性", index = 13)
    private Long tzpzsdx;

    @LiveProperty(note = "投资期限适当性", index = 14)
    private Long tzqxsdx;

    @LiveProperty(note = "预期收益适当性", index = 15)
    private Long yqsysdx;

    @LiveProperty(note = "适当性结果", index = 16)
    private Long sdxjg;
}
