<template>
  <!-- 字典翻译-->
  <div>
    {{dictNote}}
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: "dict_note",
  props: {
    // 字典唯一编码
    fldm: {
      type: String,
      default: undefined
    },
    ibm: [String],
  },
  created() {
    // 初始化数据，根据字典类型请求后台，获取下拉框列表
    if (!this.dictMap[this.fldm] && this.ibm) {
      this.$store.dispatch('dict/getDictByFldm', {
        fldm: this.fldm
      })
    }
  },
  computed: {
    ...mapGetters([
      'dictMap'
    ]),
    // 当前字典
    dictNote() {
      let map = this.dictMap[this.fldm] || {};
      if(!this.ibm || !map.items || map.items.length == 0){
        return '无';
      }
      let item = map.items.find((item) => (item.ibm == this.ibm))
      if(!item || !item.note){
        return '无';
      }
      return item.note;
    }
  },
}
</script>