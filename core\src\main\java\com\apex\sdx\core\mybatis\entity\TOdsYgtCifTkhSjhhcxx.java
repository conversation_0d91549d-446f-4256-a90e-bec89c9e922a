package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName t_ods_ygt_cif_tkh_sjhhcxx
 */
@TableName(value ="t_ods_ygt_cif_tkh_sjhhcxx")
@Data
public class TOdsYgtCifTkhSjhhcxx implements Serializable {
    /**
     * 
     */
    @TableId
    private String khh;

    /**
     * 
     */
    private String sj;

    /**
     * 
     */
    private Integer jzsfbr;

    /**
     * 
     */
    private String jzmc;

    /**
     * 
     */
    private Integer jzzjlb;

    /**
     * 
     */
    private String jzzjbh;

    /**
     * 
     */
    private Integer sjzt;

    /**
     * 
     */
    private Integer hclb;

    /**
     * 
     */
    private Integer hcjg;

    /**
     * 
     */
    private String jgsm;

    /**
     * 
     */
    private String yys;

    /**
     * 
     */
    private Integer hcrq;

    /**
     * 
     */
    private Integer jzykhgx;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TOdsYgtCifTkhSjhhcxx other = (TOdsYgtCifTkhSjhhcxx) that;
        return (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
            && (this.getSj() == null ? other.getSj() == null : this.getSj().equals(other.getSj()))
            && (this.getJzsfbr() == null ? other.getJzsfbr() == null : this.getJzsfbr().equals(other.getJzsfbr()))
            && (this.getJzmc() == null ? other.getJzmc() == null : this.getJzmc().equals(other.getJzmc()))
            && (this.getJzzjlb() == null ? other.getJzzjlb() == null : this.getJzzjlb().equals(other.getJzzjlb()))
            && (this.getJzzjbh() == null ? other.getJzzjbh() == null : this.getJzzjbh().equals(other.getJzzjbh()))
            && (this.getSjzt() == null ? other.getSjzt() == null : this.getSjzt().equals(other.getSjzt()))
            && (this.getHclb() == null ? other.getHclb() == null : this.getHclb().equals(other.getHclb()))
            && (this.getHcjg() == null ? other.getHcjg() == null : this.getHcjg().equals(other.getHcjg()))
            && (this.getJgsm() == null ? other.getJgsm() == null : this.getJgsm().equals(other.getJgsm()))
            && (this.getYys() == null ? other.getYys() == null : this.getYys().equals(other.getYys()))
            && (this.getHcrq() == null ? other.getHcrq() == null : this.getHcrq().equals(other.getHcrq()))
            && (this.getJzykhgx() == null ? other.getJzykhgx() == null : this.getJzykhgx().equals(other.getJzykhgx()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getSj() == null) ? 0 : getSj().hashCode());
        result = prime * result + ((getJzsfbr() == null) ? 0 : getJzsfbr().hashCode());
        result = prime * result + ((getJzmc() == null) ? 0 : getJzmc().hashCode());
        result = prime * result + ((getJzzjlb() == null) ? 0 : getJzzjlb().hashCode());
        result = prime * result + ((getJzzjbh() == null) ? 0 : getJzzjbh().hashCode());
        result = prime * result + ((getSjzt() == null) ? 0 : getSjzt().hashCode());
        result = prime * result + ((getHclb() == null) ? 0 : getHclb().hashCode());
        result = prime * result + ((getHcjg() == null) ? 0 : getHcjg().hashCode());
        result = prime * result + ((getJgsm() == null) ? 0 : getJgsm().hashCode());
        result = prime * result + ((getYys() == null) ? 0 : getYys().hashCode());
        result = prime * result + ((getHcrq() == null) ? 0 : getHcrq().hashCode());
        result = prime * result + ((getJzykhgx() == null) ? 0 : getJzykhgx().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", khh=").append(khh);
        sb.append(", sj=").append(sj);
        sb.append(", jzsfbr=").append(jzsfbr);
        sb.append(", jzmc=").append(jzmc);
        sb.append(", jzzjlb=").append(jzzjlb);
        sb.append(", jzzjbh=").append(jzzjbh);
        sb.append(", sjzt=").append(sjzt);
        sb.append(", hclb=").append(hclb);
        sb.append(", hcjg=").append(hcjg);
        sb.append(", jgsm=").append(jgsm);
        sb.append(", yys=").append(yys);
        sb.append(", hcrq=").append(hcrq);
        sb.append(", jzykhgx=").append(jzykhgx);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}