package com.apex.sdx.api.vo.xtcs;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025-01-16
 * @Description:
 */
@Setter
@Getter
public class XtdmVo implements Serializable {

    @LiveProperty(note = "分类代码", index = 1)
    private String fldm;

    @LiveProperty(note = "分类名称", index = 2)
    private String flmc;

    @LiveProperty(note = "数值编码", index = 3)
    private String ibm;

    @LiveProperty(note = "字符编码", index = 4)
    private String cbm;

    @LiveProperty(note = "说明", index = 5)
    private String note;

    @LiveProperty(note = "标志", index = 6)
    private Long flag;

    @LiveProperty(note = "类型", index = 7)
    private Long type;

    @LiveProperty(note = "类别", index = 8)
    private String category;

    @LiveProperty(note = "营业部", index = 9)
    private Integer yyb;


    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fldm=").append(fldm);
        sb.append(", flmc=").append(flmc);
        sb.append(", ibm=").append(ibm);
        sb.append(", cbm=").append(cbm);
        sb.append(", note=").append(note);
        sb.append(", flag=").append(flag);
        sb.append(", type=").append(type);
        sb.append(", category=").append(category);
        sb.append(", yyb=").append(yyb);
        sb.append("]");
        return sb.toString();
    }
}