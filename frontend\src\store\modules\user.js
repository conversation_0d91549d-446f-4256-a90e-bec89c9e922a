import {getUserInfo} from '@/api/livebos/user'

const getDefaultState = () => {
    return {
        "lastlogin": "", // 最后登录时间
        "simulation": false, // 是否模拟用户
        "grade": null,
        "name": "", // 姓名
        "id": null, // id
        "userid": "", // userid
        "orgid": null, // 营业部
        "status": null // 状态
    }
}

const state = getDefaultState()

const mutations = {
    RESET_STATE: (state) => {
        Object.assign(state, getDefaultState())
    },
    SET_STATE: (state, data) => {
        Object.assign(state, data)
    },
}

const actions = {
    // 未登录时清除state
    logout({ commit }) {
        return new Promise((resolve) => {
            commit('RESET_STATE')
            resolve()
        })
    },
    // 已登录时获取user
    getUserInfo({ commit }) {
        return new Promise((resolve, reject) => {
            getUserInfo().then(response => {
                const {data} = response
                commit('SET_STATE', data)
                resolve(data)
            }).catch(error => {
                reject(error)
            })
        })
    }
}

export default {
    namespaced: true,
    state,
    mutations,
    actions
}

