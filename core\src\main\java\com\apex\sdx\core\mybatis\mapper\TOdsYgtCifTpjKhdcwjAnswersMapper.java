package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjKhdcwjAnswers;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @Entity com.apex.sdx.core.mybatis.entity.TOdsYgtCifTpjKhdcwjAnswers
 */
public interface TOdsYgtCifTpjKhdcwjAnswersMapper extends BaseMapper<TOdsYgtCifTpjKhdcwjAnswers> {

    String selectDacByWjid(@Param("khdcwjid") Integer khdcwjid);
}




