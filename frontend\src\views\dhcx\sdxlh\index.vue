<template>
  <div class="dxsdx_zhsdx_nav innerbox2">
    <div class="sdxlh-tabs">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1" tab="公民身份验证">
          <gmsfyz :dictArray="dictArray" :khjbxx="khjbxx"/>
        </a-tab-pane>
        <a-tab-pane key="2" tab="机构信息核查" v-if="khjbxx?.khlb != 0 ">
          <jgxxhc :dictArray="dictArray" :khjbxx="khjbxx"/>
        </a-tab-pane>
        <a-tab-pane key="3" tab="信息变更明细">
          <xxbgmx :dictArray="dictArray" :khjbxx="khjbxx"/>
        </a-tab-pane>
        <a-tab-pane key="4" tab="手机号核查流水表">
          <sjhhcls :dictArray="dictArray" :khjbxx="khjbxx"/>
        </a-tab-pane>
        <a-tab-pane key="5" tab="问卷测评流水">
          <wjcpls :dictArray="dictArray" :khjbxx="khjbxx"/>
        </a-tab-pane>
        <a-tab-pane key="6" tab="客户警示留痕">
          <khjslh :dictArray="dictArray"/>
        </a-tab-pane>
        <a-tab-pane key="7" tab="黑名单校验">
          <hmdjy :dictArray="dictArray"/>
        </a-tab-pane>
        <a-tab-pane key="8" tab="不适当协议签署流水">
          <bsdxyqsls :dictArray="dictArray"/>
        </a-tab-pane>
        <a-tab-pane key="9" tab="呼叫回访留痕">呼叫回访留痕</a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>
<script>
import {defineComponent} from "vue";
import Wjcpls from "@views/dhcx/sdxlh/wjcpls.vue";
import Khjslh from "@views/dhcx/sdxlh/khjslh.vue";
import Bsdxyqsls from "@views/dhcx/sdxlh/bsdxyqsls.vue";
import Hmdjy from "@views/dhcx/sdxlh/hmdjy.vue";
import Xxbgmx from "@views/dhcx/sdxlh/xxbgmx.vue";
import Gmsfyz from "@views/dhcx/sdxlh/gmsfyz.vue";
import Sjhhcls from "@views/dhcx/sdxlh/sjhhcls.vue";
import Jgxxhc from "@views/dhcx/sdxlh/jgxxhc.vue";

export default defineComponent({
  name: "sdxlh",
  components: {Jgxxhc, Sjhhcls, Gmsfyz, Xxbgmx, Hmdjy, Bsdxyqsls, Khjslh, Wjcpls},
  inject: ["khh"],
  props: ["dictArray", "khjbxx"],
  data() {
    return {
      activeKey: "1",
    }
  },
  mounted() {
    console.log(this.khh);
  },
  methods: {},
});
</script>
<style scoped>
.dxsdx_zhsdx_nav {
  height: calc(100%);
  border-radius: 8px;
  background-color: #fff;
  overflow-y: auto;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.sdxlh-tabs {
  padding: 10px 20px;
}

:deep(.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
  color: #B48A3B;
}

:deep(.ant-tabs .ant-tabs-ink-bar) {
  background: #B48A3B
}
</style>