package com.apex.sdx.gateway.base.dao;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.apex.ams.client.LiveBOSClient;
import com.apex.ams.livebos.services.*;
import com.apex.sdx.api.resp.common.Response;
import com.apex.sdx.core.exception.BusinessException;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @ClassName: LivebosService
 * @Description: 平台微服务，调用服务层
 * 江山如此多娇，引无数英雄竞折腰--XFH
 */
@Component
@Slf4j
public class LivebosGrpcDao extends BaseDao {

    @Value("${live.livebos.username}")
    public String LOGIN_ID;

    @Value("${live.livebos.password}")
    public String PASSWORD;

    @Value("${live.livebos.namespace}")
    private String nameSpace = null;

    public String SCHEME = "LiveBOS";

    public String ALGORITHM = "plain";

    private ObjectServiceGrpc.ObjectServiceBlockingStub objStub;

    private WorkflowServiceGrpc.WorkflowServiceBlockingStub workflowStub;

    private AuthServiceGrpc.AuthServiceBlockingStub authStub;

    private BizServiceGrpc.BizServiceBlockingStub bizStub;

    private UserServiceGrpc.UserServiceBlockingStub userStub;

    private HyperLinkServiceGrpc.HyperLinkServiceBlockingStub hyperLinkStub;

    private NotificationServiceGrpc.NotificationServiceBlockingStub notificationStub;


    /**
     * 设置用户方案
     *
     * @param id
     * @param projectName
     * @return
     * @throws Exception
     */
    public UserProjectReply userProject(int id, String projectName) throws Exception {
        AuthServiceGrpc.AuthServiceBlockingStub stub = getAuthService();
        UserProjectReply userProjectReply = stub.loadUserProject(UserProjectRequest.newBuilder().setUserId(id).setProjectName(projectName).build());
        if (userProjectReply.getResult() == -2) {
            this.authStub = null;
            return userProject(id, projectName);
        }
        return userProjectReply;
    }


    public UserInfoReply validateUser(String userId,String pwd) throws Exception {
        UserInfoReply ws_user = getUserService().validateUser(
                ValidateUserRequest.newBuilder().setLoginId(userId).setPassword(pwd).setAlgorithm("").build());
        return ws_user;
    }

    /**
     * 用户组织机构
     * @param uId
     * @return
     * @throws Exception
     */
    public String userOrganization(long uId) throws Exception {
        UserOrganizationReply userOrganizationReply = getAuthService().loadUserOrganizations(UserOrganizationRequest.newBuilder().setUserId(uId).build());
        return JsonFormat.printer().print(userOrganizationReply);
    }

    /**
     *获取用户功能权限
     * @param id
     * @param moduleName
     * @return
     * @throws Exception
     */
    public JSONArray queryUserAuthList(long id, String moduleName) throws Exception {
        JSONArray userAuthArray = new JSONArray();
        UserAuthRequest.Builder builder = UserAuthRequest.newBuilder().setUserId(id);
        if (moduleName != null) {
            builder.setLoadOption(UserAuthRequest.LoadOption.MODULE).setMuduleName(moduleName);
        }
        Iterator<UserAuthReply> userAuthReplyIterator = getAuthService().loadUserAuth(builder.build());
        while (userAuthReplyIterator.hasNext()) {
            UserAuthReply qrs = userAuthReplyIterator.next();
            UserAuthInfo userAuthInfo = qrs.getAuthInfo();
            String objectName = userAuthInfo.getObjectName();
            if(StringUtils.isNotBlank(objectName)) {
                userAuthArray.add(userAuthInfo.getObjectName());
            }
            for(int i=0;i<userAuthInfo.getCommandsCount();i++){
                userAuthArray.add(userAuthInfo.getCommands(i));
            }
        }
        return userAuthArray;
    }


    /**
     * 判断权限
     * @param id
     * @param objectName
     * @return
     * @throws Exception
     */
    public boolean userHasRight(String id, String objectName) {
        try {
            UserRightReply t167 = getAuthService().hasRight(UserRightRequest.newBuilder().setObjectName(objectName).setUserId(Integer.parseInt(id)).build());
            return t167.getHasRight();
        }catch (Exception e){
            return false;
        }
    }


    /**
     * 获取用户信息
     *
     * @return
     * @throws Exception
     */
    public UserInfoReply getUserInfo(String userID) throws Exception {
        UserInfoReply lsp = getUserService()
                .getUserInfo(GetUserInfoRequest.newBuilder().setLoginId(userID).build());
        return lsp;
    }

    /**
     * 修改用户密码
     *
     * @return
     * @throws Exception
     */
    public Result changeUserPassword(String userID, String oldPassword, String newPassword) throws Exception {
        Result result = getUserService().changeUserPassword(ChangeUserPasswordRequest.newBuilder().setLoginId(userID)
                .setOldPassword(oldPassword).setNewPassword(newPassword).build());
        return result;
    }

    /**
     * 查询对象
     *
     * @return
     * @throws Exception
     */
    public JSONObject queryObject(QueryRequest queryRequest) throws Exception {
        JSONObject result = new JSONObject();
        Iterator<QueryReply> itQrs = getObjectService().query(queryRequest);
        if (itQrs.hasNext()) {
            QueryReply qrsMeta = itQrs.next();
            QueryBaseInfo baseInfo = qrsMeta.getBaseInfo();
            if (baseInfo.getResult() == -2) {
                this.objStub = null;
                return queryObject(queryRequest);
            }
            result.put("code", baseInfo.getResult());
            result.put("note", baseInfo.getMessage());
            result.put("count", baseInfo.getCount());
            JSONArray records = new JSONArray();
            List<String> th = new ArrayList<>();
            for (Iterator<ColInfo> it = baseInfo.getMetaData().getColInfoList().iterator(); it.hasNext(); ) {
                ColInfo colInfo = it.next();
                th.add(colInfo.getLabel());
            }
            int k = 0;
            while (itQrs.hasNext()) {
                k = 0;
                QueryReply qrs = itQrs.next();
                JSONObject item = new JSONObject();
                for (Iterator<String> itVal = qrs.getRecord().getValuesList().iterator(); itVal.hasNext(); ) {
                    item.put(th.get(k++), itVal.next());
                }
                records.add(item);
            }
            result.put("records", records);
        }
        return result;
    }


    /**
     * 获取可以执行的动作
     * @param instId
     * @return
     * @throws Exception
     */
    public Long getWorkAvailableAction(long instId) throws Exception {
        GetWorkActionReply workAvailableAction = getWorkflowService()
                .getWorkAvailableAction(GetWorkActionRequest.newBuilder().setInstId(instId).build());
        if(workAvailableAction.getResult().getResult()<0) {
            throw new BusinessException(workAvailableAction.getResult().getResult(), workAvailableAction.getResult().getMessage());
        }
        return workAvailableAction.getActions(0).getActionId();
    }

    /**
     *活动参与人
     * @param instId
     * @param stepId
     * @return
     * @throws Exception
     */
    public String getWorkOwner(long instId, long stepId) throws Exception {
        GetWorkOwnerReply workOwner = getWorkflowService()
                .getWorkOwner(GetWorkOwnerRequest.newBuilder().setInstId(instId).setStepId(stepId).build());
        return JsonFormat.printer().print(workOwner);
    }

    /**
     * 获取代办任务数
     * @param uid
     * @return
     * @throws Exception
     */
    public int getWorkTaskCount(String uid)throws Exception {
        GetWorkTaskCountReply workTaskCount = getWorkflowService().getWorkTaskCount(
                GetWorkTaskCountRequest.newBuilder().setCaller(uid).build());
        return workTaskCount.getTaskCount();
    }

    /**
     * 获取代表任务信息
     * @param uid
     * @throws Exception
     */
    public Iterator<QueryReply> queryWorkTasks(String uid)throws Exception{
        Iterator<QueryReply> itQrs = getWorkflowService().queryWorkTasks(QueryWorkflowTaskRequest.newBuilder().setCaller(uid)
                .setQueryOption(QueryOption.newBuilder().setBatchNo(1).setBatchSize(20).build()).build());
        return itQrs;
    }


    /**
     * 获取用户权限内的流程信息
     * @param uid
     * @param startUpOnly
     * @return
     * @throws Exception
     */
    public QueryWorkflowItemsReply queryWorkflowItems(String uid,boolean startUpOnly)throws Exception{
        QueryWorkflowItemsReply reply = getWorkflowService().queryWorkflowItems(QueryWorkflowItemsRequest.newBuilder().setCaller(uid)
                .setStartUpOnly(startUpOnly).build());
        return reply;
    }


    /**
     * 创建流程启动链接
     * @param userid
     * @param workflowName
     * @param parameter
     * @param authFlag
     * @param attribute
     * @return
     * @throws Exception
     */
    public HyperLinkReply createStartWorkflowHyperLink(String userid, String workflowName, Parameter parameter, int authFlag, int attribute) throws Exception {
        HyperLinkReply reply = this.getHyperLinkService().createStartWorkflowHyperLink(StartWorkflowHyperLinkRequest.newBuilder()
                .setUserId(userid).setWorkflowName(workflowName).setAuthFlag(authFlag).setAttribute(attribute)
                .build());
        return reply;
    }

    public HyperLinkReply createTransactWorkflowHyperLink(String userid, int instId, int stepId, List<Parameter> parameters, int attribute) throws Exception {
        TransactWorkflowHyperLinkRequest.Builder requestBuild = TransactWorkflowHyperLinkRequest.newBuilder()
                .setUserId(userid).setInstId(instId).setStepId(stepId).setAttribute(attribute);
        if(parameters!=null){
            for(Parameter parameter:parameters){
                requestBuild.addParameters(parameter);
            }
        }

        HyperLinkReply reply = this.getHyperLinkService().createTransactWorkflowHyperLink(requestBuild.build());
        return reply;
    }

    /**
     * 未读消息总数
     * @param uid
     * @return
     * @throws Exception
     */
    public int getUnreadNotificationCount(String uid)throws Exception {
        GetUnreadNotificationCountReply unreadNotificationCount = this.getNotificationStub().getUnreadNotificationCount(
                GetUnreadNotificationCountRequest.newBuilder().setUserId(Integer.parseInt(uid)).build());
        return unreadNotificationCount.getCount();
    }

    /**
     * 获取用户消息详细列表信息
     *
     * @throws Exception
     */
    public Iterator<LoadNotificationReply> loadNotification(String userId,String setNotificationType,int pageNo,int pageSize) throws Exception {
        LoadNotificationRequest.Builder workflow = LoadNotificationRequest.newBuilder()
                .setUserId(Integer.parseInt(userId)).setPageNo(pageNo).setPageCount(pageSize);
        if(StringUtils.isNotEmpty(setNotificationType)){
            workflow.setNotificationType(setNotificationType);
        }
        return this.getNotificationStub().loadNotifications(workflow.build());
    }

    /**
     *
     * @param sender 发送人
     * @param isAll 是否所有人接受
     * @param receiver 接受人ID串，分号隔开
     * @param notificationType 消息类型
     * @param title 消息标题
     * @param content 消息内容
     * @return
     * @throws Exception
     */
    public Result sendNotification(int sender, boolean isAll, String receiver,
                                          String notificationType, String title, String content,String objectId) throws Exception {
        SendNotificationRequest.Builder builder = SendNotificationRequest.newBuilder()
                .setSender(sender).setTitle(title).setContent(content).setObjectId(objectId)
                .setNotificationType(notificationType).setIsAll(isAll);
        if (StringUtils.isNotEmpty(receiver)) {
            SendNotificationRequest.Receiver.Builder receiveList = SendNotificationRequest.Receiver.newBuilder();
            String[] receiverS = receiver.split(";");
            for (String id : receiverS) {
                receiveList.addUids(Long.parseLong(id));
            }
            builder.setReceiver(receiveList.build());
        }
        return this.getNotificationStub().sendNotification(builder.build());
    }

    public Response doAction(String workflowName, Long instanceId, Integer actionId, String caller, String summary, JSONArray params) {
        try {
            WorkflowServiceGrpc.WorkflowServiceBlockingStub workflowService = this.getWorkflowService();
            WorkActionRequest.Builder builder = WorkActionRequest.newBuilder()
                    .setInstanceId(instanceId)
                    .setActionId(actionId)
                    .setWorkflowName(workflowName)
                    .setSummary(summary)
                    .setCaller(caller);
            if(params!=null) {
                for(int i=0; i<params.size(); i++) {
                    JSONObject json = params.getJSONObject(i);
                    String value = json.getString("value");
                    if(value == null) {
                        continue;
                    }
                    Parameter parameter = Parameter.newBuilder()
                            .setName(json.getString("name"))
                            .setValue(value)
                            .build();
                    builder.addParams(parameter);
                }
            }
            WorkActionRequest req = builder.build();
            WorkActionResult result = workflowService.doAction(req);
            return Response.builder().code(result.getResult()).note(result.getMessage()).build();
        } catch (Exception e) {
            e.printStackTrace();
            return Response.builder().code(-1).note("发起流程失败:"+e.getMessage()).build();
        }
    }
    /**
     * 标注已读消息
     * @param notificationId
     * @param userId
     * @throws Exception
     */
    public Result readNotification(long notificationId,String userId) throws Exception {
        return this.getNotificationStub().readNotification(ReadNotificationRequest.newBuilder().setNotificationId(notificationId)
                .setUserId(Integer.parseInt(userId)).build());
    }

    /**
     * 终止流程
     * @return
     * @throws Exception
     */
    public JSONObject killWorkflow(String caller,String workflowName,int instId,String reason) throws Exception {
        WorkflowServiceGrpc.WorkflowServiceBlockingStub workflowServiceBlockingStub=getWorkflowService();
        KillWorkflowRequest.Builder killWorkflowRequest = KillWorkflowRequest.newBuilder();
        killWorkflowRequest.setCaller(caller)
                .setWorkflowName(workflowName)
                .setInstId(instId)
                .setReason(reason);
        Result resultL = workflowServiceBlockingStub.killWorkflow(killWorkflowRequest.build());
        JSONObject result = new JSONObject();
        if(resultL.getResult()>0){
            result.put("code",1);
        }else{
            result.put("code",-1);
            result.put("note",resultL.getMessage());
        }
        return result;
    }


    /**
     * 执行流程方法
     *
     * @param bizProcessName 流程方法名称
     * @param objectId       对象ID
     * @param params         参数列表对应方法参数信息 Parameter name-value列表
     * @param variables      变量列表，对应输入变量 Parameter name-value列表
     * @return
     * @throws Exception
     */
    public JSONObject execBizProcess(String bizProcessName, String objectId, Map<String, String> params, Map<String, String> variables) throws Exception {
        BizServiceGrpc.BizServiceBlockingStub bizServiceBlockingStub = getBizService();
        ExecBizProcessRequest.Builder builder = ExecBizProcessRequest.newBuilder();
        builder.setBizProcessName(bizProcessName); //必填
        if(StringUtils.isNotBlank(objectId)){
            builder.setObjectId(objectId);
        }
        if(params!=null){
            Set<String> keys = params.keySet();
            for (String key : keys) {
                builder.addParams(Parameter.newBuilder().setName(key).setValue(params.get(key)).build());
            }
        }
        if(variables !=null){
            Set<String> keys = variables.keySet();
            for (String key : keys) {
                builder.addVariables(Parameter.newBuilder().setName(key).setValue(variables.get(key)).build());
            }
        }
        BizServiceReply bizResult = bizServiceBlockingStub.execBizProcess(builder.build());
        JSONObject result = new JSONObject();
        if(bizResult.getResult()>0){
            result.put("code",1);
        }else{
            result.put("code",-1);
            result.put("note",bizResult.getMessage());
        }
        return result;
    }

    public BizServiceGrpc.BizServiceBlockingStub getBizService() throws Exception {
        synchronized (this) {
            if (!LiveBOSClient.getInstance(this.nameSpace).isLogon()) {
                LiveBOSClient.getInstance(this.nameSpace).login(LOGIN_ID, PASSWORD, SCHEME, ALGORITHM, "");
                bizStub = (BizServiceGrpc.BizServiceBlockingStub) LiveBOSClient.getInstance(nameSpace).newBlockingStub(BizServiceGrpc.class);
            } else {
                if (bizStub == null) {
                    bizStub = (BizServiceGrpc.BizServiceBlockingStub) LiveBOSClient.getInstance(nameSpace).newBlockingStub(BizServiceGrpc.class);
                }
            }
        }
        return bizStub;
    }

    public WorkflowServiceGrpc.WorkflowServiceBlockingStub getWorkflowService() throws Exception {
        synchronized (this) {
            if (!LiveBOSClient.getInstance(nameSpace).isLogon()) {
                LiveBOSClient.getInstance(nameSpace).login(LOGIN_ID, PASSWORD, SCHEME, ALGORITHM, "");
                workflowStub = (WorkflowServiceGrpc.WorkflowServiceBlockingStub) LiveBOSClient.getInstance(nameSpace)
                        .newBlockingStub(WorkflowServiceGrpc.class);
            }else {
                if (workflowStub == null) {
                    workflowStub = (WorkflowServiceGrpc.WorkflowServiceBlockingStub) LiveBOSClient.getInstance(nameSpace)
                            .newBlockingStub(WorkflowServiceGrpc.class);
                }
            }
        }
    return workflowStub;
    }

    public ObjectServiceGrpc.ObjectServiceBlockingStub getObjectService() throws Exception {
        synchronized (this) {
            if (!LiveBOSClient.getInstance(this.nameSpace).isLogon()) {
                LiveBOSClient.getInstance(this.nameSpace).login(LOGIN_ID, PASSWORD, SCHEME, ALGORITHM, "");
                objStub = (ObjectServiceGrpc.ObjectServiceBlockingStub) LiveBOSClient.getInstance(this.nameSpace).newBlockingStub(ObjectServiceGrpc.class);
            }else{
                if (objStub == null) {
                    objStub = (ObjectServiceGrpc.ObjectServiceBlockingStub) LiveBOSClient.getInstance(this.nameSpace).newBlockingStub(ObjectServiceGrpc.class);
                }
            }
        }
        return objStub;
    }

    public UserServiceGrpc.UserServiceBlockingStub getUserService() throws Exception {
        synchronized (this) {
            if (!LiveBOSClient.getInstance(this.nameSpace).isLogon()) {
                LiveBOSClient.getInstance(this.nameSpace).login(LOGIN_ID, PASSWORD, SCHEME, ALGORITHM, "");
                userStub = (UserServiceGrpc.UserServiceBlockingStub) LiveBOSClient.getInstance(this.nameSpace)
                        .newBlockingStub(UserServiceGrpc.class);
            }
            if (userStub == null) {
                userStub = (UserServiceGrpc.UserServiceBlockingStub) LiveBOSClient.getInstance(this.nameSpace)
                    .newBlockingStub(UserServiceGrpc.class);
            }
        }
        return userStub;
    }

    public AuthServiceGrpc.AuthServiceBlockingStub getAuthService() throws Exception {
        synchronized (this) {
                if (!LiveBOSClient.getInstance(this.nameSpace).isLogon()) {
                    LiveBOSClient.getInstance(this.nameSpace).login(LOGIN_ID, PASSWORD, SCHEME, ALGORITHM, "");
                    authStub =
                            (AuthServiceGrpc.AuthServiceBlockingStub) LiveBOSClient.getInstance(this.nameSpace).newBlockingStub(AuthServiceGrpc.class);
                }
            if (authStub == null) {
                authStub =
                        (AuthServiceGrpc.AuthServiceBlockingStub) LiveBOSClient.getInstance(this.nameSpace).newBlockingStub(AuthServiceGrpc.class);
            }
        }
        return authStub;
    }

    public HyperLinkServiceGrpc.HyperLinkServiceBlockingStub getHyperLinkService() throws Exception {
        synchronized (this) {
            if (!LiveBOSClient.getInstance(this.nameSpace).isLogon()) {
                LiveBOSClient.getInstance(this.nameSpace).login(LOGIN_ID, PASSWORD, SCHEME, ALGORITHM, "");
                hyperLinkStub =
                        (HyperLinkServiceGrpc.HyperLinkServiceBlockingStub) LiveBOSClient.getInstance(this.nameSpace).newBlockingStub(HyperLinkServiceGrpc.class);
            }
            if (hyperLinkStub == null) {
                hyperLinkStub =
                        (HyperLinkServiceGrpc.HyperLinkServiceBlockingStub) LiveBOSClient.getInstance(this.nameSpace).newBlockingStub(HyperLinkServiceGrpc.class);
            }
        }
        return hyperLinkStub;
    }


    public NotificationServiceGrpc.NotificationServiceBlockingStub getNotificationStub() throws Exception {

        synchronized (this) {
            if (!LiveBOSClient.getInstance(this.nameSpace).isLogon()) {
                LiveBOSClient.getInstance(this.nameSpace).login(LOGIN_ID, PASSWORD, SCHEME, ALGORITHM, "");
                notificationStub = (NotificationServiceGrpc.NotificationServiceBlockingStub) LiveBOSClient.getInstance(this.nameSpace).newBlockingStub(NotificationServiceGrpc.class);
            }
            if (notificationStub == null) {
                notificationStub = (NotificationServiceGrpc.NotificationServiceBlockingStub) LiveBOSClient.getInstance(this.nameSpace).newBlockingStub(NotificationServiceGrpc.class);
            }
        }
        return notificationStub;
    }

}
