package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.vo.khgl.SjhcxxVo;
import com.apex.sdx.core.exception.BusinessException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhSjhhcxx;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhSjhhcxxService;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTkhSjhhcxxMapper;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class TOdsYgtCifTkhSjhhcxxServiceImpl extends ServiceImpl<TOdsYgtCifTkhSjhhcxxMapper, TOdsYgtCifTkhSjhhcxx>
    implements TOdsYgtCifTkhSjhhcxxService{

    @Override
    public Page<SjhcxxVo> queryByCoinditions(String khh, Integer ksrq, Integer jsrq, Integer hcjg, boolean isSearchCount, int pagesize, int pagenum) {
        Page<TOdsYgtCifTkhSjhhcxx> page = new Page<>(pagenum, pagesize, isSearchCount);
        try {
            return this.baseMapper.selectSjhhcxx(page, khh, ksrq, jsrq, hcjg);
        } catch (Exception e) {
            String note = String.format("查询客户手机号核查信息失败：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }
}




