package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.api.vo.query.TgcpsdxVo;
import com.apex.sdx.core.mybatis.entity.TsdxjgQtdytg;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tsdxjg_qtdytg(全体已订阅投顾适当性)】的数据库操作Service
 * @createDate 2025-06-11 18:54:56
 */
public interface TsdxjgQtdytgService extends IService<TsdxjgQtdytg> {

    List<TgcpsdxVo> selectByKhhSjlxAndSdxjg(String khh, Integer sjlx, boolean onlysdx,String rq);
}
