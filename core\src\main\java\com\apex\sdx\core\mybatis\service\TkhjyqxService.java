package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.core.mybatis.entity.Tkhjyqx;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface TkhjyqxService extends IService<Tkhjyqx> {

    /**
     *
     * @param khh
     * @param ywzh
     * @param gdh
     * @param jyqx
     * @param zt
     * @param isSearchCount
     * @param pagesize
     * @param pagenum
     * @return
     */
    Page<Tkhjyqx> queryKhjyqx(String khh, String ywzh, String gdh, Integer jyqx, Integer zt,  boolean isSearchCount, int pagesize, int pagenum);
}
