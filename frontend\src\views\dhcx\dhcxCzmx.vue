<template>
  <!--操作明细-->
  <div class="czmx_top">
    <div class="czmx_titlebox">
      <span class="czmx_title">操作明细查询</span>
    </div>
    <div>
      <a-row>
        <a-col :span="11">
          <span style="padding-right: 10px; font-size: 15px; padding-left: 5%; font-weight: bold">操作日期 :</span>
          <a-range-picker
            @change="onChange"
            style="width: 30%"
            :ranges="ranges"
            :value="date"
            format="YYYYMMDD"
          />
          <span style="padding-right: 10px; font-size: 15px; padding-left: 5%; font-weight: bold">业务科目 :</span>
          <a-select
            show-search
            placeholder="请选择业务科目"
            option-filter-prop="children"
            style="width: 200px"
            :filter-option="filterOption"
            @change="handleChange"
            :options="ywkmList"
            :allowClear=true
          >
          </a-select>
        </a-col>
        <a-col :span="13">
          <a-button :loading="isLoad" :disabled="isDisabled" @click="queryCzmx">
            <template #icon>
              <SearchOutlined/>
            </template>
            <span style="font-weight: bold">查询</span>
          </a-button>
        </a-col>
      </a-row>
    </div>
    <a-table :columns="columns"
             :data-source="data"
             :scroll="{ x: 1100, y: 310 }"
             style="margin-top: 10px"
             :pagination="pagination"
             @change="pageChange"
    >
    </a-table>
  </div>
</template>

<script>
import dayjs from "dayjs";
import {SearchOutlined} from "@ant-design/icons-vue";
import czmxApi from "@/api/ywls/czmx";

const columns = [{
  title: "流水号",
  width: 100,
  dataIndex: "id",
  key: "id",
  fixed: "left",
  align: "center",
}, {
  title: "发生日期",
  width: 100,
  dataIndex: "rq",
  key: "rq",
  align: "center",
}, {
  title: "发生时间",
  dataIndex: "fssj",
  key: "fssj",
  width: 100,
  align: "center",
}, {
  title: "业务科目",
  dataIndex: "ywkm",
  key: "ywkm",
  width: 150,
  align: "center",
}, {
  title: "发生营业部",
  dataIndex: "fsyyb",
  key: "fsyyb",
  width: 150,
  align: "center",
}, {
  title: "操作柜员",
  dataIndex: "czgy",
  key: "czgy",
  width: 100,
  align: "center",
}, {
  title: "操作站点",
  dataIndex: "czzd",
  key: "czzd",
  width: 150,
  align: "center",
  ellipsis: true,
}, {
  title: "摘要",
  dataIndex: "zy",
  key: "zy",
  width: 150,
  align: "center",
  ellipsis: true,
  showSorterTooltip: true,
}, {
  title: "业务申请号",
  dataIndex: "ywqqid",
  key: "ywqqid",
  width: 100,
  align: "center",
}];
const data = [];
for (let i = 0; i < 100; i++) {
  data.push({
    key: i,
    name: `Edrward ${i}`,
    age: 32,
    address: `London Park no. ${i}`,
  });
}
export default {
  name: "dhcxCzmx",
  props: ["khh"],

  data() {
    return {
      ranges: {
        "最近一周": [dayjs().subtract(7, "day"), dayjs()],
        "最近一个月": [dayjs().subtract(1, "month"), dayjs()],
        "最近三个月": [dayjs().subtract(3, "month"), dayjs()],
      },
      date: [dayjs().subtract(1, "month"), dayjs()],
      pagination: {
        "show-quick-jumper": true,

      },
      kssj: dayjs().subtract(1, "month").format("YYYYMMDD"),
      jssj: dayjs().format("YYYYMMDD"),
      isLoad: false,
      isDisabled: false,
      data: data,
      columns,
      ywkmList: [],
      ywkm: "",
      pageNum: 1,
      pageSize: 10,
    };
  },
  mounted() {
    this.getYwkm();
    this.queryCzmx();
  },
  methods: {
    onChange(date, dateString) {
      console.log(date, dateString[0], dateString[1]);
      this.date = date;
      this.kssj = dateString[0];
      this.jssj = dateString[1];
    },
    handleChange(value) {
      this.ywkm = value;
    },
    filterOption(input, option) {
      return (
        option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0 || option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    getYwkm() {
      czmxApi.cxywkm().then((res) => {
        let result = res.records;
        if (result == null || result === []) {
          return;
        }
        result.forEach(res => {
          this.ywkmList.push({
            label: res["ywmc"],
            value: res["ywkm"],
          });
        });
      });
    },
    pageChange(page) {
      console.log(page);
      this.pageNum = page.current;
      this.pageSize = page.pageSize;
    },
    queryCzmx(){
      this.isDisabled = true;
      this.isLoad = true;
      czmxApi.cxczmx({
        'khh': this.khh,
        'kssj': this.kssj,
        'jssj': this.jssj,
        'ywkm': this.ywkm,
        'pagenum': this.pageNum,
        'pagesize': this.pageSize,
        'isSearchCount': true
      }).then((res) => {
        console.log(res);
        this.data = res.records;
      }).finally(() => {
        this.isLoad = false;
        this.isDisabled = false;
      })
    }
  },
  components: {
    SearchOutlined,
  },
};
</script>

<style scoped>
.czmx_top {
    border-radius: 8px;
    padding: 10px 20px;
    font-size: 12px !important;
    margin-top: -4px
}

.czmx_titlebox {
    padding-bottom: 10px;
}

.czmx_title {
    font-size: 16px;
    color: #666;
}

.czmx_title:before {
    width: 5px;
    height: 20px;
    margin-right: 12px;
    margin-left: 12px;
    color: transparent;
    background: #1890ff;
    border-radius: 5px;
    content: ":";
}

</style>
