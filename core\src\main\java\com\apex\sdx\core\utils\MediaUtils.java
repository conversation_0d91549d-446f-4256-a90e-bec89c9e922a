package com.apex.sdx.core.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * <AUTHOR>
 * @Date 2025-03-10
 * @Description:
 */
@Component
public class MediaUtils {

    private static String fileFolderPath;

    @Value("${fileFolderPath}")
    public void setFileFolderPath(String fileFolderPath) {
        MediaUtils.fileFolderPath = fileFolderPath;
    }

    private static final String DEFAULT_PATH_FORMAT = "/yyyy/MM/dd/";

    private static final String FILE_CONTENT_PRE = "APEX";

    private static final String FORMAT_TEMPATES[] = {
            "yyyyMMddHHmmss"
            , "MMddyyyyHHmmss"
            , "MMddHHyyyymmss"
            , "MMddHHmmyyyyss"
            , "MMddHHmmssyyyy"
            , "MMmmssyyyyddHH"
            , "ssMMmmyyyyddHH"
            , "ssmmyyyyMMddHH"
            , "HHssmmyyyyMMdd"
    };

    public static void readMediaFile(OutputStream os, String token, boolean thumb) throws IOException {
        Base64.Decoder decoder = Base64.getDecoder();
        byte[] bts = decoder.decode(token);
        ByteArrayInputStream bais = new ByteArrayInputStream(bts);
        int format = bais.read();
        int nameLen = bais.read();
        byte b14[] = new byte[nameLen];
        if (bais.read(b14) < 1) {
            throw new IOException("token格式出错.");
        }
        String s = new String(b14);
        String path = StringUtils.getDateStringByFormat(s, FORMAT_TEMPATES[format], getPathFormat());
        String realFileName = toSaveFilePath(encStr(bts), thumb);
        File dir = new File((new StringBuilder()).append(fileFolderPath).append("/").append(path).toString());
        if (!dir.exists()) {
            System.out.println(dir);
            throw new IOException("token格式出错"+(new StringBuilder()).append(dir.getAbsolutePath()).append("/").append(realFileName).toString());
        }
        File realFile = new File((new StringBuilder()).append(dir.getAbsolutePath()).append("/").append(realFileName).toString());
        if (!realFile.exists()) {
            throw new IOException("token格式出错/文件不存在"+realFile.getPath());
        } else {
            outPrintStream(realFile, os);
        }
    }

    private static String getPathFormat() {
        return DEFAULT_PATH_FORMAT;
    }

    private static String toSaveFilePath(String md5, boolean thumb) {
        md5 = md5.replaceAll("[\\\\|/|:|\\*|\"|\\?|<|>]", "_");
        return thumb ? (new StringBuilder()).append(md5).append("__thumb").toString() : md5;
    }

    private static String encStr(byte source[])
            throws IOException {
        char hexDigits[] = {
                '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'a', 'b', 'c', 'd', 'e', 'f'
        };
        MessageDigest md;
        try {
            md = MessageDigest.getInstance("MD5");
        }
        catch (NoSuchAlgorithmException e) {
            throw new IOException((new StringBuilder()).append("").append(e).toString());
        }
        md.update(source);
        byte tmp[] = md.digest();
        char str[] = new char[32];
        int k = 0;
        for (int i = 0; i < 16; i++) {
            byte byte0 = tmp[i];
            str[k++] = hexDigits[byte0 >>> 4 & 0xf];
            str[k++] = hexDigits[byte0 & 0xf];
        }

        return (new String(str));
    }

    private static void outPrintStream(File src, OutputStream os)
            throws IOException {
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(src);
            byte bts[] = new byte[FILE_CONTENT_PRE.getBytes().length];
            if (fis.read(bts) < 1 || !FILE_CONTENT_PRE.equals(new String(bts))) {
                throw new IOException("非顶点CIF多媒体文件格式");
            }
            int fileNameLen = fis.read();//滑1个 长度=1 值=X
            if(fileNameLen > 0) {
                byte fileNameBytes[] = new byte[fileNameLen];
                if (fis.read(fileNameBytes) < 1) {//滑X个 长度=X 值=文件名称
                    throw new IOException("文件格式不正确");
                }
            }
            byte buffer[] = new byte[2048];
            int b;
            while ((b = fis.read(buffer)) != -1) {
                os.write(buffer, 0, b);
            }
        }
        catch (RuntimeException e) {
            throw new IOException((new StringBuilder()).append("").append(e).toString());
        } finally {
            try {
                if (fis != null) {
                    fis.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                if (os != null) {
                    os.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
