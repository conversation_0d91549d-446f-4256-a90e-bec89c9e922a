package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.mybatis.entity.TLdTradingCalendar;
import com.apex.sdx.core.mybatis.mapper.TLdTradingCalendarMapper;
import com.apex.sdx.core.mybatis.service.TLdTradingCalendarService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_ld_trading_calendar(交易日历表)】的数据库操作Service实现
* @createDate 2025-05-30 17:04:16
*/
@Service
public class TLdTradingCalendarServiceImpl extends ServiceImpl<TLdTradingCalendarMapper, TLdTradingCalendar>
    implements TLdTradingCalendarService {

    @Override
    public TLdTradingCalendar queryJyrByZrr(String zrr) {
        QueryWrapper<TLdTradingCalendar> wrapper = new QueryWrapper<>();
        wrapper.eq("ZRR",zrr);
        try {
            return this.getOne(wrapper, false);
        }catch (Exception e){
            String note = String.format("获取交易日失败:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }

}




