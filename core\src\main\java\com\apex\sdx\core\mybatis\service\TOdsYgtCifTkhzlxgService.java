package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhzlxg;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface TOdsYgtCifTkhzlxgService extends IService<TOdsYgtCifTkhzlxg> {

    /**
     * 根据客户号查询客户资料变更明细
     * @param khh
     * @param isSearchCount
     * @param pagesize
     * @param pagenum
     * @return
     */
    Page<TOdsYgtCifTkhzlxg> queryByKhh(String khh, Integer ksrq, Integer jsrq, boolean searchCount, int pagesize, int pagenum);
}
