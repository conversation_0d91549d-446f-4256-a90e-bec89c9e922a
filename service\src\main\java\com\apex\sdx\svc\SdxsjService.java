package com.apex.sdx.svc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.apex.sdx.api.req.compute.SdxsjtjReq;
import com.apex.sdx.api.req.query.SdxsjcxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.resp.query.SdxsjDescRes;
import com.apex.sdx.api.vo.compute.SdxsjtjVo;
import com.apex.sdx.api.vo.query.SdxsjDescDataVo;
import com.apex.sdx.api.vo.query.SdxsjDescVo;
import com.apex.sdx.api.vo.query.SdxsjVo;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.service.TsdxSjService;
import com.apex.sdx.core.utils.StringUtils;
import com.apexsoft.LiveService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import sdx.query.ISdxsjService;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> Jingliang
 * @Date 2025-03-04
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "适当性事件查询服务")
@RequiredArgsConstructor
public class SdxsjService implements ISdxsjService {

    private final TsdxSjService sjService;

    @Override
    public QueryPageResponse<SdxsjVo> sdxsjcx(SdxsjcxReq req) throws Exception {
        Assert.notNull(req, SdxsjcxReq::getKhh);
        QueryPageResponse<SdxsjVo> result = new QueryPageResponse<>(1, "查询成功");
        String clzt = req.getClzt();
        String khh = req.getKhh();

        Page<SdxsjVo> page = sjService.queryByKhhAndClzt(khh, clzt, req.isSearchCount(), req.getPagesize(), req.getPagenum());
        result.page(page);
        return result;
    }

    @Override
    public SdxsjDescRes sdxsjDesc(Long id) {
        Assert.notNull(id, "事件id不能为空");
        List<SdxsjDescVo> list = sjService.querySdxsjDesc(id);
        if(list==null || list.isEmpty()) {
            return SdxsjDescRes.builder().code(0).note("暂无数据").build();
        }
        String sjxq = list.get(0).getSjxq();
        if(StringUtils.isEmpty(sjxq)) {
            sjxq = "{}";
        }
        JSONObject sjxqJson = JSON.parseObject(sjxq);
        /*
        json [a->11 b->22 c->[] d->33]
         */
        SdxsjDescRes result = SdxsjDescRes.builder().code(1).note("查询成功").build();
        // JSONObject data = new JSONObject();
        for (SdxsjDescVo vo : list) {
            // 1|字符;2|数值;3|数组;5|JSON数组
            if("jgsm".equals(vo.getYsdm())) {
                // 结果说明
                result.setJgsm(vo.getBzsm());
            } else if("sjxq".equals(vo.getYsdm())) {
                // 事件详情
                String bzsm = vo.getBzsm();
                result.setSjxq(replaceTemplate(bzsm, sjxqJson));
            }
        }
        result.setDescList(buildDescList(list, sjxqJson, null));
        return result;
    }

    private List<SdxsjDescDataVo> buildDescList(List<SdxsjDescVo> list, JSONObject sjxqJson, String fysdm) {
        List<SdxsjDescDataVo> descList = new ArrayList<>();
        for (SdxsjDescVo vo : list) {
            if("jgsm".equals(vo.getYsdm()) || "sjxq".equals(vo.getYsdm()) || (!StringUtils.isEmpty(fysdm) && !fysdm.equals(vo.getFysdm()))) {
                // 结果说明和事件详情不参与构建
                // 父要素代码不相等时，跳过
                continue;
            }
            SdxsjDescDataVo data = new SdxsjDescDataVo();
            // 1|字符;2|数值;5|JSON数组
            Integer sjlx = vo.getSjlx();
            if (sjlx == null) {
                sjlx = 1;
            }
            if((sjlx == 1 || sjlx == 2)
                    && (StringUtils.isEmpty(vo.getFysdm()) || vo.getFysdm().equals(fysdm))) {
                // 字符串或数值 -> 普通详情
                data.setYsdm(vo.getYsdm());
                data.setYsmc(vo.getYsmc());
                data.setValue(sjxqJson.getString(vo.getYsdm()));
                descList.add(data);
            } else if(sjlx == 5) {
                // JSON数组 -> 表格详情
                data.setYsdm(vo.getYsdm());
                data.setYsmc(vo.getYsmc());
                data.setValue(sjxqJson.getString(vo.getYsdm()));
                data.setColumns(buildDescList(list, sjxqJson, vo.getYsdm()));
                descList.add(data);
            }
        }
        return descList;
    }

    /**
     * 替换单个字符串中的模板变量
      */
    private static String replaceTemplate(String template, JSONObject replacementJson) {
        if(StringUtils.isEmpty(template)) {
            return "";
        }
        // 定义正则表达式模式，匹配${...}格式的变量
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(template);
        StringBuffer result = new StringBuffer();
        // 查找并替换所有匹配的变量
        while (matcher.find()) {
            String variableName = matcher.group(1);
            String replacement = replacementJson.getString(variableName);
            if(replacement == null) {
                replacement = "";
            }
            // 将匹配到的变量替换为实际值
            matcher.appendReplacement(result, "<span class=\"tips-important\">"
                    +Matcher.quoteReplacement(replacement)+"</span>");
        }
        matcher.appendTail(result);
        return result.toString();
    }

    @Override
    public QueryPageResponse<SdxsjVo> sdxtimeline(SdxsjcxReq req) throws Exception {
        Assert.notNull(req, SdxsjcxReq::getKhh);
        QueryPageResponse<SdxsjVo> result = new QueryPageResponse<>(1, "查询成功");

        String clzt = req.getClzt();
        String khh = req.getKhh();
        String ksrq = req.getKsrq();
        String jsrq = req.getJsrq();
        Page<SdxsjVo> page = sjService.queryByKhhAndClztAndRq(khh, ksrq, jsrq, clzt, req.isSearchCount(), req.getPagesize(), req.getPagenum());
        result.page(page);
        return result;
    }

    @Override
    public QueryPageResponse<SdxsjtjVo> sdxsjtj(SdxsjtjReq req) {
        QueryPageResponse<SdxsjtjVo> result = new QueryPageResponse<>(1, "查询成功");

        int tjwd = req.getTjwd();
        String ksrq = req.getKsrq();
        String jsrq = req.getJsrq();
        String khh = req.getKhh();
        String sjbm = req.getSjbm();
        Page<SdxsjtjVo> page = new Page<SdxsjtjVo>() ;
        if (tjwd == 1) {//所有事件
            page = sjService.compute(ksrq, jsrq, khh, sjbm, req.isSearchCount(), req.getPagesize(), req.getPagenum());
        } else if (tjwd == 2) {//每个事件
            page = sjService.compute4Sdxsj(ksrq, jsrq, khh, sjbm, req.isSearchCount(), req.getPagesize(), req.getPagenum());
        }
        result.page(page);
        return result;
    }
}
