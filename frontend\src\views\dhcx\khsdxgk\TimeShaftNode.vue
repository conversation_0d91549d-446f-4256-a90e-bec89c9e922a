<template>
  <div style="display: flex">
    <div :class="xSign > 0 ? 'bottom' : 'top'" style="display: flex; margin-right: 5px">
      <div style="background-color: #ffffff; border-radius: 50%; height: 15px; width: 15px"></div>
    </div>
    <div class="content" style="width: 250px; height: 80px; padding: 5px">
      <div class="timeline-date">
        {{ schedule.start }}
      </div>
      <div class="even-item">
        <div class="item-title" :class="{ active: schedule.highlight }">
          <a-tag :bordered="false" color="processing">{{ schedule.title }}</a-tag>
          <span>ID {{ schedule.id }}</span>
        </div>
        <div class="item-desc">
          {{ schedule.description }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Tag } from 'ant-design-vue';
// export default {
//   components: {
//     'a-tag': Tag // 显式注册组件
//   }
// }
export default {
  components: {
    'a-tag': Tag // 显式注册组件
  },
  // 注入 getNode 函数
  inject: ['getNode'],
  data() {
    return {
      schedule: [],
      time: '',
      xSign: 1
    };
  },
  computed: {
    // 这里可以添加计算属性，例如之前的 color, extraBorederColor 等
    // 由于代码中注释掉了部分逻辑，这里暂时不添加
  },
  created() {
    // 注入 getNode 函数并获取节点实例
    const node = this.getNode();
    this.schedule = node.getData()?.schedule || [];
    this.time = node.getData()?.time || '';
    this.xSign = node.getData()?.xSign || 1;
  },
  mounted() {
    // 在 mounted 中监听数据变化
    // node.on('change:data', ({ current }) => {
    //   this.schedule = current?.schedule || [];
    // });
  },
  methods: {
    // 修改处理任务点击事件的方法
    handleTaskClick() {
      const node = this.getNode();
      node.getData().openDetailModal(this.schedule.id);
    }
  }
};
</script>

<style >

/* 每一个时间轴块 */
.timeline-item {
  flex: 0 0 auto; /* 防止 item 自动缩小，保证水平排列 */
  min-width: 6rem;
  margin-right: 2rem;
  text-align: center;
}

/* 日期文本 */
.timeline-date {
  font-size: 12px;
  color: #6b7280;
  padding-bottom: 10px;
  text-align: left;
}

.triangle-item {
  display: flex;
  align-items: center;
  gap: 0; /* 控制三角形和横线的间距 */
  padding-bottom: 20px;
}

/* 小三角形，指向上方 */
.triangle {

  width: 0;
  height: 0;
  margin-bottom: 3px;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 14px solid #E9F0FF;
}

.line {
  height: 2px;
  width: 145px;
  margin-left: -3px;
  margin-right: -35px;
  background-color: #C9CDD4;
}

.even-item {
  line-height: 20px;
  padding-bottom: 10px;
}

/* 事件标题 */
.item-title {
  color: #AAAAAA;
  font-size: 14px;
  padding-bottom: 10px;
}

/* 高亮时的样式 */
.item-title.active {
  color: #2563eb; /* 示例：蓝色 */
}

/* 事件描述 */
.item-desc {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.25rem;
}
</style>
