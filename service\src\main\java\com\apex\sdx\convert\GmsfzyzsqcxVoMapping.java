package com.apex.sdx.convert;

import com.apex.sdx.api.vo.khgl.GmsfzyzsqcxVo;
import com.apex.sdx.core.converter.IMapping;
import com.apex.sdx.core.converter.MapStructConfig;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTgmsfcxsq;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025-02-14
 * @Description:
 */
@Mapper(config = MapStructConfig.class)
@Component
public interface GmsfzyzsqcxVoMapping extends IMapping<GmsfzyzsqcxVo, TOdsYgtCifTgmsfcxsq> {
}
