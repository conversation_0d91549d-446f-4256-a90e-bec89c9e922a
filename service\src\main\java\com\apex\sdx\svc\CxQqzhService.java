package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.KhhReq;
import com.apex.sdx.api.resp.query.CxqqzhRes;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhqqxx;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhqqxxService;
import com.apex.sdx.core.mybatis.service.VxtdmService;
import com.apexsoft.LiveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.query.ICxQqzhService;

/**
 * <AUTHOR>
 * @Date 2025/2/7 16:10
 * @Description: 查询信用账户信息
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "查询信用账户信息")
public class CxQqzhService implements ICxQqzhService {
    @Autowired
    private VxtdmService vxtdmService;
    @Autowired
    private TOdsYgtCifTkhqqxxService tOdsYgtCifTkhqqxxService;
    @Override
    public CxqqzhRes check(KhhReq req) {
        Assert.notNull(req, KhhReq::getKhh);
        return null;
    }

    @Override
    public CxqqzhRes execute(KhhReq req) {
        TOdsYgtCifTkhqqxx tOdsYgtCifTkhqqxx = tOdsYgtCifTkhqqxxService.getQqzhjbxx(req.getKhh());
        if(tOdsYgtCifTkhqqxx == null) {
            throw new BusinessException(-1, "改客户号"+req.getKhh()+"的期权账户不存在");
        }
        Integer tzzfl = tOdsYgtCifTkhqqxx.getTzzfl();
        String tzzflmc = "";
        if(tzzfl != null) {
            tzzflmc = vxtdmService.getNote("QQ_TZZFL", tzzfl+"");
        }
        return CxqqzhRes.builder().khh(tOdsYgtCifTkhqqxx.getKhh())
                .ywzh(tOdsYgtCifTkhqqxx.getYwzh())
                .pjzf(tOdsYgtCifTkhqqxx.getPjzf())
                .shxgedsx(tOdsYgtCifTkhqqxx.getShxgedsx())
                .szxgedsx(tOdsYgtCifTkhqqxx.getSzxgedsx())
                .tzzfl(tzzfl)
                .tzzflmc(tzzflmc)
                .build();
    }
}
