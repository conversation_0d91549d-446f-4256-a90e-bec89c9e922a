package sdx.query;

import com.apex.sdx.api.req.query.YwxtsdxReq;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.sdx.YwxtsdxTjVo;
import com.apexsoft.LiveMethod;

/**
 * @Description: 业务系统适当性统计
 */
public interface IYwxtsdxService {
    
    /**
     * 根据日期统计业务系统适当性情况
     */
    @LiveMethod(paramAsRequestBody = true, note = "业务系统适当性统计")
    QueryResponse<YwxtsdxTjVo> tjYwxtSdxByRq(YwxtsdxReq req) throws Exception;
} 