<template>
  <div class="zhsdx-content">
    <div class="module-title">
      账户适当性
    </div>
    <a-divider style="margin: 12px 0"/>
    <a-row>
      <a-col :span="16" class="sdxjbsx-content">
        <div class="module-sub-title">
          适当性基本属性
        </div>
        <a-descriptions :bordered="true" :column="2" size="small" :label-style="{width: '22%'}"
                        :content-style="{width: '28%'}">
          <a-descriptions-item label="客户号">{{ khjbxx?.khh }}</a-descriptions-item>
          <a-descriptions-item label="客户名称">{{ khjbxx?.khmc }}</a-descriptions-item>
          <a-descriptions-item label="一户通号">{{ khjbxx?.cid }}</a-descriptions-item>
          <a-descriptions-item label="开户日期">{{ khjbxx?.khrq }}</a-descriptions-item>
          <a-descriptions-item label="风险承受能力">{{
              getDcNote("SDX_FXCSNL", khjbxx?.cpdj, dictArray)
            }}
          </a-descriptions-item>
          <a-descriptions-item label="最近一次风测日期">{{ khjbxx?.cprq }}</a-descriptions-item>
          <a-descriptions-item label="投资者分类">{{
              getDcNote("SDX_TZZFL", khjbxx?.tzzfl, dictArray)
            }}
          </a-descriptions-item>
          <a-descriptions-item label="专业投资者认定日期" v-if="khjbxx?.tzzpdyxq">{{
              khjbxx?.tzzpdrq
            }}
          </a-descriptions-item>
          <a-descriptions-item label="专业投资者认定有效期" v-if="khjbxx?.tzzpdyxq">{{
              khjbxx?.tzzpdyxq
            }}
          </a-descriptions-item>
          <a-descriptions-item label="投资品种">{{
              getDcNote("SDX_TZPZ", khjbxx?.tzpz, dictArray)
            }}
          </a-descriptions-item>
          <a-descriptions-item label="投资期限">{{
              getDcNote("SDX_TZQX", khjbxx?.tzqx, dictArray)
            }}
          </a-descriptions-item>
          <a-descriptions-item label="预期收益">{{
              getDcNote("SDX_YQSY", khjbxx?.yqsy, dictArray)
            }}
          </a-descriptions-item>
        </a-descriptions>
        <div class="module-sub-title" style="padding-top: 10px">
          账户适当性评估
        </div>
        <a-descriptions :bordered="true" :column="2" size="small" :label-style="{width: '22%'}"
                        :content-style="{width: '28%'}">
          <a-descriptions-item label="证件有效期">{{ khjbxx?.zjjzrq }}</a-descriptions-item>
          <a-descriptions-item label="账户状态">{{
              getDcNote("SDX_KHZT", khjbxx?.khzt, dictArray)
            }}
          </a-descriptions-item>
          <a-descriptions-item label="反洗钱风险等级">{{
              getDcNote("SDX_XQFXDJ", khjbxx?.xqfxdj, dictArray)
            }}
          </a-descriptions-item>
          <a-descriptions-item label="风险测评有效期">{{ khjbxx?.cpyxq }}</a-descriptions-item>
          <a-descriptions-item label="专业投资者测评">{{
              getDcNote("SDX_TZZFL", khjbxx?.tzzfl, dictArray)
            }}
          </a-descriptions-item>
          <a-descriptions-item label="其他信息"></a-descriptions-item>
        </a-descriptions>
      </a-col>
      <a-col :span="8" style="border-left: rgba(5, 5, 5, 0.06) solid 1px; padding-left: 10px">
        <div class="khbq-title">客户标签</div>
        <div class="khbq-container">
          <!-- 四个同心圆 -->
          <div class="ring ring1"></div>
          <div class="ring ring3"></div>
          <!-- 中心头像，其实可以用图片替换，这里仅示例 -->
          <div class="center-avatar">
            <!-- 如果有真实头像图片，可以这样写 -->
            <!-- <img src="avatar.png" alt="头像"/> -->
            <span>头像</span>
          </div>
          <!-- 环绕的7个标签示例 -->
          <div class="tag tag--red tag1">异常账户</div>
          <div class="tag tag--normal tag2">激进型</div>
          <div class="tag tag--normal tag3">A类专业<br>投资者</div>
          <div class="tag tag--normal tag4">交易所<br>重点关注</div>
          <div class="tag tag--red tag5">黑名单<br>客户</div>
          <div class="tag tag--normal tag6">最低客户<br>标识</div>
          <div class="tag tag--normal tag7">中登<br>重点关注</div>
        </div>
      </a-col>
    </a-row>
  </div>
  <a-row>
    <a-col :span="12">
      <div class="module-container" style="margin-right: 5px">
        <div class="module-title">
          业务账户
          <span style="padding: 0 10px; color: #888888;font-size: 12px">数据时间: {{ dataDate }}</span>
        </div>
        <a-divider style="margin: 12px 0"/>
        <a-row>
          <a-col :span="12">
            <a-row>
              <a-col :span="8" class="icon-layout">
                <span class="iconfont icon-jzjy" style="font-size: 30px"></span>
              </a-col>
              <a-col :span="16" class="ywzh-content">
                <div>集中交易</div>
                <div style="color: #888888">{{ ywzhJzjyData?.ywzh }} /
                  {{ getDcNote("SDX_KHZT", ywzhJzjyData?.zhzt, dictArray) }}
                </div>
              </a-col>
            </a-row>
          </a-col>
          <a-col :span="12">
            <a-row>
              <a-col :span="8" class="icon-layout">
                <span class="iconfont icon-rzrq" style="font-size: 30px"></span>
              </a-col>
              <a-col :span="16" class="ywzh-content">
                <div>融资融券</div>
                <div style="color: #888888">{{ ywzhRzrqData?.ywzh }} /
                  {{ getDcNote("SDX_KHZT", ywzhRzrqData?.zhzt, dictArray) }}
                </div>
                <a-progress
                    :stroke-color="{'0%': '#108ee9','100%': '#87d068'}"
                    size="small"
                    :percent="100"
                    style="width: 65%" v-if="ywzhRzrqData?.sdxjg == 1"
                >
                  <template #format="percent">
                    <span>适当</span>
                  </template>
                </a-progress>
                <a-progress
                    :stroke-color="{'0%': '#FF7800','100%': '#FF1919'}" v-if="ywzhRzrqData?.sdxjg == -1"
                    size="small"
                    :percent="100"
                    style="width: 65%"
                >
                  <template #format="percent">
                    <span style="color: #FF1919">不适当</span>
                  </template>
                </a-progress>
              </a-col>
            </a-row>
          </a-col>
        </a-row>
        <a-divider style="margin: 12px 0"/>
        <a-row>
          <a-col :span="12">
            <a-row>
              <a-col :span="8" class="icon-layout">
                <span class="iconfont icon-gpqq" style="font-size: 30px"></span>
              </a-col>
              <a-col :span="16" class="ywzh-content">
                <div>股票期权</div>
                <div style="color: #888888">{{ ywzhGpqqData?.ywzh }} /
                  {{ getDcNote("SDX_KHZT", ywzhGpqqData?.zhzt, dictArray) }}
                </div>
                <a-progress
                    :stroke-color="{'0%': '#108ee9','100%': '#87d068'}"
                    size="small"
                    :percent="100"
                    style="width: 65%" v-if="ywzhGpqqData?.sdxjg == 1"
                >
                  <template #format="percent">
                    <span>适当</span>
                  </template>
                </a-progress>
                <a-progress
                    :stroke-color="{'0%': '#FF7800','100%': '#FF1919'}" v-if="ywzhGpqqData?.sdxjg == -1"
                    size="small"
                    :percent="100"
                    style="width: 65%"
                >
                  <template #format="percent">
                    <span style="color: #FF1919">不适当</span>
                  </template>
                </a-progress>
              </a-col>
            </a-row>
          </a-col>
          <a-col :span="12">
            <a-row>
              <a-col :span="8" class="icon-layout">
                <span class="iconfont icon-cwyw" style="font-size: 30px"></span>
              </a-col>
              <a-col :span="16" class="ywzh-content">
                <div>场外业务</div>
                <div style="color: #888888">{{ ywzhCwywData?.ywzh }} /
                  {{ getDcNote("SDX_KHZT", ywzhCwywData?.zhzt, dictArray) }}
                </div>
              </a-col>
            </a-row>
          </a-col>
        </a-row>
        <a-divider style="margin: 12px 0"/>
        <div class="module-title">
          <span>产品持仓适当性</span>
          <span style="padding: 0 10px; color: #888888;font-size: 12px">数据时间: {{ dataDate }}</span>
          <div class="iconfont icon-cxmx"
               style="color: #BF935F;  margin-left: auto;font-size: 14px; cursor: pointer"
               @click="open = true"
          >
            场内外持仓明细
          </div>
          <cnwccmx-modal v-model:open="open" :dictArray="dictArray" :khjbxx="khjbxx" title="场内外持仓明细"/>
        </div>
        <a-divider style="margin: 12px 0"/>
        <a-row>
          <a-col :span="12">
            <a-row style="padding: 30px 0 0 10px">
              <a-col :span="8" class="icon-layout">
                <span class="iconfont icon-kfsjjcc" style="font-size: 50px"></span>
              </a-col>
              <a-col :span="16" class="ywzh-content">
                <div>开放式基金持仓</div>
                <div><span style="font-weight: bold;font-size: 28px;">{{ cpccsdxMod.KH_KGSJJ_CCSL }}</span> 只</div>
              </a-col>
            </a-row>
          </a-col>
          <a-col :span="12" class="ywzh-content">
            <div>适配 <span style="padding: 0 10px">{{ cpccsdxMod.KH_KGSJJ_SDSL }}</span><span
                style="color: #888888">{{ cpccsdxMod.KH_KGSJJ_SDZB }}</span></div>
            <a-progress
                :stroke-color="{'0%': '#36A6FC','100%': '#36A6FC'}"
                size="small"
                :percent="Number(cpccsdxMod.KH_KGSJJ_SDZB.replace('%', ''))"
                :show-info="false"
                style="width: 65%"
            />
            <div>不适配 <span style="padding: 0 10px">{{ cpccsdxMod.KH_KGSJJ_BSDSL }}</span><span
                style="color: #888888">{{ cpccsdxMod.KH_KGSJJ_BSDZB }}</span></div>
            <a-progress
                :stroke-color="{'0%': '#FFC730','100%': '#FFC730'}"
                size="small"
                :percent="Number(cpccsdxMod.KH_KGSJJ_BSDZB.replace('%', ''))"
                :show-info="false"
                style="width: 65%"
            />
          </a-col>
        </a-row>
        <a-divider style="margin: 12px 0"/>
        <a-row>
          <a-col :span="12">
            <a-row style="padding: 30px 0 0 10px">
              <a-col :span="8" class="icon-layout">
                <span class="iconfont icon-cwcpcc" style="font-size: 50px"></span>
              </a-col>
              <a-col :span="16" class="ywzh-content">
                <div>场外产品持仓</div>
                <div><span style="font-weight: bold;font-size: 28px;">{{ cpccsdxMod.KH_OTC_CCSL }}</span> 只</div>
              </a-col>
            </a-row>
          </a-col>
          <a-col :span="12" class="ywzh-content">
            <div>适配 <span style="padding: 0 10px">{{ cpccsdxMod.KH_OTC_SDSL }}</span><span
                style="color: #888888">{{ cpccsdxMod.KH_OTC_SDZB }}</span></div>
            <a-progress
                :stroke-color="{'0%': '#36A6FC','100%': '#36A6FC'}"
                size="small"
                :percent="Number(cpccsdxMod.KH_OTC_SDZB.replace('%', ''))"
                :show-info="false"
                style="width: 65%"
            />
            <div>不适配 <span style="padding: 0 10px">{{ cpccsdxMod.KH_OTC_BSDSL }}</span><span style="color: #888888">{{ cpccsdxMod.KH_OTC_BSDZB }}</span>
            </div>
            <a-progress
                :stroke-color="{'0%': '#FFC730','100%': '#FFC730'}"
                size="small"
                :percent="Number(cpccsdxMod.KH_OTC_BSDZB.replace('%', ''))"
                :show-info="false"
                style="width: 65%"
            />
          </a-col>
        </a-row>
      </div>
    </a-col>
    <a-col :span="12">
      <div class="module-container" style="margin-left: 5px">
        <div class="module-title">
          业务适当性
          <span style="padding: 0 10px; color: #888888;font-size: 12px">数据时间: {{ dataDate }}</span>
        </div>
        <a-divider style="margin: 12px 0"/>
        <div class="show_qjywsdx">
          <div class="qjywsdx_item"><p><font>业务开通数量</font><br/><b>{{ ywsdxMap.ktsl }}</b></p>
          </div>
          <div class="qjywsdx_show">
            <ul>
              <li><span></span>
                <p><font>适当</font><font>{{ ywsdxMap.sdsl }}
                  ({{ parseFloat((ywsdxMap.sdzb * 100).toString()).toFixed(0) }}%)</font></p></li>
              <li><span></span>
                <p><font>不适当</font><font>{{ ywsdxMap.bsdsl }}
                  ({{ parseFloat((ywsdxMap.bsdzb * 100).toString()).toFixed(0) }}%)</font></p></li>
              <li><span></span>
                <p><font>不通过</font><font>{{ ywsdxMap.btgsl }}
                  ({{ parseFloat((ywsdxMap.btgzb * 100).toString()).toFixed(0) }}%)</font></p></li>
            </ul>
          </div>
        </div>
        <div class="module-title">
          持有服务咨询产品
          <span style="padding: 0 10px; color: #888888;font-size: 12px">数据时间: {{ dataDate }}</span>
          <div class="iconfont icon-cxmx" style="color: #BF935F;  margin-left: auto;font-size: 14px;cursor: pointer"
               @click="open1 = true" >服务订阅明细
          </div>
          <fwdymx-modal v-model:open="open1" :dictArray="dictArray" :khjbxx="khjbxx" title="服务订阅明细"/>
        </div>
        <a-divider style="margin: 12px 0"/>
        <a-row>
          <a-col :span="12">
            <div class="chart-title">
              投顾产品 <span style="padding: 0 5px">总数量:</span> <span
                style="font-weight: bold">{{ cyfwzxcpMod.KH_TGCP_CCSL }}</span>
            </div>
            <div style="height: 225px">
              <pie-chart title="tgcp" :self_pie_data="cyfwzxcpMod.KH_TGCP_PIEDATA"/>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="chart-title">
              公募投顾产品 <span style="padding: 0 5px">总数量:</span> <span
                style="font-weight: bold">{{ cyfwzxcpMod.KH_GMTGCP_CCSL }}</span>
            </div>
            <div style="height: 225px">
              <pie-chart title="gmtgcp" :self_pie_data="cyfwzxcpMod.KH_GMTGCP_PIEDATA"/>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-col>
  </a-row>
  <div class="sdxsj-content">
    <div class="module-title">
      适当性事件
    </div>
    <a-divider style="margin: 12px 0"/>
    <a-row>
      <a-col :span="6">
        <div class="chart-title">
          事件分布<span style="padding: 0 5px">总数量:</span> <span style="font-weight: bold">{{
            sdxsjData.sjsl
          }}</span>
        </div>
        <a-row style="padding: 30px 0 0 10px">
          <a-col :span="8" class="sjfb-icon-layout">
            <div class="sjfb-iconcontainer1">
              <span class="iconfont icon-ycl" style="font-size: 30px"></span>
            </div>
          </a-col>
          <a-col :span="16" class="ywzh-content">
            <div>已处理</div>
            <div>
              <span style="font-weight: bold;font-size: 20px;">{{ sdxsjData.clsl }}</span>
              <span style="color: #888888;font-size: 14px;padding-left: 5px">({{
                  parseFloat((sdxsjData.clzb * 100).toString()).toFixed(2)
                }}%)</span>
            </div>
          </a-col>
        </a-row>
        <a-progress
            :stroke-color="{'0%': '#108ee9','100%': '#87d068'}"
            size="small"
            :percent="sdxsjData.clzb * 100"
            :show-info="false"
            style="width: 65%;padding-left: 30px;"
        />
        <a-row style="padding: 30px 0 0 10px">
          <a-col :span="8" class="sjfb-icon-layout">
            <div class="sjfb-iconcontainer2">
              <span class="iconfont icon-dcl" style="font-size: 30px"></span>
            </div>
          </a-col>
          <a-col :span="16" class="ywzh-content">
            <div>待处理</div>
            <div>
              <span style="font-weight: bold;font-size: 20px;">{{ sdxsjData.dclsl }}</span>
              <span style="color: #888888;font-size: 14px;padding-left: 5px">({{
                  parseFloat((sdxsjData.dclzb * 100).toString()).toFixed(2)
                }}%)</span>
            </div>
          </a-col>
        </a-row>
        <a-progress
            :stroke-color="{'0%': '#FF7800','100%': '#F4D458'}"
            size="small"
            :percent="sdxsjData.dclzb * 100"
            :show-info="false"
            style="width: 65%;padding-left: 30px;"
        />
      </a-col>
      <a-col :span="18">
        <div class="sjsjz-title">
          事件时间轴
          <div style="margin-left: auto;">
            <a-segmented v-model:value="sdxsjzRqRange" :options="dateOptions" size="small" @change="changeRqRange()"/>
            <a-switch v-model:checked="sdxsjzChecked" style="margin: 0 10px 0 20px"/>
            <span style="font-weight: 400;font-size: 12px;color: #333333;">仅看未处理</span>
          </div>
        </div>
        <div class="sjsjz-content">
          <div class="timeline-scroller">
            <TimerShaft :TimerShaftData="filteredEvents"/>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import {defineComponent} from "vue";
import PieChart from "@views/dhcx/khsdxgk/pieChart.vue";
import {getDcNote} from "../../../utils/bussinessUtils";
import ModalComponent from "@components/ModalComponent.vue";
import CnwccmxModal from "@views/dhcx/khsdxgk/cnwccmxModal.vue";
import FwdymxModal from "@views/dhcx/khsdxgk/fwdymxModal.vue";
import {message} from "ant-design-vue";
import {commonApi} from "@/api/common";
import dayjs from 'dayjs'; //不用moment
import TimerShaft from '@/views/dhcx/khsdxgk/TimerShaft.vue'

export default defineComponent({
  name: "khsdxgk",

  components: {CnwccmxModal, ModalComponent, PieChart, TimerShaft, FwdymxModal},
  props: ['dictArray', 'khjbxx'],
  inject: ["khh", "LastTradingDay"],//不通过props传递数据
  data() {
    return {
      dataDate: '',//数据时间
      //业务账户
      ywzhJzjyData: null,
      ywzhRzrqData: null,
      ywzhGpqqData: null,
      ywzhCwywData: null,
      //适当性事件时间轴 //查询数据用
      currentTimeLine: 1,
      pageSizeTimeLine: 10,
      totalTimeLine: 0,
      //日期区间范围使用
      dateOptions: ["近一个月", "近半年", "近一年"],
      /*用sdxsjzRqRange,sdxsjzChecked代替
      value: "近一个月",
      checked: false,
       */
      dateOptionsFw: {
        "近一个月": null,
        "近半年": null,
        "近一年": null,
      },
      sdxsjzRqRange: "近一个月",//这个是动态变化
      sdxsjzRqRangeOld: "",//这个存储历史的数据 暂未使用
      sdxsjzChecked: false,
      events: [
        {
          date: "06/28",
          title: "事件生成",
          id: 1233,
          description: "风险测评过期期指示",
          highlight: false,
        },
        {
          date: "07/04",
          title: "事件消除",
          id: 1233,
          description: "风险测评过期期指示",
          highlight: false,
        },
        {
          date: "07/12",
          title: "事件生成",
          id: 1234,
          description: "专业投资者即将到期",
          highlight: true,
        },
        {
          date: "07/12",
          title: "事件生效",
          id: 1235,
          description: "债券合格投资者到期",
          highlight: false,
        },
        {
          date: "07/13",
          title: "事件消除",
          id: 1235,
          description: "债券合格投资者到期",
          highlight: false,
        },
        {
          date: "07/24",
          title: "事件生成",
          id: 1236,
          description: "一户通信息风险不一致",
          highlight: false,
        },
        {
          date: "07/28",
          title: "事件生效",
          id: 1238,
          description: "资产合格投资者即将到期",
          highlight: false,
        },
      ],
      //khh2: '',
      open: false,
      open1: false,
      sdxsjData: {
        "sjsl": "0",//适当性事件_分布_总数
        "clsl": "0",
        "clzb": "0",
        "dclsl": "0",
        "dclzb": "0",
      },
      cpccsdxMod: {
        'KH_KGSJJ_CCSL': "0",//客户开放式基金持仓数量
        'KH_KGSJJ_SDSL': "0",//客户开放式基金持仓适当数量
        'KH_KGSJJ_SDZB': "0%",//客户开放式基金持仓适当占比
        'KH_KGSJJ_BSDSL': "0",//客户开放式基金持仓不适当数量
        'KH_KGSJJ_BSDZB': "0%",//客户开放式基金持仓不适当占比
        'KH_OTC_CCSL': "0",//客户场外产品持仓数量
        'KH_OTC_SDSL': "0",//客户场外产品持仓适当数量
        'KH_OTC_SDZB': "0%",//客户场外产品持仓适当占比
        'KH_OTC_BSDSL': "0",//客户场外产品持仓不适当数量
        'KH_OTC_BSDZB': "0%",//客户场外产品持仓不适当占比
      },
      //持有服务咨询产品
      cyfwzxcpMod: {
        //第一部分: 交易权限 适当性的饼图
        'KH_TGCP_CCSL': '0',
        'KH_TGCP_PIEDATA': {
          "data": [
            {value: 0, name: '适当'},
            {value: 0, name: '不适当'},
          ], datazb: ['0%', '0%']
        },
        //第二部分: 两融权限 适当性的饼图
        'KH_GMTGCP_CCSL': '0',
        'KH_GMTGCP_PIEDATA': {
          "data": [
            {value: 0, name: '适当'},
            {value: 0, name: '不适当'},
          ], datazb: ['0%', '0%']
        },
      },
      ywsdxMap: {btgzb: 0, bsdzb: 0, sdzb: 0, btgsl: 0, bsdsl: 0, sdsl: 0, ktsl: 0},//业务适当性
    };
  },
  computed: {
    // 计算过滤后的事件数据
    filteredEvents() {
      if (this.sdxsjzChecked) {
        // 仅看未处理，过滤出未处理的事件
        return this.events.filter(event => event.highlight === false);
      }
      // 显示所有事件
      return this.events;
    }
  },
  watch: {
    LastTradingDay: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.getCpccsdx();
          this.getYwsdx();
          this.getYwzhxx();
          this.dataDate = dayjs(this.LastTradingDay).format('YYYY-MM-DD');
        }
      }
    }
  },
  methods: {
    getDcNote,
    querySdxsjData() {
      commonApi.executeAMS(
          "sdx.query.ISdxsjService",
          "sdxsjtj",
          {khh: this.khh},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.sdxsjData = res.records[0] || []
      }).finally(() => {
      })
    },
    getCpccsdx() {//获取_产品持仓适当性
      commonApi.executeAMS(
          "sdx.query.ICpccsdxService",
          "qtcccpSdxtj",
          {khh: this.khh, rq: this.LastTradingDay,},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        let data = res.records || [];
        if (data.length > 0) {
          data.forEach((item) => {
            let ywxt = item.ywxt;
            if (ywxt == '1003') {//场外产品持仓
              this.cpccsdxMod.KH_KGSJJ_CCSL = item.ccsl;
              this.cpccsdxMod.KH_KGSJJ_SDSL = item.sdsl;
              this.cpccsdxMod.KH_KGSJJ_BSDSL = item.bsdsl;
              this.cpccsdxMod.KH_KGSJJ_SDZB = (item.sdzb * 100).toFixed(2) + "%";
              this.cpccsdxMod.KH_KGSJJ_BSDZB = (item.bsdzb * 100).toFixed(2) + "%";
            } else if (ywxt == '1000') {//开放式基金持仓
              this.cpccsdxMod.KH_OTC_CCSL = item.ccsl;
              this.cpccsdxMod.KH_OTC_SDSL = item.sdsl;
              this.cpccsdxMod.KH_OTC_BSDSL = item.bsdsl;
              this.cpccsdxMod.KH_OTC_SDZB = (item.sdzb * 100).toFixed(2) + "%";
              this.cpccsdxMod.KH_OTC_BSDZB = (item.bsdzb * 100).toFixed(2) + "%";
            } else if (ywxt == '1004') {//投顾产品
              this.cyfwzxcpMod.KH_TGCP_CCSL = item.ccsl;
              this.cyfwzxcpMod.KH_TGCP_PIEDATA = {
                data: [
                  {value: item.sdsl, name: '适当'},
                  {value: item.bsdsl, name: '不适当'},
                ],
                datazb: [(item.sdzb * 100) + "%", (item.bsdzb * 100) + "%"]
              };
            } else if (ywxt == '1005') {//公募投顾
              this.cyfwzxcpMod.KH_GMTGCP_CCSL = item.ccsl;
              this.cyfwzxcpMod.KH_GMTGCP_PIEDATA = {
                data: [
                  {value: item.sdsl, name: '适当'},
                  {value: item.bsdsl, name: '不适当'},
                ],
                datazb: [(item.sdzb * 100) + "%", (item.bsdzb * 100) + "%"]
              };
            }
          });
        }
      }).finally(() => {
      })
    },
    getYwsdx() {//获取业务适当性  done
      let today = dayjs().format("YYYYMMDD");
      commonApi.executeAMS(
          "sdx.query.IYktywsdxService",
          "khyktywsdxtj",
          {
            khh: this.khh,
            rq: this.LastTradingDay,
          },
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        if (res.code > 0) {
          this.ywsdxMap = res.records[0] || this.ywsdxMap;
          //this.ywsdxMap.rq = this.ywsdxMap.rq?.replace(/(\d{4})(\d{2})(\d{2})/, "$1-$2-$3");
        }
      })
    },
    changeRqRange() {//更改日期区间范围 触发查询事件
      //let oldfw = this.sdxsjzRqRangeOld;
      //let fw = this.sdxsjzRqRange;
      this.querySdxsjTimelineData();
    },
    querySdxsjTimelineData() {//一定是查询

      let rqFw = this.sdxsjzRqRange;
      let ksrqjsrq = this.dateOptionsFw[rqFw];
      if (ksrqjsrq == null) {//如果没有初始化,初始化一次
        let jsrq = dayjs().format("YYYYMMDD");
        if (rqFw == '近一个月') {
          ksrqjsrq = {
            "ksrq": dayjs().subtract(1, "month").format("YYYYMMDD"),
            "jsrq": jsrq
          }
        } else if (rqFw == '近半年') {
          ksrqjsrq = {
            "ksrq": dayjs().subtract(6, "month").format("YYYYMMDD"),
            "jsrq": jsrq
          }
        } else if (rqFw == '近一年') {
          ksrqjsrq = {
            "ksrq": dayjs().subtract(12, "month").format("YYYYMMDD"),
            "jsrq": jsrq
          }
        }
        this.dateOptionsFw[rqFw] = ksrqjsrq;//回填 初始化数据
      }
      let ksrq = ksrqjsrq['ksrq'] || "";
      let jsrq = ksrqjsrq['jsrq'] || "";
      commonApi.executeAMS(
          "sdx.query.ISdxsjService",
          "sdxtimeline",
          {
            khh: this.khh, clzt: this.clzt,
            ksrq: ksrq,
            jsrq: jsrq,
            isSearchCount: true, pagenum: this.currentTimeLine, pagesize: this.pageSizeTimeLine
          },
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        if (res.code > 0) {
          let newEventsList = [];
          let records = res.records || [];
          for (let i = 0; i < records.length; i++) {
            let sjObj = records[i];
            let done = !sjObj['clzt'] == '0';
            let eventObj = {
              date: sjObj['scrq'],
              title: done ? "事件消除" : "事件生成",
              id: sjObj['id'],
              description: sjObj['sjmc'] || sjObj['sjid'] || '',
              highlight: done,
            }
            newEventsList.push(eventObj);
          }
          this.events = newEventsList;
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    getYwzhxx() {
      commonApi.executeAMS(
          "sdx.query.ICxYwzhService",
          "execute",
          {khh: this.khh, rq: this.LastTradingDay},
      ).then((res) => {
        this.ywzhJzjyData = res?.jzjy;
        this.ywzhRzrqData = res?.rzrq;
        this.ywzhGpqqData = res?.gpqq;
        this.ywzhCwywData = res?.cwyw;
      }).finally(() => {
      })
    },
  },
  mounted() {
    this.querySdxsjData();//查询_事件分布

    /*this.getYwsdx();//获取业务适当性数据
    this.getCpccsdx();//获取_产品持仓适当性
    this.getYwzhxx();*/

    //20250424 适当性时间时间轴 默认查一次
    this.querySdxsjTimelineData();

  }
});
</script>
<style scoped>
.zhsdx-content {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.sdxsj-content {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.module-title:before {
  content: '';
  width: 4px;
  border-radius: 2px;
  height: 18px;
  background-color: #B48A3B;
  display: inline-block;
  vertical-align: middle;
  margin: 0 5px 0 5px;
}

.module-title {
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 30px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.sdxjbsx-content {
  padding: 0 20px 0 20px;
}

.module-sub-title:before {
  content: '';
  width: 8px;
  height: 8px;
  background: #B48A3B;
  transform: rotate(45deg);
  display: inline-block; /* 这个很重要，让伪元素变成块级元素 */
  flex-shrink: 0; /* 防止菱形被压缩 */
}

.module-sub-title {
  font-weight: 400;
  font-size: 14px;
  color: #000033;
  line-height: 28px;
  display: flex;
  align-items: center;
  gap: 8px; /* 控制菱形和文字的间距 */
  padding: 0 0 10px 10px;
}

.khbq-title {
  position: absolute;
  right: 18px;
  z-index: 99;
  top: 0px;
  font-size: 14px;
  color: #fff;
  background-color: #BF935F;
  display: inline-block;
  padding: 0 12px 0 8px;
  line-height: 32px;
}

.khbq-title:before {
  position: absolute;
  display: inline-block;
  content: "";
  right: 76px;
  top: 0px;
  border-left: 16px solid transparent;
  border-top: 16px solid #BF935F;
  border-bottom: 16px solid #BF935F;
  border-right: 0px solid #BF935F;
}

.khbq-title:after {
  position: absolute;
  display: inline-block;
  content: "";
  right: -6px;
  top: 2px;
  width: 6px;
  height: 32px;
  border-left: 1px solid #A27A4A;
  background-color: #A27A4A;
  transform: skewY(30deg);
}

.khbq-container {
}

/* 中间放置头像的区域 */
.center-avatar {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80px;
  height: 80px;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  z-index: 10; /* 保证在圆环之上 */
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
}

/* 可以把头像图片放这里，如果有实际链接可以替换 */
.center-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 同心圆环，越外层半径越大 */
.ring {
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  border: 2px solid #F4F6FE; /* 用稍淡的边框模拟圆环 */
  transform: translate(-50%, -50%);
}

.ring1 {
  width: 120px;
  height: 120px;
}

.ring2 {
  width: 180px;
  height: 180px;
}

.ring3 {
  width: 240px;
  height: 240px;
}

.ring4 {
  width: 300px;
  height: 300px;
}

/* 标签(小圆)公共样式 */
.tag {
  position: absolute;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px;
  font-size: 14px;
  line-height: 1.3;
  text-align: center;
  color: #666;
}

/* 普通标签: 浅黄色圆 */
.tag--normal {
  background: radial-gradient(farthest-corner at 20px 20px,
  rgba(255, 243, 224, 0) 0%,
  rgba(255, 243, 224, 0.5) 30%,
  rgba(255, 243, 224, 0.9) 80%,
  rgba(255, 243, 224, 1) 100%
  );
  color: #BF935F;
}

/* 红色标签 */
.tag--red {
  background: radial-gradient(farthest-corner at 20px 20px,
  rgba(255, 232, 235, 0) 0%,
  rgba(255, 232, 235, 0.5) 30%,
  rgba(255, 232, 235, 0.9) 80%,
  rgba(255, 232, 235, 1) 100%
  );
  color: #cc0000;
}

/* 以下根据示例图片，做大致位置摆放。可根据实际需求微调百分比 */
.tag1 { /* 异常账户 */
  top: 34%;
  left: 10%;
}

.tag2 { /* 激进型 */
  top: 12%;
  left: 26%;
}

.tag3 { /* A类专业投资者 */
  top: 16%;
  right: 24%;
}

.tag4 { /* 交易所重点关注 */
  top: 46%;
  right: 13%;
  transform: translateY(-50%);
}

.tag5 { /* 黑名单客户 */
  bottom: 22%;
  right: 18%;
}

.tag6 { /* 最低客户标识 */
  bottom: 20%;
  left: 18%;
}

.tag7 { /* 中登重点关注 */
  bottom: 10%;
  left: 50%;
  transform: translateX(-50%);
}

.module-container {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 10px;
  margin: 10px 0;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.icon-layout {
  text-align: center;
}

.ywzh-content {
  line-height: 28px;
  height: 89px;
}

.chart-title {
  padding-left: 10px;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 30px;
}

.sjsjz-title {
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 30px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.sjsjz-content {
  height: 250px;
  background: #F9FCFF;
  border-radius: 8px;
}

.sjfb-icon-layout {
  padding-left: 20px;
}

.sjfb-iconcontainer1 {
  width: 50px;
  height: 50px;
  background: #ECF8FF;
  border-radius: 6px;
  color: #35B5FF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sjfb-iconcontainer2 {
  width: 50px;
  height: 50px;
  background: #FCF7E3;
  border-radius: 6px;
  color: #F4D458;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-segmented .ant-segmented-item-selected) {
  background-color: #FBF4EC;
  color: #BF935F;
}

:deep(.ant-segmented) {
  background-color: white;
}

:deep(.ant-segmented .ant-segmented-item) {
  border-radius: 10px;
}

/* 横向滚动容器 */
.timeline-scroller {
  display: flex;
  flex-wrap: nowrap; /* 不换行，确保可以横向滚动 */
  overflow-x: auto; /* 允许超出部分横向滚动 */
  scrollbar-width: thin; /* Firefox 中的滚动条细一点，可根据需要定制 */
  height: 250px;
  padding: 10px;
}


:deep(.ant-switch.ant-switch-checked) {
  background: #bf935f
}

:deep(.ant-switch.ant-switch-checked:hover:not(.ant-switch-disabled)) {
  background: #bf935f
}


.show_qjywsdx {
  height: 225px;
}

.qjywsdx_item {
  display: inline-block;
  vertical-align: top;
  width: 34%;
  font-size: 14px;
  color: #222;
  padding: 0 30px;
}

.qjywsdx_item:before {
  content: "\e63d";
  margin-top: 75px;
  width: 78px;
  height: 80px;
  display: inline-block;
  padding-left: 2px;
  margin-right: 20px;
  font-family: "iconfont" !important;
  vertical-align: middle;
  line-height: 80px;
  text-align: center;
  border-radius: 40px;
  background-color: #fff5ea;
  color: #bf935f;
  font-size: 50px;
}

.qjywsdx_item p {
  display: inline-block;
  vertical-align: middle;
  width: calc(100% - 100px);
  margin-top: 75px;
}

.qjywsdx_item p b {
  font-size: 28px;
  font-weight: bold;
}

.qjywsdx_show {
  display: inline-block;
  vertical-align: top;
  width: 66%;
  padding: 25px 0;
}

.qjywsdx_show ul li {
  margin: 14px 30px 14px 0;
  height: 42px;
  position: relative;
  line-height: 42px;
}

.qjywsdx_show ul li:nth-child(1) {
  padding-left: 65px;
}

.qjywsdx_show ul li:nth-child(2) {
  padding-left: 180px;
}

.qjywsdx_show ul li:nth-child(3) {
  padding-left: 225px;
}

.qjywsdx_show ul li p font:nth-child(1) {
  color: #fff;
  padding-left: 25px;
}

.qjywsdx_show ul li p font {
  position: relative;
  z-index: 2;
  color: #fff;
  font-size: 14px;
  margin-right: 15px;
}

.qjywsdx_show ul li p:before {
  content: "";
  position: absolute;
  height: 42px;
  z-index: 0;
  width: 200px;
  border-radius: 4px;
  transform: skew(-28deg);
}

.qjywsdx_show ul li:nth-child(1) p:after {
  content: "";
  position: absolute;
  height: 42px;
  left: 275px;
  width: 30px;
  background-color: #a0d5fe;
  border-radius: 4px;
  transform: skew(-28deg);
}

.qjywsdx_show ul li:nth-child(2) p:after {
  content: "";
  position: absolute;
  height: 42px;
  left: 140px;
  width: 30px;
  background-color: #ffc730;
  border-radius: 4px;
  transform: skew(-28deg);
}

.qjywsdx_show ul li:nth-child(3) p:after {
  content: "";
  position: absolute;
  height: 42px;
  left: 435px;
  width: 30px;
  background-color: #ff8e8b;
  border-radius: 4px;
  transform: skew(-28deg);
}

.qjywsdx_show ul li span {
  position: absolute;
  left: 34px;
  top: 10px;
  display: inline-block;
  vertical-align: middle;
  border: 1px solid #d7bc9b;
  width: 12px;
  height: 12px;
  border-radius: 6px;
}

.qjywsdx_show ul li span:after {
  content: "";
  width: 16px;
  position: absolute;
  left: -4px;
  top: -4px;
  height: 16px;
  border-radius: 8px;
  border: 1px dashed #d7bc9b;
  box-shadow: 0px 0px 1px rgba(0, 0, 0, .3);
}

.qjywsdx_show ul li:nth-child(1) span:before {
  content: "";
  position: absolute;
  left: -35px;
  top: 8px;
  width: 25px;
  height: 40px;
  border-bottom: 1px solid #e5d4bf;
  border-right: 1px solid #e5d4bf;
  transform: skew(-28deg);
}

.qjywsdx_show ul li:nth-child(2) span {
  left: 108px;
}

.qjywsdx_show ul li:nth-child(3) span {
  left: 194px;
}

.qjywsdx_show ul li:nth-child(2) span:before {
  content: "";
  position: absolute;
  left: -115px;
  top: 8px;
  width: 110px;
  height: 20px;
  border-bottom: 1px solid #e5d4bf;
  border-right: 1px solid #e5d4bf;
  transform: skew(-28deg);
}

.qjywsdx_show ul li:nth-child(3) span:before {
  content: "";
  position: absolute;
  left: -205px;
  top: 8px;
  width: 200px;
  height: 0px;
  border-bottom: 1px solid #e5d4bf;
}

.qjywsdx_show ul li:nth-child(1) p:before {
  background-image: linear-gradient(to right, #36a6fc 10%, #a0d5fe 100%);
}

.qjywsdx_show ul li:nth-child(2) p:before {
  background-image: linear-gradient(to right, #ffc730 10%, #ffe7a7 100%);
}

.qjywsdx_show ul li:nth-child(3) p:before {
  background-image: linear-gradient(to right, #ff1d18 10%, #ff9997 100%);
}

</style>