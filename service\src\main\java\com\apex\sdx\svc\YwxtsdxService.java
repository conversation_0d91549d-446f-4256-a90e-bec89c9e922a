package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.YwxtsdxReq;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.sdx.YwxtsdxTjVo;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.mapper.YwxtsdxMapper;
import com.apexsoft.LiveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import sdx.query.IYwxtsdxService;

import java.util.List;

/**
 * @Description: 业务系统适当性统计实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
@LiveService(interceptor = QueryServiceInterceptor.class, note = "业务系统适当性统计")
public class YwxtsdxService implements IYwxtsdxService {

    private final YwxtsdxMapper ywxtsdxMapper;

    @Override
    public QueryResponse<YwxtsdxTjVo> tjYwxtSdxByRq(YwxtsdxReq req) throws Exception {
        // 参数校验
        Assert.notNull(req, YwxtsdxReq::getRq);
        
        try {
            // 构建结果
            QueryResponse<YwxtsdxTjVo> result = new QueryResponse<>(1, "查询成功");
            
            // 执行查询
            List<YwxtsdxTjVo> tjList = ywxtsdxMapper.tjYwxtSdxByRq(req.getRq());
            
            // 设置记录
            result.setRecords(tjList);
            return result;
        } catch (Exception e) {
            String note = String.format("统计业务系统适当性情况异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-701, note);
        }
    }
} 