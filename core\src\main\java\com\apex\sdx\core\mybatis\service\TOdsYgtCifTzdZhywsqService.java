package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTzdZhywsq;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *
 */
public interface TOdsYgtCifTzdZhywsqService extends IService<TOdsYgtCifTzdZhywsq> {

    /**
     *
     * @param khh
     * @param ksrq
     * @param jsrq
     * @param isSearchCount
     * @param pagesize
     * @param pagenum
     * @return
     */
    Page<TOdsYgtCifTzdZhywsq> queryJgkhxxhcsqByConditionos(String khh, Integer ksrq, Integer jsrq, boolean isSearchCount, int pagesize, int pagenum);
}
