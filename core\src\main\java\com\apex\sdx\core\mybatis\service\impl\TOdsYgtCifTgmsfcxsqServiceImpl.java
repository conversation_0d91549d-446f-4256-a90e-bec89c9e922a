package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.resp.common.Response;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhzlxg;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTgmsfcxsq;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTgmsfcxsqService;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTgmsfcxsqMapper;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class TOdsYgtCifTgmsfcxsqServiceImpl extends ServiceImpl<TOdsYgtCifTgmsfcxsqMapper, TOdsYgtCifTgmsfcxsq>
    implements TOdsYgtCifTgmsfcxsqService{

    @Override
    public Page<TOdsYgtCifTgmsfcxsq> queryPageByConditions(String khxm, Integer zjlb, String zjbh, Integer ksrq, Integer jsrq, String xmhcjg, String zjhcjg, String rxbdjg, Integer cxlx, Integer czlb, boolean isSearchCount, int pagesize, int pagenum) {
        Page<TOdsYgtCifTgmsfcxsq> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);

        try {
            LambdaQueryWrapper<TOdsYgtCifTgmsfcxsq> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TOdsYgtCifTgmsfcxsq::getSqxm, khxm);
            wrapper.eq(TOdsYgtCifTgmsfcxsq::getZjlb, zjlb);
            wrapper.eq(TOdsYgtCifTgmsfcxsq::getZjbh, zjbh);
            wrapper.ge(ksrq != null, TOdsYgtCifTgmsfcxsq::getSqrq, ksrq);
            wrapper.le(jsrq != null, TOdsYgtCifTgmsfcxsq::getSqrq, jsrq);
            wrapper.eq(xmhcjg != null && !xmhcjg.equals("无"), TOdsYgtCifTgmsfcxsq::getXmhcjg, xmhcjg);
            wrapper.eq(zjhcjg != null && !zjhcjg.equals("无"), TOdsYgtCifTgmsfcxsq::getZjbhhcjg, zjhcjg);
            wrapper.eq(rxbdjg != null && !rxbdjg.equals("无"), TOdsYgtCifTgmsfcxsq::getRxbdjg, rxbdjg);
            wrapper.eq(cxlx != null, TOdsYgtCifTgmsfcxsq::getCxlx, cxlx);
            wrapper.eq(czlb != null, TOdsYgtCifTgmsfcxsq::getCzlb, czlb);
            wrapper.orderByDesc(TOdsYgtCifTgmsfcxsq::getId);
            this.page(page, wrapper);
            return page;

        } catch (Exception e) {
            String note = String.format("获取公民身份验证信息申请失败[%s]", e.getMessage());
            log.error(note, e);
            throw new BusinessException(Response.buildErrorMsg(-601, note));
        }
    }
}




