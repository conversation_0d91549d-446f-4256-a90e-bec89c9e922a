<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.TuserMapper">

    <resultMap id="BaseResultMap" type="com.apex.sdx.core.mybatis.entity.Tuser">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="userid" column="UserID" jdbcType="VARCHAR"/>
            <result property="password" column="Password" jdbcType="VARCHAR"/>
            <result property="name" column="Name" jdbcType="VARCHAR"/>
            <result property="grade" column="Grade" jdbcType="DECIMAL"/>
            <result property="lastlogin" column="LastLogin" jdbcType="TIMESTAMP"/>
            <result property="logins" column="Logins" jdbcType="DECIMAL"/>
            <result property="chgpwdtime" column="ChgPwdTime" jdbcType="TIMESTAMP"/>
            <result property="chgpwdlimit" column="ChgPwdLimit" jdbcType="DECIMAL"/>
            <result property="status" column="Status" jdbcType="DECIMAL"/>
            <result property="iplimit" column="IPLimit" jdbcType="VARCHAR"/>
            <result property="certno" column="CertNo" jdbcType="VARCHAR"/>
            <result property="orgid" column="OrgID" jdbcType="DECIMAL"/>
            <result property="locktime" column="LockTime" jdbcType="TIMESTAMP"/>
            <result property="retrycount" column="RetryCount" jdbcType="INTEGER"/>
            <result property="lasttrytime" column="LastTryTime" jdbcType="TIMESTAMP"/>
            <result property="userattribute" column="UserAttribute" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,UserID,Password,
        Name,Grade,LastLogin,
        Logins,ChgPwdTime,ChgPwdLimit,
        Status,IPLimit,CertNo,
        OrgID,LockTime,RetryCount,
        LastTryTime,UserAttribute,Photo
    </sql>
</mapper>
