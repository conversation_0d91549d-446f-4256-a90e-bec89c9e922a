<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.TsdxjgQtcccpMapper">

    <resultMap id="BaseResultMap" type="com.apex.sdx.core.mybatis.entity.TsdxjgQtcccp">
            <id property="rq" column="RQ" jdbcType="DECIMAL"/>
            <id property="khh" column="KHH" jdbcType="VARCHAR"/>
            <id property="cpdm" column="CPDM" jdbcType="VARCHAR"/>
            <id property="sjly" column="SJLY" jdbcType="DECIMAL"/>
            <result property="khfxdj" column="KHFXDJ" jdbcType="DECIMAL"/>
            <result property="khtzpz" column="KHTZPZ" jdbcType="VARCHAR"/>
            <result property="khtzqx" column="KHTZQX" jdbcType="DECIMAL"/>
            <result property="khyqsy" column="KHYQSY" jdbcType="VARCHAR"/>
            <result property="cpfxdj" column="CPFXDJ" jdbcType="DECIMAL"/>
            <result property="cptzpz" column="CPTZPZ" jdbcType="DECIMAL"/>
            <result property="cptzqx" column="CPTZQX" jdbcType="DECIMAL"/>
            <result property="cpyqsy" column="CPYQSY" jdbcType="DECIMAL"/>
            <result property="fxdjsdx" column="FXDJSDX" jdbcType="DECIMAL"/>
            <result property="tzpzsdx" column="TZPZSDX" jdbcType="DECIMAL"/>
            <result property="tzqxsdx" column="TZQXSDX" jdbcType="DECIMAL"/>
            <result property="yqsysdx" column="YQSYSDX" jdbcType="DECIMAL"/>
            <result property="sdxjg" column="SDXJG" jdbcType="DECIMAL"/>
            <result property="yyb" column="YYB" jdbcType="DECIMAL"/>
            <result property="kcrq" column="KCRQ" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        RQ,KHH,CPDM,
        SJLY,KHFXDJ,KHTZPZ,
        KHTZQX,KHYQSY,CPFXDJ,
        CPTZPZ,CPTZQX,CPYQSY,
        FXDJSDX,TZPZSDX,TZQXSDX,
        YQSYSDX,SDXJG,YYB,
        KCRQ
    </sql>

    <select id="compute" resultType="com.apex.sdx.api.vo.compute.QtcccptjVo">
        select
            t.xtbh               as ywxt,
            rq                   as rq,
            nvl(ccsl, 0)         as ccsl,
            nvl(sdsl, 0)         as sdsl,
            nvl(case when ccsl > 0 then round(sdsl * 1.0 / ccsl, 4) else 0 end, 0)  as sdzb,
            nvl(bsdsl, 0)        as bsdsl,
            nvl(case when ccsl > 0 then round(bsdsl * 1.0 / ccsl, 4) else 0 end, 0) as bsdzb,
            nvl(khsl, 0)         as khsl
        from
            (
                select
                    sjly,
                    max(tq.rq)                                 as rq,
                    count(1)                                   as ccsl,
                    sum(case when sdxjg = 1 then 1 else 0 end) as sdsl,
                    sum(case when sdxjg = 0 then 1 else 0 end) as bsdsl,
                    count(distinct(khh))                       as khsl
                from
                    sdx.tsdxjg_qtcccp tq
                <where>
                    <if test="khh != null and khh != ''">
                        and tq.khh = #{khh}
                    </if>
                    <if test="rq!= null and rq!= ''">
                        and rq = #{rq}
                    </if>
                </where>
                group by
                    sjly
                union all
                select
                    sjly,
                    max(tq.rq)                                 as rq,
                    count(1)                                   as ccsl,
                    sum(case when sdxjg = 1 then 1 else 0 end) as sdsl,
                    sum(case when sdxjg = 0 then 1 else 0 end) as bsdsl,
                    count(distinct(khh))                       as khsl
                from
                    sdx.TSDXJG_QTDYTG tq
                <where>
                    <if test="khh != null and khh != ''">
                        and tq.khh = #{khh}
                    </if>
                    <if test="rq!= null and rq!= ''">
                        and rq = #{rq}
                    </if>
                </where>
                group by
                    sjly) as qtcccp
                right join sdx.tsystem t on
                qtcccp.sjly = t.xtbh
        <where>
            t.xtbh in (1000, 1003, 1004, 1005)
        <if test="ywxt!= null and ywxt!= ''">
            and t.xtbh = #{ywxt}
        </if>
        </where>
    </select>

</mapper>
