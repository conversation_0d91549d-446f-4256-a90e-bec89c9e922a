package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName tkhfxjsls
 */
@TableName(value ="tkhfxjsls")
@Data
public class Tkhfxjsls implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 
     */
    private String khh;

    /**
     * 
     */
    private Long zjlb;

    /**
     * 
     */
    private String zjbh;

    /**
     * 
     */
    private String khmc;

    /**
     * 
     */
    private Long jslb;

    /**
     * 
     */
    private String jsnr;

    /**
     * 
     */
    private String hashkey;

    /**
     * 
     */
    private String ip;

    /**
     * 
     */
    private String mac;

    /**
     * 
     */
    private String sdata;

    /**
     * 
     */
    private String fqqd;

    /**
     * 
     */
    private Integer czrq;

    /**
     * 
     */
    private String czsj;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Tkhfxjsls other = (Tkhfxjsls) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
            && (this.getZjlb() == null ? other.getZjlb() == null : this.getZjlb().equals(other.getZjlb()))
            && (this.getZjbh() == null ? other.getZjbh() == null : this.getZjbh().equals(other.getZjbh()))
            && (this.getKhmc() == null ? other.getKhmc() == null : this.getKhmc().equals(other.getKhmc()))
            && (this.getJslb() == null ? other.getJslb() == null : this.getJslb().equals(other.getJslb()))
            && (this.getJsnr() == null ? other.getJsnr() == null : this.getJsnr().equals(other.getJsnr()))
            && (this.getHashkey() == null ? other.getHashkey() == null : this.getHashkey().equals(other.getHashkey()))
            && (this.getIp() == null ? other.getIp() == null : this.getIp().equals(other.getIp()))
            && (this.getMac() == null ? other.getMac() == null : this.getMac().equals(other.getMac()))
            && (this.getSdata() == null ? other.getSdata() == null : this.getSdata().equals(other.getSdata()))
            && (this.getFqqd() == null ? other.getFqqd() == null : this.getFqqd().equals(other.getFqqd()))
            && (this.getCzrq() == null ? other.getCzrq() == null : this.getCzrq().equals(other.getCzrq()))
            && (this.getCzsj() == null ? other.getCzsj() == null : this.getCzsj().equals(other.getCzsj()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getZjlb() == null) ? 0 : getZjlb().hashCode());
        result = prime * result + ((getZjbh() == null) ? 0 : getZjbh().hashCode());
        result = prime * result + ((getKhmc() == null) ? 0 : getKhmc().hashCode());
        result = prime * result + ((getJslb() == null) ? 0 : getJslb().hashCode());
        result = prime * result + ((getJsnr() == null) ? 0 : getJsnr().hashCode());
        result = prime * result + ((getHashkey() == null) ? 0 : getHashkey().hashCode());
        result = prime * result + ((getIp() == null) ? 0 : getIp().hashCode());
        result = prime * result + ((getMac() == null) ? 0 : getMac().hashCode());
        result = prime * result + ((getSdata() == null) ? 0 : getSdata().hashCode());
        result = prime * result + ((getFqqd() == null) ? 0 : getFqqd().hashCode());
        result = prime * result + ((getCzrq() == null) ? 0 : getCzrq().hashCode());
        result = prime * result + ((getCzsj() == null) ? 0 : getCzsj().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", khh=").append(khh);
        sb.append(", zjlb=").append(zjlb);
        sb.append(", zjbh=").append(zjbh);
        sb.append(", khmc=").append(khmc);
        sb.append(", jslb=").append(jslb);
        sb.append(", jsnr=").append(jsnr);
        sb.append(", hashkey=").append(hashkey);
        sb.append(", ip=").append(ip);
        sb.append(", mac=").append(mac);
        sb.append(", sdata=").append(sdata);
        sb.append(", fqqd=").append(fqqd);
        sb.append(", czrq=").append(czrq);
        sb.append(", czsj=").append(czsj);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}