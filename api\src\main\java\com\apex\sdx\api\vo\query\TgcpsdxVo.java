package com.apex.sdx.api.vo.query;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/6/11 18:59
 * @Description: TODO
 */
@Setter
@Getter
public class TgcpsdxVo implements Serializable {

    @LiveProperty(note = "日期", index = 1)
    private Integer rq;

    @LiveProperty(note = "客户号", index = 2)
    private String khh;

    /**
     * 产品代码
     */
    @LiveProperty(note = "产品代码", index = 3)
    private String cpdm;

    /**
     * 营业部
     */
    @LiveProperty(note = "营业部", index = 4)
    private Long yyb;

    /**
     * 订单渠道;1|客户线上;2|运营线下
     */
    @LiveProperty(note = "订单渠道", index = 5)
    private Long ddqd;

    /**
     * 订单日期
     */
    @LiveProperty(note = "订单日期", index = 6)
    private Integer ddrq;

    /**
     * 订单状态;1|解约中;2|签约成功;3|解约成功
     */
    @LiveProperty(note = "订单状态", index = 7)
    private Long ddzt;

    /**
     * 产品名称
     */
    @LiveProperty(note = "产品名称", index = 8)
    private String cpmc;

    /**
     * 客户风险等级;SDX_FXCSNL
     */
    @LiveProperty(note = "客户风险等级", index = 9)
    private Long khfxdj;

    /**
     * 产品风险等级;SDX_CPFXDJ
     */
    @LiveProperty(note = "产品风险等级", index = 10)
    private Long cpfxdj;

    /**
     * 风险等级适当性;SDX_PPJG，-1|不通过;0|不适当;1|匹配
     */
    @LiveProperty(note = "风险等级适当性", index = 11)
    private Long fxdjsdx;

    /**
     * 适当性结果;SDX_PPJG，-1|不通过;0|不适当;1|匹配
     */
    @LiveProperty(note = "适当性结果", index = 12)
    private Long sdxjg;

    /**
     * 数据来源;TSYSTEM.YWXT，1000;1003
     */
    @LiveProperty(note = "数据来源", index = 13)
    private Integer sjly;
}
