package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 适当性事件
 * @TableName tsdx_sj
 */
@TableName(value ="tsdx_sj", schema = "sdx")
@Data
public class TsdxSj implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private Integer scrq;

    /**
     * 
     */
    private String scsj;

    /**
     * 
     */
    private String khh;

    /**
     * 
     */
    private Long yyb;

    /**
     * 
     */
    private Long sjlx;

    /**
     * 
     */
    private String sjbm;

    /**
     * 
     */
    private String sjxq;

    /**
     * 
     */
    private Integer tzfs;

    /**
     * 
     */
    private Integer tzrq;

    /**
     * 
     */
    private String tzsj;

    /**
     * 
     */
    private Integer tzzt;

    /**
     * 
     */
    private Integer clzt;

    /**
     * 
     */
    private String tznr;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TsdxSj other = (TsdxSj) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getScrq() == null ? other.getScrq() == null : this.getScrq().equals(other.getScrq()))
            && (this.getScsj() == null ? other.getScsj() == null : this.getScsj().equals(other.getScsj()))
            && (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
            && (this.getYyb() == null ? other.getYyb() == null : this.getYyb().equals(other.getYyb()))
            && (this.getSjlx() == null ? other.getSjlx() == null : this.getSjlx().equals(other.getSjlx()))
            && (this.getSjbm() == null ? other.getSjbm() == null : this.getSjbm().equals(other.getSjbm()))
            && (this.getSjxq() == null ? other.getSjxq() == null : this.getSjxq().equals(other.getSjxq()))
            && (this.getTzfs() == null ? other.getTzfs() == null : this.getTzfs().equals(other.getTzfs()))
            && (this.getTzrq() == null ? other.getTzrq() == null : this.getTzrq().equals(other.getTzrq()))
            && (this.getTzsj() == null ? other.getTzsj() == null : this.getTzsj().equals(other.getTzsj()))
            && (this.getTzzt() == null ? other.getTzzt() == null : this.getTzzt().equals(other.getTzzt()))
            && (this.getClzt() == null ? other.getClzt() == null : this.getClzt().equals(other.getClzt()))
            && (this.getTznr() == null ? other.getTznr() == null : this.getTznr().equals(other.getTznr()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getScrq() == null) ? 0 : getScrq().hashCode());
        result = prime * result + ((getScsj() == null) ? 0 : getScsj().hashCode());
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getYyb() == null) ? 0 : getYyb().hashCode());
        result = prime * result + ((getSjlx() == null) ? 0 : getSjlx().hashCode());
        result = prime * result + ((getSjbm() == null) ? 0 : getSjbm().hashCode());
        result = prime * result + ((getSjxq() == null) ? 0 : getSjxq().hashCode());
        result = prime * result + ((getTzfs() == null) ? 0 : getTzfs().hashCode());
        result = prime * result + ((getTzrq() == null) ? 0 : getTzrq().hashCode());
        result = prime * result + ((getTzsj() == null) ? 0 : getTzsj().hashCode());
        result = prime * result + ((getTzzt() == null) ? 0 : getTzzt().hashCode());
        result = prime * result + ((getClzt() == null) ? 0 : getClzt().hashCode());
        result = prime * result + ((getTznr() == null) ? 0 : getTznr().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", scrq=").append(scrq);
        sb.append(", scsj=").append(scsj);
        sb.append(", khh=").append(khh);
        sb.append(", yyb=").append(yyb);
        sb.append(", sjlx=").append(sjlx);
        sb.append(", sjbm=").append(sjbm);
        sb.append(", sjxq=").append(sjxq);
        sb.append(", tzfs=").append(tzfs);
        sb.append(", tzrq=").append(tzrq);
        sb.append(", tzsj=").append(tzsj);
        sb.append(", tzzt=").append(tzzt);
        sb.append(", clzt=").append(clzt);
        sb.append(", tznr=").append(tznr);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}