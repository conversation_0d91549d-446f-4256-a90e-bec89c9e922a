package com.apex.sdx.api.req.query;

import com.apex.sdx.api.req.common.PageRequest;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-03-05
 * @Description:
 */
@Setter
@Getter
public class KhfxjslscxReq extends PageRequest {
    @LiveProperty(note = "客户号", index = 1)
    private String khh;

    @LiveProperty(note = "开始日期", index = 2)
    private Integer ksrq;

    @LiveProperty(note = "结束日期", index = 3)
    private Integer jsrq;

    @LiveProperty(note = "警示内容", index = 4)
    private String jsnr;
}
