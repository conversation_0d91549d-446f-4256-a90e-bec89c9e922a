package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.mybatis.entity.TicKhsjzb;
import com.apex.sdx.core.mybatis.mapper.TicKhsjzbMapper;
import com.apex.sdx.core.mybatis.service.TkhsjzbService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class TicKhsjzbServiceImpl extends ServiceImpl<TicKhsjzbMapper, TicKhsjzb>
    implements TkhsjzbService {

    @Override
    public List<TicKhsjzb> queryKhsjzbByKhhCode(String khh, String rq, String id_codes) {//查询数组数据
        LambdaQueryWrapper<TicKhsjzb> queryWrapper = new LambdaQueryWrapper<>();
        try {
            queryWrapper.eq(TicKhsjzb::getKhh, khh);
            if (StringUtils.isNotBlank(rq)) {
                queryWrapper.eq(TicKhsjzb::getRq, rq);
            }
            if (id_codes != null) {
                Object[] idsArr = id_codes.split(";");
                queryWrapper.in(TicKhsjzb::getIdxCode, idsArr);
            }
            return this.list(queryWrapper);
        } catch (Exception e) {
            String note = String.format("查询客户数据指标异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-610, note);
        }
    }
}




