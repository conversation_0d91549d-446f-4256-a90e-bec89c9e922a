package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.api.vo.compute.SdxsjtjVo;
import com.apex.sdx.api.vo.query.SdxsjDescVo;
import com.apex.sdx.api.vo.query.SdxsjVo;
import com.apex.sdx.core.mybatis.entity.TsdxSj;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Entity com.apex.sdx.core.mybatis.entity.TsdxSj
 */
public interface TsdxSjMapper extends BaseMapper<TsdxSj> {

    Page<SdxsjVo> query4sdxsj(Page<SdxsjVo> page, @Param("khh") String khh, @Param("ksrq") String ksrq, @Param("jsrq") String jsrq, @Param("clzt") String clzt);

    /**
     * 适当性事件详情
     * @param sjid sjid
     * @return 详情列表
     */
    List<SdxsjDescVo> querySdxsjDesc(@Param("sjid")Long sjid);

    Page<SdxsjtjVo> compute(Page<SdxsjtjVo> page, @Param("ksrq") String ksrq, @Param("jsrq") String jsrq, @Param("khh") String khh, @Param("sjbm") String sjbm);

    Page<SdxsjtjVo> compute4Sdxsj(Page<SdxsjtjVo> page, @Param("ksrq") String ksrq, @Param("jsrq") String jsrq, @Param("khh") String khh, @Param("sjbm") String sjbm);

}




