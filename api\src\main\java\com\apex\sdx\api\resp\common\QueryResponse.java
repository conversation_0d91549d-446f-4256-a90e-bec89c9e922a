package com.apex.sdx.api.resp.common;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 通用查询服务响应对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class QueryResponse<T> extends R {

    public QueryResponse() {
        this.setCode(-1);
        this.setNote("");
    }

    public QueryResponse(int code) {
        this.setCode(code);
    }

    public QueryResponse(int code, String note) {
        this.setCode(code);
        this.setNote(note);
    }

    public void setRecords(List<T> records) {
        this.records = records;
        this.count = records.size();
    }

    /**
     * 查询数据集
     */
    @LiveProperty(note = "查询数据集", index = 1001)
    private List<T> records;
    /**
     * 查询数据集记录数
     */
    @LiveProperty(note = "查询数据集记录数", index = 1002)
    private int count;

    public static <T> QueryResponse<T> buildRecords(List<T> records) {
        QueryResponse<T> response = new QueryResponse<>();
        response.setCode(1);
        response.setNote("查询成功");
        response.setRecords(records);
        response.setCount(records!=null? records.size(): 0);
        return response;
    }

    public static <T> QueryResponse<T> buildError(int code, String note) {
        QueryResponse<T> response = new QueryResponse<>();
        response.setCode(code);
        response.setNote(note);
        return response;
    }

}
