package com.apex.sdx.gateway.common.redis;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Repository;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @Author: xfh
 * @Description: 非对象缓存用这个类，推荐所以缓存都用这个。
 * @Date: Created model 上午10:00 2019/2/22
 * @MODIFIED BY:
 */
@Slf4j
@Repository("redisStringDao")
public class RedisStringDao {

    @Autowired
    @Qualifier("redisTemplate")
    private RedisTemplate<Object, Object> template;

    @Autowired
    private StringRedisTemplate stringTemplate;


    public void setKey(String key, String value) {
        //先清除缓存后再存一次
        removeKey(key);
        ValueOperations<String, String> ops = stringTemplate.opsForValue();
        ops.set(key, value, 30, TimeUnit.MINUTES);
    }

    /**
     * @param key
     * @param value
     * @param timeout 分钟
     */
    public void setKey(String key, String value, int timeout) {
        ValueOperations<String, String> ops = stringTemplate.opsForValue();
        ops.set(key, value, timeout, TimeUnit.MINUTES);
    }

    public String getValue(String key) {
        log.info("从redis中获取:" + key);
        ValueOperations<String, String> ops = stringTemplate.opsForValue();
        String redisObj = ops.get(key);
        if (redisObj != null)
            log.info("key=【" + key + "】从redis中获取到值:" + redisObj);
        else
            log.info("key=【" + key + "】从redis中获取到null");
        return redisObj;
    }

    public void removeKey(String key) {
        stringTemplate.delete(key);
    }

    public void removeKeys(String key) {
        Set<String> keys = stringTemplate.keys(key);
        log.info("清除redis缓存：" + keys.size() + "条");
        stringTemplate.delete(stringTemplate.keys(key));
    }


    /**
     * 加锁
     * @param key
     * @param value
     * @return
     */
    public boolean lock(String key, String value) {
        if (stringTemplate.opsForValue().setIfAbsent(key, value)) {
            //如果设置成功，说明没有锁，返回true
            return true;
        }
        //// 判断锁超时 - 防止原来的操作异常，没有运行解锁操作  防止死锁
        String currentValue = stringTemplate.opsForValue().get(key);
        // 如果锁过期 currentLock不为空且小于当前时间
        if (!StringUtils.isEmpty(currentValue)
                && Long.parseLong(currentValue) < System.currentTimeMillis()) {
            // 获取上一个锁的时间value 对应getset，如果lock存在
            String oldValue = stringTemplate.opsForValue().getAndSet(key, value);
            //是否已被别人抢占
            return StringUtils.isNotEmpty(oldValue) && oldValue.equals(currentValue);
        }
        return false;
    }

    /**
     * 解锁
     *
     * @param key   键
     * @param value 当前时间 + 超时时间
     */
    public void unlock(String key, String value) {
        try {
            String currentValue = stringTemplate.opsForValue().get(key);
            if (!StringUtils.isEmpty(currentValue) && currentValue.equals(value)) {
                stringTemplate.opsForValue().getOperations().delete(key);
            }
        } catch (Exception e) {
            log.error("redis解锁异常");
        }
    }


}
