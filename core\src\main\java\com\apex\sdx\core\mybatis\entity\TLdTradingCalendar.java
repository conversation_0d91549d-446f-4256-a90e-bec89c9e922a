package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 交易日历表
 * @TableName t_ld_trading_calendar
 */
@TableName(value ="t_ld_trading_calendar", schema = "livedata")
@Data
public class TLdTradingCalendar {
    /**
     * 自然日
     */
    @TableId
    private Integer zrr;

    /**
     * 交易日
     */
    private Integer jyr;

    /**
     * 年度
     */
    private Integer nd;

    /**
     * 季度
     */
    private Integer jd;

    /**
     * 月份序号
     */
    private Integer yfXh;

    /**
     * 星期_星期几,to_char(rq,'w')-1
     */
    private Integer xq;

    /**
     * 年月-年度+月份
     */
    private Integer ny;

    /**
     * 年周-年度+周号
     */
    private Integer nz;

    /**
     * 星期序号
     */
    private Integer xqXh;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TLdTradingCalendar other = (TLdTradingCalendar) that;
        return (this.getZrr() == null ? other.getZrr() == null : this.getZrr().equals(other.getZrr()))
            && (this.getJyr() == null ? other.getJyr() == null : this.getJyr().equals(other.getJyr()))
            && (this.getNd() == null ? other.getNd() == null : this.getNd().equals(other.getNd()))
            && (this.getJd() == null ? other.getJd() == null : this.getJd().equals(other.getJd()))
            && (this.getYfXh() == null ? other.getYfXh() == null : this.getYfXh().equals(other.getYfXh()))
            && (this.getXq() == null ? other.getXq() == null : this.getXq().equals(other.getXq()))
            && (this.getNy() == null ? other.getNy() == null : this.getNy().equals(other.getNy()))
            && (this.getNz() == null ? other.getNz() == null : this.getNz().equals(other.getNz()))
            && (this.getXqXh() == null ? other.getXqXh() == null : this.getXqXh().equals(other.getXqXh()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getZrr() == null) ? 0 : getZrr().hashCode());
        result = prime * result + ((getJyr() == null) ? 0 : getJyr().hashCode());
        result = prime * result + ((getNd() == null) ? 0 : getNd().hashCode());
        result = prime * result + ((getJd() == null) ? 0 : getJd().hashCode());
        result = prime * result + ((getYfXh() == null) ? 0 : getYfXh().hashCode());
        result = prime * result + ((getXq() == null) ? 0 : getXq().hashCode());
        result = prime * result + ((getNy() == null) ? 0 : getNy().hashCode());
        result = prime * result + ((getNz() == null) ? 0 : getNz().hashCode());
        result = prime * result + ((getXqXh() == null) ? 0 : getXqXh().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", zrr=").append(zrr);
        sb.append(", jyr=").append(jyr);
        sb.append(", nd=").append(nd);
        sb.append(", jd=").append(jd);
        sb.append(", yfXh=").append(yfXh);
        sb.append(", xq=").append(xq);
        sb.append(", ny=").append(ny);
        sb.append(", nz=").append(nz);
        sb.append(", xqXh=").append(xqXh);
        sb.append("]");
        return sb.toString();
    }
}