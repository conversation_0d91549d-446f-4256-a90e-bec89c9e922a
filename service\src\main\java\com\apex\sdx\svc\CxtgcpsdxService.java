package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.TgcpsdxcxReq;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.query.TgcpsdxVo;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.service.TsdxjgQtdytgService;
import com.apexsoft.LiveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import sdx.query.ICxtgcpsdxService;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/11 19:03
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "投顾产品适当性查询服务")
@RequiredArgsConstructor
public class CxtgcpsdxService implements ICxtgcpsdxService {

    private final TsdxjgQtdytgService tsdxjgQtdytgService;

    @Override
    public QueryResponse<TgcpsdxVo> tgcpsdxcx(TgcpsdxcxReq req) throws Exception {
        Assert.notNull(req, TgcpsdxcxReq::getKhh, TgcpsdxcxReq::getSjlx);
        QueryResponse<TgcpsdxVo> result = new QueryResponse<>(1, "查询成功");
        String khh = req.getKhh();
        String rq = req.getRq();
        Integer sjlx = req.getSjlx();
        boolean onlysdx = req.isOnlysdx();

        List<TgcpsdxVo> list = tsdxjgQtdytgService.selectByKhhSjlxAndSdxjg(khh, sjlx, onlysdx,rq);

        result.setRecords(list);
        return result;
    }
}
