package com.apex.sdx.api.resp.query;

import com.apex.sdx.api.resp.common.R;
import com.apex.sdx.api.vo.xtcs.XtdmVo;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-01-16
 * @Description:
 */
@Setter
@Getter
public class SjzdRes extends R {
    public SjzdRes() {
        super(-1);
    }
    public SjzdRes(int code) {
        super(code);
    }
    public SjzdRes(int code, String note) {
        super(code, note);
    }

    /**
     * 数据字典
     */
    @LiveProperty(note = "数据字典", index = 1)
    private HashMap<String, List<XtdmVo>> sjzd;

}