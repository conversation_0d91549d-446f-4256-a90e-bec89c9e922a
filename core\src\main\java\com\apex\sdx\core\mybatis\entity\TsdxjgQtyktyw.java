package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 全体已开通业务适当性
 * @TableName tsdxjg_qtyktyw
 */
@TableName(value ="tsdxjg_qtyktyw", schema = "sdx")
@Data
public class TsdxjgQtyktyw implements Serializable {
    /**
     * 客户号
     */
    private String khh;

    /**
     * 日期
     */
    private Integer rq;

    /**
     * 创新业务类别
     */
    private Long cxywlb;

    /**
     * 客户风险等级
     */
    private Long khfxdj;

    /**
     * 客户投资品种
     */
    private String khtzpz;

    /**
     * 客户投资期限
     */
    private Long khtzqx;

    /**
     * 客户预期收益
     */
    private String khyqsy;

    /**
     * 业务风险等级
     */
    private Long ywfxdj;

    /**
     * 业务投资品种
     */
    private Long ywtzpz;

    /**
     * 业务投资期限
     */
    private Long ywtzqx;

    /**
     * 业务预期收益
     */
    private Long ywyqsy;

    /**
     * 风险等级适当性
     */
    private Long fxdjsdx;

    /**
     * 投资品种适当性
     */
    private Long tzpzsdx;

    /**
     * 投资期限适当性
     */
    private Long tzqxsdx;

    /**
     * 预期收益适当性
     */
    private Long yqsysdx;

    /**
     * 适当性结果
     */
    private Long sdxjg;

    /**
     * 营业部
     */
    private Long yyb;

    /**
     * 不适当及风险确认协议
     */
    private Integer qsbsdxy;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TsdxjgQtyktyw other = (TsdxjgQtyktyw) that;
        return (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
            && (this.getRq() == null ? other.getRq() == null : this.getRq().equals(other.getRq()))
            && (this.getCxywlb() == null ? other.getCxywlb() == null : this.getCxywlb().equals(other.getCxywlb()))
            && (this.getKhfxdj() == null ? other.getKhfxdj() == null : this.getKhfxdj().equals(other.getKhfxdj()))
            && (this.getKhtzpz() == null ? other.getKhtzpz() == null : this.getKhtzpz().equals(other.getKhtzpz()))
            && (this.getKhtzqx() == null ? other.getKhtzqx() == null : this.getKhtzqx().equals(other.getKhtzqx()))
            && (this.getKhyqsy() == null ? other.getKhyqsy() == null : this.getKhyqsy().equals(other.getKhyqsy()))
            && (this.getYwfxdj() == null ? other.getYwfxdj() == null : this.getYwfxdj().equals(other.getYwfxdj()))
            && (this.getYwtzpz() == null ? other.getYwtzpz() == null : this.getYwtzpz().equals(other.getYwtzpz()))
            && (this.getYwtzqx() == null ? other.getYwtzqx() == null : this.getYwtzqx().equals(other.getYwtzqx()))
            && (this.getYwyqsy() == null ? other.getYwyqsy() == null : this.getYwyqsy().equals(other.getYwyqsy()))
            && (this.getFxdjsdx() == null ? other.getFxdjsdx() == null : this.getFxdjsdx().equals(other.getFxdjsdx()))
            && (this.getTzpzsdx() == null ? other.getTzpzsdx() == null : this.getTzpzsdx().equals(other.getTzpzsdx()))
            && (this.getTzqxsdx() == null ? other.getTzqxsdx() == null : this.getTzqxsdx().equals(other.getTzqxsdx()))
            && (this.getYqsysdx() == null ? other.getYqsysdx() == null : this.getYqsysdx().equals(other.getYqsysdx()))
            && (this.getSdxjg() == null ? other.getSdxjg() == null : this.getSdxjg().equals(other.getSdxjg()))
            && (this.getYyb() == null ? other.getYyb() == null : this.getYyb().equals(other.getYyb()))
            && (this.getQsbsdxy() == null ? other.getQsbsdxy() == null : this.getQsbsdxy().equals(other.getQsbsdxy()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getRq() == null) ? 0 : getRq().hashCode());
        result = prime * result + ((getCxywlb() == null) ? 0 : getCxywlb().hashCode());
        result = prime * result + ((getKhfxdj() == null) ? 0 : getKhfxdj().hashCode());
        result = prime * result + ((getKhtzpz() == null) ? 0 : getKhtzpz().hashCode());
        result = prime * result + ((getKhtzqx() == null) ? 0 : getKhtzqx().hashCode());
        result = prime * result + ((getKhyqsy() == null) ? 0 : getKhyqsy().hashCode());
        result = prime * result + ((getYwfxdj() == null) ? 0 : getYwfxdj().hashCode());
        result = prime * result + ((getYwtzpz() == null) ? 0 : getYwtzpz().hashCode());
        result = prime * result + ((getYwtzqx() == null) ? 0 : getYwtzqx().hashCode());
        result = prime * result + ((getYwyqsy() == null) ? 0 : getYwyqsy().hashCode());
        result = prime * result + ((getFxdjsdx() == null) ? 0 : getFxdjsdx().hashCode());
        result = prime * result + ((getTzpzsdx() == null) ? 0 : getTzpzsdx().hashCode());
        result = prime * result + ((getTzqxsdx() == null) ? 0 : getTzqxsdx().hashCode());
        result = prime * result + ((getYqsysdx() == null) ? 0 : getYqsysdx().hashCode());
        result = prime * result + ((getSdxjg() == null) ? 0 : getSdxjg().hashCode());
        result = prime * result + ((getYyb() == null) ? 0 : getYyb().hashCode());
        result = prime * result + ((getQsbsdxy() == null) ? 0 : getQsbsdxy().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", khh=").append(khh);
        sb.append(", rq=").append(rq);
        sb.append(", cxywlb=").append(cxywlb);
        sb.append(", khfxdj=").append(khfxdj);
        sb.append(", khtzpz=").append(khtzpz);
        sb.append(", khtzqx=").append(khtzqx);
        sb.append(", khyqsy=").append(khyqsy);
        sb.append(", ywfxdj=").append(ywfxdj);
        sb.append(", ywtzpz=").append(ywtzpz);
        sb.append(", ywtzqx=").append(ywtzqx);
        sb.append(", ywyqsy=").append(ywyqsy);
        sb.append(", fxdjsdx=").append(fxdjsdx);
        sb.append(", tzpzsdx=").append(tzpzsdx);
        sb.append(", tzqxsdx=").append(tzqxsdx);
        sb.append(", yqsysdx=").append(yqsysdx);
        sb.append(", sdxjg=").append(sdxjg);
        sb.append(", yyb=").append(yyb);
        sb.append(", qsbsdxy=").append(qsbsdxy);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}