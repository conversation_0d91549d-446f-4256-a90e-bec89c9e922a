package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.api.resp.query.KhsdxfbRes;
import com.apex.sdx.api.vo.khgl.KhxxtjVo;
import com.apex.sdx.api.vo.khgl.TzzcfbVo;
import com.apex.sdx.api.vo.khgl.ZhsdxqkVo;
import com.apex.sdx.core.mybatis.entity.Tkhxx;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface TkhxxService extends IService<Tkhxx> {

    /**
     * 根据khh,zjbh,zjlb查询客户信息
     * @param khh
     * @param zjbh
     * @param zjlb
     * @return
     */
    Tkhxx getKhxxByKhhZjbhZjlb(String khh, String zjbh, Integer zjlb);


    List<KhxxtjVo> khxxtj();

    List<KhsdxfbRes> queryTzzflxx(String type);

    List<KhsdxfbRes> queryFxcsnl(String type);
    
    /**
     * 查询ABC类投资者数据
     * @return ABC类投资者数据列表
     */
    List<KhsdxfbRes> queryAbcTzzflxx();

    /**
     * 查询投资者构成及分布
     * @return 投资者构成及分布数据
     */
    TzzcfbVo queryTzzcfb();

    Page<ZhsdxqkVo> queryZhztmx(
            String khh, String zhqk, String zhzt, boolean isSearchCount, int pagesize, int pagenum);

    Page<ZhsdxqkVo> queryZjyxqmx(
            String khh, String zhqk, String zjjzrq, boolean isSearchCount, int pagesize, int pagenum);

    Page<ZhsdxqkVo> queryFxqfxdjmx(
            String khh, String xqfxdj, boolean isSearchCount, int pagesize, int pagenum);

    Page<ZhsdxqkVo> queryFxcpyxmx(
            String khh, String zhqk, String cpyxq, boolean isSearchCount, int pagesize, int pagenum);

    Page<ZhsdxqkVo> queryZytzzcpmx(
            String khh, String tzzpdyxq, boolean isSearchCount, int pagesize, int pagenum);

    Page<ZhsdxqkVo> queryQtxxmx(String khh, String qtxxzt, boolean isSearchCount, int pagesize, int pagenum);

}
