package sdx.query;

import com.apex.sdx.api.req.query.CpbsdxyqslscxReq;
import com.apex.sdx.api.req.query.QxbsdxyqslscxReq;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.query.CpbsdxyqslscxVo;
import com.apex.sdx.api.vo.query.QxbsdxyqslscxVo;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR>
 * @Date 2025/6/6 16:45
 * @Description: 查询不适当协议签署流水服务接口
 */
public interface ICxbsdxyqslsService {

    @LiveMethod(paramAsRequestBody = true, note = "权限不适当协议签署流水查询")
    QueryResponse<QxbsdxyqslscxVo> qxbsdxyqslscx(QxbsdxyqslscxReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "产品不适当协议签署流水查询")
    QueryResponse<CpbsdxyqslscxVo> cpbsdxyqslscx(CpbsdxyqslscxReq req) throws Exception;
}
