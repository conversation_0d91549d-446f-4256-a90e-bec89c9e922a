package sdx.query;

import com.apex.sdx.api.req.query.KhfxjslscxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.vo.query.KhfxjslsVo;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR>
 * @Date 2025-03-05
 * @Description:
 */
public interface IKhfxjslsService {
    @LiveMethod(paramAsRequestBody = true, note = "客户风险警示流水查询")
    QueryPageResponse<KhfxjslsVo> khfxjslscx(KhfxjslscxReq req) throws Exception;
}
