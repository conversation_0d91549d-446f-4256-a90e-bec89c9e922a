package com.apex.sdx.gateway.base.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.apex.sdx.gateway.aas.modules.index.service.DefaultServiceHandler;
import com.apex.sdx.gateway.base.dao.LivebosGrpcDao;
import com.apex.sdx.gateway.base.model.CommonResponse;
import com.apex.sdx.gateway.base.model.JSONResponse;
import com.apex.sdx.gateway.base.service.LbProjectService;
import com.apex.sdx.gateway.base.service.UserService;
import com.apex.sdx.gateway.aas.common.session.UserSession;
import com.apex.sdx.gateway.aas.modules.index.model.AuthUser;
import com.apex.sdx.gateway.common.utils.Constant;
import com.apexsoft.live.session.UserAuthenticateSession;
import com.apexsoft.live.utils.AES;
import com.apexsoft.livebos.ILiveBOSUserConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.UUID;


@Controller
@RequestMapping("/user")
@Slf4j
public class UserController {
    @Value("${application.name}")
    private String ps;

    @Autowired
    LivebosGrpcDao livebosGrpcDao;
    @Autowired
    LbProjectService lbProjectService;

    @Autowired
    private ILiveBOSUserConverter liveBOSUserConverter;
    @Autowired
    UserService userService;
    private DefaultServiceHandler defaultServiceHandler = new DefaultServiceHandler();


    @RequestMapping(value = "/queryUserProjectMenus",name = "查询用户项目菜单")
    @ResponseBody
    public JSONResponse queryUserProject(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
        int id = (int)user.getId();
        CommonResponse response = lbProjectService.getProjectMenus(id, jsonData.getString("projectName"));
        return response;
    }

    @RequestMapping(value = "/queryUserInfo", name = "查询柜员信息")
    @ResponseBody
    public JSONResponse queryUserInfo(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        try{
            AuthUser<JSONObject> authUser = UserAuthenticateSession.getUser(httpServletRequest);
            // 如果会话用户和传参用户相同，只取会话信息
            JSONObject userInfo = authUser.getUser();
            JSONArray authList = authUser.getAuthList();
            if(authList == null) {
                // 用户权限
                authList = livebosGrpcDao.queryUserAuthList(authUser.getId(), null);
                authUser.setAuthList(authList);
                UserAuthenticateSession.setUser(httpServletRequest, authUser);
            }
            userInfo.put("authList", authUser.getAuthList());
            response.setData(userInfo);
            return response;
        } catch (Exception e){
            response = new CommonResponse(JSONResponse.CODE_FAIL, "查询失败:"+e.getMessage());
        }
        return response;
    }

    @RequestMapping(value = "/changeUserPassword", name = "修改柜员密码")
    @ResponseBody
    public JSONResponse changeUserPassword(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        JSONResponse response = null;
        try{
            JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);

            String oldpassword = AES.decrypt(jsonData.getString("oldpassword"), ps);
            String newpassword = AES.decrypt(jsonData.getString("newpassword"), ps);
            response = userService.changeUserPassword(jsonData.getString("userid"), oldpassword, newpassword);
        }catch (Exception e){
            response = new CommonResponse(JSONResponse.CODE_FAIL, "密码修改失败:"+e.getMessage());
        }
        return response;
    }


    @RequestMapping(value = "/updatePhoto",name = "更新柜员头像")
    @ResponseBody
    public JSONResponse updatePhoto(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
        String uid = user.getId()+"";

        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) httpServletRequest;
        // 获得文件：
        MultipartFile multipartFile = multipartRequest.getFile("file");
        File file = new File(new File("").getAbsoluteFile()+File.separator+UUID.randomUUID());
        String fileName = UUID.randomUUID()+"";

        multipartFile.transferTo(file);

        JSONResponse response = userService.updatePhoto(uid, file, fileName);
        if(response.getCode()>0){
            JSONObject userinfo = userService.getUserInfo(user.getUserId());
            UserSession.updateUserPhoto(uid, userinfo.getString("photo"));
        }
        return response;
    }

    @RequestMapping(value = "/getDbWorkCount", name = "获取代办任务数量")
    @ResponseBody
    public JSONResponse getDbWorkCount(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        try{
            JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
            response.setData(userService.getDbWorkCount());;
        }catch (Exception e){
            response = new CommonResponse(JSONResponse.CODE_FAIL, "查询失败:"+e.getMessage());
        }
        return response;
    }

    @RequestMapping(value = "/getUnreadNotificationCount", name = "获取未读消息通知数量")
    @ResponseBody
    public JSONResponse getUnreadNotificationCount(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        try{
            JSONObject data = userService.getUnreadNotificationCount();
            response.setData(data);
        }catch (Exception e){
            response = new CommonResponse(JSONResponse.CODE_FAIL, "查询失败:"+e.getMessage());
        }
        return response;
    }

    @RequestMapping(value = "/loadNotification",name = "获取消息通知信息")
    @ResponseBody
    public JSONResponse loadNotification(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        try{
            JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
            AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
            String uid = user.getId()+"";
            JSONObject data = userService.loadNotification(uid, jsonData.getString("setnotificationtype")
                    ,jsonData.getIntValue("pagenum"),jsonData.getIntValue("pagesize") );
            if(data.getIntValue("code")>0) {
                response.setRecords(data.getJSONArray("records"));
                response.setCount(data.getIntValue("count"));
            }
        }catch (Exception e){
            response = new CommonResponse(-1, "查询失败:"+e.getMessage());
        }
        return response;
    }

    @RequestMapping(value = "/sendNotification", name = "发送消息通知")
    @ResponseBody
    public JSONResponse sendNotification(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = null;
        try{
            JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
            AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
            int uid = (int)user.getId();
            response = userService.sendNotification(uid,
                    jsonData.getBoolean("all"),
                    jsonData.getString("receiver"),
                    jsonData.getString("setnotificationtype"),
                    jsonData.getString("title"),
                    jsonData.getString("content"),
                    jsonData.getString("objectid")
            );
        }catch (Exception e){
            response = new CommonResponse(-1, "发送消息失败:"+e.getMessage());
        }
        return response;
    }

    @RequestMapping(value ="/readNotification", name = "阅读消息通知")
    @ResponseBody
    public JSONResponse readNotification(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = null;
        try{
            JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
            AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
            String uid = user.getId()+"";
            response = userService.readNotification(jsonData.getLong("notificationid"), uid);
        }catch (Exception e){
            response = new CommonResponse(-1, "发送消息失败:"+e.getMessage());
        }
        return response;
    }

    @RequestMapping(value = "/sendMessage", name = "发送留言")
    @ResponseBody
    public JSONResponse sendMessage(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = null;
        try{
            JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
            AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
            int uid = (int)user.getId();
            response = userService.sendNotification(uid,
                    true,
                    jsonData.getString("receiver"),
                    Constant.NOTIFICATION_TYPE_MESSAGE,
                    "",
                    jsonData.getString("message"),
                    "3"
            );
        }catch (Exception e){
            response = new CommonResponse(-1, "发送留言失败:"+e.getMessage());
        }
        return response;
    }


}
