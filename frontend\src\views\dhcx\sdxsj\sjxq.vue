<template>
  <div class="modal-container">
    <a-spin :spinning="loading">
    <a-empty v-if="isEmpty"/>
    <template v-else>
      <template v-if="descList.length > 0">
        <div>您的适当性属性信息</div>
        <a-descriptions :bordered="true"
                        :column="2"
                        size="small"
                        :label-style="{width: '22%'}"
                        :content-style="{width: '28%'}">
          <template v-for="item in descList" :key="item.ysdm">
            <a-descriptions-item :label="item.ysmc">{{item.value}}</a-descriptions-item>
          </template>
        </a-descriptions>
      </template>
      <template v-for="item in tables" :key="item.title">
        <div class="title">{{item.title}}</div>
        <div class="sdxsj-content">
          <a-table :columns="item.columns" :data-source="item.dataSource" :pagination="false">
          </a-table>
        </div>
      </template>
      <div class="title" v-if="jgsm">持续性评估结果 :
        <span style="font-weight: bold;font-size: 18px;color: #FF7800;">{{jgsm}}</span>
      </div>
      <div class="tips-content" v-if="sjxq">
        <div style="padding: 0 10px" v-html="sjxq"></div>
      </div>
    </template>
    </a-spin>
  </div>
</template>
<script setup>
import { useRoute } from 'vue-router';
import {ref, onMounted} from "vue";
import {commonApi} from "@/api/common";
import {message} from "ant-design-vue";

const props = defineProps({
  sjid: {
    type: Number,
  }
})
const loading = ref(false)
const isEmpty = ref(true)
const jgsm = ref("")
const sjxq = ref("")
const descList = ref([])
const tables = ref([])
const initTable = (list) => {
  tables.value = []
  const columns = []
  let dataSource = [];
  list?.forEach(item => {
    if (item.columns) {
      item.columns.forEach(col => {
        columns.push({
          title: col.ysmc,
          dataIndex: col.ysdm,
        })
      })
      if(item.value) {
        dataSource = JSON.parse(item.value)||[]
      }
      tables.value.push({
        title: item.ysmc,
        columns: columns,
        dataSource: dataSource
      })
    }
  })
}
onMounted(() => {
  const route = useRoute();
  const sjid = route.query.sjid||props.sjid;
  loading.value = true;
  commonApi.executeAMS(
      "sdx.query.ISdxsjService",
      "sdxsjDesc",
      {
        id: sjid
      }
  ).then((res) => {
    if (res.code < 0) {
      message.error(res.note);
    }
    isEmpty.value = res.code === 0
    descList.value = res.descList?.filter(item => {
      return !item.columns
    })
    jgsm.value = res.jgsm
    sjxq.value = res.sjxq
    initTable(res.descList)
  }).finally(() => {
    loading.value = false;
  })
})
</script>
<style>
.tips-important {
  color: #B48A3B;
  font-size: 20px;
  padding: 0 5px;
}
</style>
<style scoped>
.modal-container {
  padding: 10px 10px 10px 20px;
  background-color: #FFFFFF;
  height: 100%;
  border-radius: 4px;
  line-height: 30px;
  color: #888888;
  overflow-y: auto;
}
.title{
  padding-top: 10px;
}
.tips-content {
  height: 80px;
  background: rgb(245, 249, 252);
  border-radius: 4px;
  display: flex;
  align-items: center;
}
</style>