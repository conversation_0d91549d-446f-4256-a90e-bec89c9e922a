package com.apex.sdx.gateway.base.model;

import com.apexsoft.LiveProperty;
import lombok.Data;

@Data
public class User {
    @LiveProperty(note = "id", index = 1)
    private Long id;
    @LiveProperty(note = "柜员号", index = 2)
    private String userid;
    @LiveProperty(note = "柜员姓名", index = 3)
    private String name;
    @LiveProperty(note = "用户级别", index = 4)
    private Long grade;
    @LiveProperty(note = "最近登录时间", index = 5)
    private String lastlogin;
    @LiveProperty(note = "登录次数", index = 6)
    private Long logins;
    @LiveProperty(note = "密码变更日期", index = 7)
    private String chgpwdtime;
    @LiveProperty(note = "更密周期（天）", index = 8)
    private Long chgpwdlimit;
    @LiveProperty(note = "允许登录", index = 9)
    private Long status;
    @LiveProperty(note = "登录IP限制", index = 10)
    private String iplimit;
    @LiveProperty(note = "数字证书号", index = 11)
    private String certno;
    @LiveProperty(note = "柜员营业部", index = 12)
    private Integer orgid;
    @LiveProperty(note = "头像", index = 13)
    private byte[] photo;
    @LiveProperty(note = "锁定时间", index = 14)
    private String locktime;
    @LiveProperty(note = "重试次数", index = 15)
    private Integer retrycount;
    @LiveProperty(note = "最近重试时间", index = 16)
    private String lasttrytime;
    @LiveProperty(note = "用户属性", index = 17)
    private Integer userattribute;

}
