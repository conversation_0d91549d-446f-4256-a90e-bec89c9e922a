<template>
  <a-row style="align-items: center; padding-bottom: 20px">
    <a-col :span="3" class="attr-title">
      校验对象:
    </a-col>
    <a-col :span="5">
      <a-select v-model:value="jydx"
                :options="dictArray?.HMD_JYDX"
                allow-clear
                :field-names="{ label: 'note', value: 'ibm' }"
                style="width: 95%"
                placeholder="校验对象"/>
    </a-col>
    <a-col :span="3" class="attr-title">
      开始日期:
    </a-col>
    <a-col :span="5">
      <a-date-picker v-model:value="ksrq" style="width: 95%" placeholder="开始日期"/>
    </a-col>
    <a-col :span="3" class="attr-title">
      结束日期:
    </a-col>
    <a-col :span="5">
      <a-date-picker v-model:value="jsrq" style="width: 95%" placeholder="开始日期"/>
    </a-col>
  </a-row>
  <a-row style="align-items: center;">
    <a-col :span="3" class="attr-title">
      校验名单类型:
    </a-col>
    <a-col :span="5">
      <a-select
        v-model:value="jymdlx"
        :options="dictArray?.HMD_JYMDLX"
        allow-clear :field-names="{ label: 'note', value: 'ibm' }"
        style="width: 95%"
        placeholder="校验名单类型"
      />
    </a-col>
    <a-col :span="3" class="attr-title">
      校验名单结果:
    </a-col>
    <a-col :span="5">
      <a-select
        v-model:value="jymdjg"
        :options="dictArray?.HMD_JYJG"
        allow-clear
        :field-names="{ label: 'note', value: 'ibm' }"
        style="width: 95%"
        placeholder="校验名单结果"
      />
    </a-col>
    <a-col :span="5" offset="2">
      <div class="cxtj_item">
        <a class="btn" @click="getHmdjyjgls">查询</a>
        <a class="btn fz" @click="reset">重置</a>
      </div>
    </a-col>
  </a-row>
  <a-divider style="margin: 12px 0"/>
  <div>
    <a-table
      :columns="columns"
      :data-source="data"
      row-key="id"
      :loading="loading"
      :pagination="pagination"
      @change="pageChange"
      :scroll="{x: 1500}"
    >
    </a-table>
  </div>
</template>
<script>
import {defineComponent} from "vue";
import {commonApi} from "@/api/common";
import dayjs from "dayjs";
import {message} from "ant-design-vue";
import {getDcNote} from "@utils/bussinessUtils";

export default defineComponent({
  name: "hmdjy",
  inject: ["khh"],
  props: ["dictArray"],
  data() {
    return {
      ksrq: null,
      jsrq: null,
      jydx: null,
      jymdlx: null,
      jymdjg: null,
      loading: false,
      columns: [
        { title: "校验名单类型", dataIndex: "jymdlx", key: "jymdlx", fixed: true, width: 150,
          customRender: ({text}) => {
            return getDcNote("HMD_JYMDLX", text, this.dictArray)
          }
        },
        { title: "校验对象", dataIndex: "jydx", key: "jydx", width: 100,
          customRender: ({text}) => {
            return getDcNote("HMD_JYDX", text, this.dictArray)
          }
        },
        { title: "客户号", dataIndex: "khh", key: "khh", width: 150, },
        { title: "证件类别", dataIndex: "zjlb", key: "zjlb", width: 100,
          customRender: ({text}) => {
            return getDcNote("SDX_ZJLB", text, this.dictArray)
          }
        },
        { title: "证件编号", dataIndex: "zjbh", key: "zjbh", width: 100, },
        { title: "客户姓名", dataIndex: "khxm", key: "khxm", width: 100, },
        { title: "客户全称", dataIndex: "khqc", key: "khqc", width: 100, },
        { title: "客户类别", dataIndex: "khlb", key: "khlb", width: 100,
          customRender: ({text}) => {
            return getDcNote("SDX_ZJLB", text, this.dictArray)
          }
        },
        { title: "性别", dataIndex: "xb", key: "xb", width: 100,
          customRender: ({text}) => {
            return getDcNote("SDX_XB", text, this.dictArray)
          }
        },
        { title: "国籍", dataIndex: "gj", key: "gj", width: 100, },
        { title: "出生日期", dataIndex: "csrq", key: "csrq", width: 100, },
        { title: "名单客户姓名", dataIndex: "mdkhxm", key: "mdkhxm", width: 150, },
        { title: "名单证件类别", dataIndex: "mdzjlb", key: "mdzjlb", width: 150,
          customRender: ({text}) => {
            return getDcNote("SDX_ZJLB", text, this.dictArray)
          }
        },
        { title: "名单证件编号", dataIndex: "mdzjbh", key: "mdzjbh", width: 150, },
        { title: "名单性别", dataIndex: "mdxb", key: "mdxb", width: 100,
          customRender: ({text}) => {
            return getDcNote("SDX_XB", text, this.dictArray)
          }
        },
        { title: "名单国籍", dataIndex: "mdgj", key: "mdgj", width: 100, },
        { title: "名单出生日期", dataIndex: "mdcsrq", key: "mdcsrq", width: 150, },
        { title: "名单外部id", dataIndex: "mdwbid", key: "mdwbid", width: 150, },
        { title: "黑名单id", dataIndex: "hmdid", key: "hmdid", width: 100, },
        { title: "校验名单结果", dataIndex: "jymdjg", key: "jymdjg", width: 150,
          customRender: ({text}) => {
            return getDcNote("HMD_JYJG", text, this.dictArray)
          }
        },
        { title: "业务代码", dataIndex: "ywdm", key: "ywdm", width: 100, },
        { title: "日期", dataIndex: "rq", key: "rq", width: 100, },
        { title: "非主体对象id", dataIndex: "fztdxid", key: "fztdxid", width: 150, },
      ],
      data: [],
      current: 1,
      pageSize: 10,
      total: 0,
    };
  },
  computed: {
    pagination() {
      return {
        total: this.total,
        current: this.current,
        pageSize: this.pageSize,
        //showQuickJumper: true,
        showSizeChanger: true,
        showTotal(total) {
          return "共 " + total + " 条";
        },
      };
    },
  },
  methods: {
    pageChange(page) {
      this.current = page.current;
      this.pageSize = page.pageSize;
      this.getHmdjyjgls();
    },
    reset() {
      this.ksrq = null;
      this.jsrq = null;
      this.jydx = null;
      this.jymdlx = null;
      this.jymdjg = null;
    },
    getHmdjyjgls() {
      this.loading = true;
      commonApi.executeAMS(
        "sdx.query.IHmdjyjglsService",
        "hmdjyjglscx",
        {
          khh: this.khh,
          ksrq: this.ksrq ? dayjs(this.ksrq).format("YYYYMMDD") : null,
          jsrq: this.jsrq ? dayjs(this.jsrq).format("YYYYMMDD") : null,
          jydx: this.jydx,
          jymdlx: this.jymdlx,
          jymdjg: this.jymdjg,
          isSearchCount: true,
          pagenum: this.current,
          pagesize: this.pageSize
        },
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.data = res.records || [];
        this.total = res.total;
      }).finally(() => {
        this.loading = false;
      })
    },
  },
});
</script>
<style scoped>
.attr-title {
  text-align: right;
  padding-right: 5px;
}

.dxsdx_cxtj {
  line-height: 32px;
  padding: 0 5px 3px 10px;
}

.dxsdx_cxtj .cxtj_item {
  white-space: nowrap;
  margin-bottom: 12px;
  font-size: 14px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 20px;
}

.dxsdx_cxtj .cxtj_item span {
  color: #888;
  display: inline-block;
  margin-right: 15px;
  vertical-align: middle;
}

.dxsdx_cxtj .cxtj_item input[type=text] {
  display: inline-block;
  vertical-align: middle;
  width: 340px;
  height: 32px;
  line-height: 30px;
  border: 1px solid #d6d6d6;
  border-radius: 4px;
  padding: 0 10px;
}

.dxsdx_cxtj .cxtj_item input[type=text]:focus {
  outline: 1px solid #d0ad6b;
}

a.btn {
  min-width: 80px;
  padding: 0 10px;
  margin-right: 10px;
  text-align: center;
  line-height: 32px;
  border-radius: 4px;
  background-color: #f6e5d1;
  color: #bf935f;
  cursor: pointer;
  display: inline-block;
  vertical-align: middle;
}

a.btn.fz {
  background-color: #fff;
  color: #777;
  border: 1px solid #d6d6d6;
  line-height: 30px;
}

a.btn:hover, a.btn.fz:Hover {
  background-color: #bf935f;
  color: #fff;
  border: none;
  line-height: 32px;
}
</style>