package com.apex.sdx.api.vo.khgl;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025-01-16
 * @Description:
 */
@Getter
@Setter
public class KhxxVo {

    /**
     * 客户号
     */
    @LiveProperty(note = "客户号", index = 1)
    private String khh;

    /**
     * 客户简称
     */
    @LiveProperty(note = "客户简称", index = 2)
    private String khjc;

    /**
     * 客户名称
     */
    @LiveProperty(note = "客户名称", index = 3)
    private String khmc;

    /**
     * 客户状态，数据字典：GT_KHZT
     */
    @LiveProperty(note = "客户状态，数据字典：GT_KHZT", index = 4)
    private Integer khzt;

    /**
     * 营业部
     */
    @LiveProperty(note = "营业部", index = 5)
    private Integer yyb;

    /**
     * 客户类别，数据字典：GT_KHLB
     */
    @LiveProperty(note = "客户类别，数据字典：GT_KHLB", index = 6)
    private Integer khlb;

    /**
     * 客户群组
     */
    @LiveProperty(note = "客户群组", index = 7)
    private Integer khqz;

    /**
     * 开户方式，数据字典：GT_KHFS
     */
    @LiveProperty(note = "开户方式，数据字典：GT_KHFS", index = 8)
    private Integer khfs;

    /**
     * 开户日期，8位数字yyyymmdd
     */
    @LiveProperty(note = "开户日期，8位数字yyyymmdd", index = 9)
    private Integer khrq;

    /**
     * 销户日期，yyyymmdd
     */
    @LiveProperty(note = "销户日期，yyyymmdd", index = 10)
    private Integer xhrq;

    /**
     * 证件类别，数据字典：GT_ZJLB
     */
    @LiveProperty(note = "证件类别，数据字典：GT_ZJLB", index = 11)
    private Integer zjlb;

    /**
     * 证件号码
     */
    @LiveProperty(note = "证件号码", index = 12)
    private String zjbh;

    /**
     * 证件起始日期，yyyymmdd
     */
    @LiveProperty(note = "证件起始日期，yyyymmdd", index = 13)
    private Integer zjqsrq;

    /**
     * 证件截止日期，yyyymmdd，长期为30001231
     */
    @LiveProperty(note = "证件截止日期，yyyymmdd，长期为30001231", index = 14)
    private Integer zjjzrq;

    /**
     * 证件地址
     */
    @LiveProperty(note = "证件地址", index = 15)
    private String zjdz;

    /**
     * 证件地址邮编
     */
    @LiveProperty(note = "证件地址邮编", index = 16)
    private String zjdzyb;

    /**
     * 证件发证机关
     */
    @LiveProperty(note = "证件发证机关", index = 17)
    private String zjfzjg;

    /**
     * 证件照片
     */
    @LiveProperty(note = "证件照片", index = 18)
    private String zjzp;

    /**
     * 地址
     */
    @LiveProperty(note = "地址", index = 19)
    private String dz;

    /**
     * 邮政编码
     */
    @LiveProperty(note = "邮政编码", index = 20)
    private String yzbm;

    /**
     * 电话
     */
    @LiveProperty(note = "电话", index = 21)
    private String dh;

    /**
     * 手机
     */
    @LiveProperty(note = "手机", index = 22)
    private String sj;

    /**
     * 传真
     */
    @LiveProperty(note = "传真", index = 23)
    private String cz;

    /**
     * 电子邮箱
     */
    @LiveProperty(note = "电子邮箱", index = 24)
    private String email;

    /**
     * 国籍
     */
    @LiveProperty(note = "国籍", index = 25)
    private Integer gj;


    /**
     * 一户通签约标志
     */
    @LiveProperty(note = "一户通签约标志", index = 26)
    private Integer yhtqybz;

    /**
     * 一户通签约日期
     */
    @LiveProperty(note = "一户通签约日期", index = 27)
    private Integer yhtqyrq;

    /**
     * 一码通号
     */
    @LiveProperty(note = "一码通号", index = 28)
    private String ymth;


    /**
     * 读卡标志
     */
    @LiveProperty(note = "读卡标志", index = 29)
    private Integer dkbz;


    /**
     * 税收居民身份，数据字典：GT_SSJMSF
     */
    @LiveProperty(note = "税收居民身份，数据字典：GT_SSJMSF", index = 30)
    private Integer ssjmsf;

    /**
     * 一户通号
     */
    @LiveProperty(note = "一户通号", index = 31)
    private String cid;

    /**
     * 反洗钱行业类别 数据字典：GT_FXQHYLB
     */
    @LiveProperty(note = "反洗钱行业类别", index = 32)
    private Integer fxqhylb;

    /**
     * 洗钱风险等级 数据字典：GT_XQFXDJ
     */
    @LiveProperty(note = "洗钱风险等级", index = 33)
    private Integer xqfxdj;

    /**
     * 反洗钱设置日期
     */
    @LiveProperty(note = "反洗钱设置日期", index = 34)
    private Integer fxqszrq;

    /**
     * 投资者分类 数据字典：TZZFL
     */
    @LiveProperty(note = "投资者分类", index = 35)
    private Integer tzzfl;

    /**
     * 投资品种
     */
    @LiveProperty(note = "投资品种", index = 36)
    private String tzpz;

    /**
     * 投资期限 数据字典：TZQX
     */
    @LiveProperty(note = "投资期限", index = 37)
    private Integer tzqx;

    /**
     * 预期收益
     */
    @LiveProperty(note = "预期收益", index = 38)
    private String yqsy;

    /**
     * 投资者判定日期
     */
    @LiveProperty(note = "投资者判定日期", index = 39)
    private Integer tzzpdrq;

    /**
     * 投资者判定有效期
     */
    @LiveProperty(note = "投资者判定有效期", index = 40)
    private Integer tzzpdyxq;

    /**
     * 不良诚信记录
     */
    @LiveProperty(note = "不良诚信记录", index = 41)
    private String blcxjl;

    /**
     * 其他反洗钱行业类别信息
     */
    @LiveProperty(note = "其他反洗钱行业类别信息", index = 42)
    private String qtfxqhylbxx;

    /**
     * 测评等级
     */
    @LiveProperty(note = "测评等级", index = 43)
    private Integer cpdj;

    /**
     * 测评得分
     */
    @LiveProperty(note = "测评得分", index = 44)
    private BigDecimal cpdf;

    /**
     * 测评日期
     */
    @LiveProperty(note = "测评日期", index = 45)
    private Integer cprq;

    /**
     * 测评有效期
     */
    @LiveProperty(note = "测评有效期", index = 46)
    private Integer cpyxq;

    /**
     * 测评有效期_剩余天数
     */
    @LiveProperty(note = "测评有效期剩余天数", index = 47)
    private String cpyxqsyts;
    /**
     * 测评有效期_剩余天数
     */
    @LiveProperty(note = "投资者判定有效期剩余天数", index = 48)
    private String tzzpdyxqsyts;

}
