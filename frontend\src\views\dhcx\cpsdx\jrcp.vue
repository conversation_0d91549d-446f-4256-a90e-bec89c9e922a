<template>
  <div class="dxsdx_khqjgk_nav">
    <div class="dxsdx_cpsdx innerbox2" @scroll="handleScroll">
      <div class="dxsdx_cpsdx_left" ref="contentRef">
        <div class="shreach_cp_info">
          <a-input :bordered="false" placeholder="请输入产品名称" v-model:value="searchCpmc"/>
          <i class="shreach_tb" @click="queryCpmc"></i>
        </div>
        <div class="cpsdx-content">
          <a-collapse
              v-model:activeKey="activeKey"
              :bordered="false"
              style="background: rgb(255, 255, 255)"
          >
            <template #expandIcon="{ isActive }">
              <caret-right-outlined :rotate="isActive ? 90 : 0" style="color: #AAAAAA"/>
            </template>
            <a-collapse-panel key="1000" :header="header1" style="border: 0" :id="`section-1000`">
              <a-table :columns="columns1" :data-source="kfsjjccData" :scroll="{ x: 1200 }"></a-table>
            </a-collapse-panel>
            <a-collapse-panel key="1003" :header="header2" style="border: 0" :id="`section-1003`">
              <a-table :columns="columns2" :data-source="cwcpccData" :scroll="{ x: 1200 }"></a-table>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </div>
      <div class="dxsdx_cpsdx_right">
        <div class="dxsdx_cpsdx_sx">
          <a-switch v-model:checked="checked" style="margin: 0 10px 0 20px" @change="bsdChange"/>
          <span style="font-weight: 400;font-size: 14px;color: #333333; line-height: 55px">仅看不适当</span>
        </div>
        <div class="dxsdx_cpsdx_fl">
          <ul>
            <li
                v-for="(item, index) in menuItems"
                :key="item.key"
                :class="{ cur: activeSection === item.key }"
                @click="scrollToSection(item.key)"
                style="cursor: pointer"
            >
              {{ item.label }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {defineComponent} from "vue";
import {CaretRightOutlined} from "@ant-design/icons-vue";
import {commonApi} from "@/api/common";
import {message} from "ant-design-vue";
import {formatDate, getDcNote} from "@utils/bussinessUtils";

export default defineComponent({
  name: "jrcp",
  inject: ["khh", "LastTradingDay"],
  props: ['dictArray'],
  components: {
    CaretRightOutlined,
  },
  data() {
    return {
      header1: "开放式基金持仓（0）",
      header2: "场外产品持仓（0）",
      checked: false,
      activeKey: [],
      ywxts: ['1000', '1003'],//默认查询全部的标签
      text: "1",
      columns1: [
        {title: '基金代码', dataIndex: 'tadm', key: 'tadm', width: 150},
        {title: '基金公司', dataIndex: 'tamc', key: 'tamc', width: 200},
        {title: '基金账号', dataIndex: 'jjzh', key: 'jjzh', width: 150},
        {title: '交易账号', dataIndex: 'jyzh', key: 'jyzh', width: 150},
        {title: '产品代码', dataIndex: 'cpdm', key: 'cpdm', width: 150},
        {title: '产品名称', dataIndex: 'cpmc', key: 'cpmc', width: 150},
        {
          title: '状态', dataIndex: 'cpzt', key: 'cpzt', width: 100,
          customRender: ({text}) => {
            return getDcNote("SDX_CPJYZT", text, this.dictArray)
          }
        },
        {
          title: '持仓日期', dataIndex: 'kcrq', key: 'kcrq', width: 180,
          customRender: ({text}) => {
            return formatDate(text);
          }
        },
        {
          title: '产品风险等级', dataIndex: 'cpfxdj', key: 'cpfxdj', width: 150,
          customRender: ({text}) => {
            return getDcNote("SDX_CPFXDJ", text, this.dictArray)
          }
        },
        {
          title: '产品投资品种', dataIndex: 'cptzpz', key: 'cptzpz', width: 350,
          customRender: ({text}) => {
            return getDcNote("SDX_CPTZPZ", text, this.dictArray)
          }
        },
        {
          title: '产品投资期限', dataIndex: 'cptzqx', key: 'cptzqx', width: 150,
          customRender: ({text}) => {
            return getDcNote("SDX_CPTZQX", text, this.dictArray)
          }
        },
        {
          title: '适当性结果', dataIndex: 'sdxjg', key: 'sdxjg', width: 150,
          customRender: ({text}) => {
            return getDcNote("SDX_PPJG", text, this.dictArray);
          }
        },

      ],
      columns2: [
        {title: '基金代码', dataIndex: 'tadm', key: 'tadm', width: 150},
        {title: '基金公司', dataIndex: 'tamc', key: 'tamc', width: 200},
        {title: '基金账号', dataIndex: 'jjzh', key: 'jjzh', width: 150},
        {title: '交易账号', dataIndex: 'jyzh', key: 'jyzh', width: 150},
        {title: '产品代码', dataIndex: 'cpdm', key: 'cpdm', width: 150},
        {title: '产品名称', dataIndex: 'cpmc', key: 'cpmc', width: 150},
        {title: '状态', dataIndex: 'cpzt', key: 'cpzt', width: 100},
        {
          title: '持仓日期', dataIndex: 'kcrq', key: 'kcrq', width: 180,
          customRender: ({text}) => {
            return formatDate(text);
          }
        },
        {
          title: '产品风险等级', dataIndex: 'cpfxdj', key: 'cpfxdj', width: 200,
          customRender: ({text}) => {
            return getDcNote("SDX_CPFXDJ", text, this.dictArray)
          }
        },
        {
          title: '产品投资品种', dataIndex: 'cptzpz', key: 'cptzpz', width: 350,
          customRender: ({text}) => {
            return getDcNote("SDX_CPTZPZ", text, this.dictArray)
          }
        },
        {
          title: '产品投资期限', dataIndex: 'cptzqx', key: 'cptzqx', width: 200,
          customRender: ({text}) => {
            return getDcNote("SDX_CPTZQX", text, this.dictArray)
          }
        },
        {
          title: '适当性结果', dataIndex: 'sdxjg', key: 'sdxjg', width: 200,
          customRender: ({text}) => {
            return getDcNote("SDX_PPJG", text, this.dictArray);
          }
        },

      ],
      kfsjjccData: [],
      cwcpccData: [],
      kfsjjccMetaData: [],
      cwcpccMetaData: [],
      menuItems: [
        {
          key: 1000,
          label: '开放式基金持仓',
        },
        {
          key: 1003,
          label: '场外产品持仓',
        },
      ],
      activeSection: 1000,
      searchCpmc: '',//搜索框
    };
  },
  watch: {
    kfsjjccData() {
      return this.header1 = '开放式基金持仓（' + this.kfsjjccData.length + ')';
    },
    cwcpccData() {
      return this.header2 = '场外产品持仓（' + this.cwcpccData.length + ')';
    },
    /* activeKey(newVal, oldVal) {//不需要缓存，且在activeKey变化时，只查询新增的key值  若可以缓存，则此处可以不用
       const oldValSet = new Set(oldVal);
       const result = newVal.filter(item => !oldValSet.has(item));// 找出新增的key值数组,即每次只查询新打开的面板，避免重复查询
       this.getKfsjjcc(result);
     },*/

  },
  methods: {
    // 滚动到指定区域
    scrollToSection(key) {
      // 展开对应的面板
      if (!this.activeKey.includes(key)) {
        this.activeKey = [...this.activeKey, key]
      }
      this.activeSection = key
      // 等待面板展开后再滚动
      setTimeout(() => {
        const element = document.getElementById(`section-${key}`)
        if (element) {
          element.scrollIntoView({behavior: 'smooth', block: 'start'})
        }
      }, 300)
    },
    // 监听滚动，更新当前活跃section
    handleScroll() {
      const sections = this.menuItems.map(item => {
        const element = document.getElementById(`section-${item.key}`)
        if (element) {
          const rect = element.getBoundingClientRect()
          return {
            key: item.key,
            top: rect.top
          }
        }
        return null
      }).filter(Boolean)

      // 找到最接近顶部的section
      const current = sections.reduce((closest, section) => {
        if (!closest) return section
        return Math.abs(section.top) < Math.abs(closest.top) ? section : closest
      })

      if (current) {
        this.activeSection = current.key
      }
    },
    bsdChange() {
      this.getKfsjjcc(this.ywxts);
    },
    //获取产品适当性
    getKfsjjcc(ywxts) {
      ywxts.forEach(item => {
        commonApi.executeAMS(
            "sdx.query.ICpccsdxService",
            "cpccsdxcx",
            {khh: this.khh, sjlx: item, onlysdx: this.checked, rq: this.LastTradingDay},
        ).then((res) => {
          if (res.code < 0) {
            message.error(res.note);
          }
          if (item === "1000") {
            this.kfsjjccMetaData = res.records || [];
            this.kfsjjccData = this.kfsjjccMetaData;
          } else if (item === "1003") {
            this.cwcpccMetaData = res.records || [];
            this.cwcpccData = this.cwcpccMetaData;
          }
          this.filterCpmc();
        }).finally(() => {
        })
      });
    },
    queryCpmc() {
      this.filterCpmc();
      this.activeKey = [];
      if (this.cwcpccData.length > 0) {
        this.activeKey = [...this.activeKey, 1003]
      }
      if (this.kfsjjccData.length > 0) {
        this.activeKey = [...this.activeKey, 1000]
      }
    },
    filterCpmc() {
      this.cwcpccData = this.cwcpccMetaData.filter(item => item.cpmc?.includes(this.searchCpmc));
      this.kfsjjccData = this.kfsjjccMetaData.filter(item => item.cpmc?.includes(this.searchCpmc));

    },
  },
  mounted() {
    this.getKfsjjcc(this.ywxts);//初始化查询全部
  },
});
</script>
<style scoped>
.dxsdx_khqjgk_nav {
  background-color: #fff;
  border-radius: 8px;
  height: 100%;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.dxsdx_cpsdx {
  position: relative;
  height: calc(100%);
  overflow-y: auto;
  border-radius: 8px;
  background-color: #fff;
}

.dxsdx_cpsdx_left {
  display: inline-block;
  vertical-align: top;
  width: calc(100% - 210px);
}

.dxsdx_cpsdx_right {
  display: inline-block;
  vertical-align: top;
  width: 210px;
  position: fixed;
  top: 130px;
  right: 0;
}

.shreach_cp_info {
  padding: 0px 10px 0 15px;
}

.shreach_cp_info input[type=text] {
  display: inline-block;
  vertical-align: middle;
  width: 340px;
  height: 32px;
  line-height: 30px;
  border: 1px solid #d6d6d6;
  border-radius: 4px;
  padding: 0 10px;
}

.shreach_cp_info input[type=text]:focus {
  outline: 1px solid #d0ad6b;
}

.shreach_cp_info i.shreach_tb {
  font-style: normal;
  font-size: 16px;
  color: #888;
  position: relative;
  left: -25px;
  cursor: pointer;
  z-index: 999;
  top: 2px;
}

.shreach_cp_info i.shreach_tb:before {
  content: "\e625";
  font-family: "iconfont" !important;
}

.shreach_cp_info i.shreach_tb:hover {
  color: #b48a3b;
}

.dxsdx_cpsdx_sx {
  padding-top: 18px;
}

.dxsdx_cpsdx_sx a.ck_jkwcl {
  font-size: 12px;
  cursor: pointer;
  margin-bottom: 25px;
  color: #333;
  display: inline-block;
  vertical-align: middle;
  position: relative;
  padding-left: 40px;
}

.dxsdx_cpsdx_sx a.ck_jkwcl.kq:before {
  content: "";
  display: inline-block;
  vertical-align: middle;
  width: 32px;
  height: 18px;
  border-radius: 9px;
  background-color: #bf935f;
  position: absolute;
  left: 0;
  top: 0px;
}

.dxsdx_cpsdx_sx a.ck_jkwcl.gb:before {
  content: "";
  display: inline-block;
  vertical-align: middle;
  width: 32px;
  height: 18px;
  border-radius: 9px;
  background-color: #e6e6e6;
  position: absolute;
  left: 0;
  top: 0px;
}

.dxsdx_cpsdx_sx a.ck_jkwcl.kq:after {
  content: "";
  display: inline-block;
  vertical-align: middle;
  width: 18px;
  height: 18px;
  border-radius: 9px;
  background-color: #fff;
  position: absolute;
  left: 14px;
  box-shadow: 0 0 4px rgba(0, 0, 0, .3);
  top: 0px;
}

.dxsdx_cpsdx_sx a.ck_jkwcl.gb:after {
  content: "";
  display: inline-block;
  vertical-align: middle;
  width: 18px;
  height: 18px;
  border-radius: 9px;
  background-color: #fff;
  position: absolute;
  left: 0;
  box-shadow: 0 0 4px rgba(0, 0, 0, .3);
  top: 0px;
}

.dxsdx_cpsdx_fl {
}

.dxsdx_cpsdx_fl ul li {
  line-height: 50px;
  font-size: 14px;
  color: #888;
  padding-left: 30px;
  position: relative;
}

.dxsdx_cpsdx_fl ul li.cur {
  padding-bottom: 15px;
  color: #333;
  font-weight: bold;
  padding-top: 15px;
}

.dxsdx_cpsdx_fl ul li:first-child.cur {
  padding-top: 0px;
}

.dxsdx_cpsdx_fl ul li:before {
  content: "\e63e";
  font-family: "iconfont" !important;
  color: #888;
  width: 14px;
  z-index: 88;
  background-color: #fff;
  height: 20px;
  line-height: 20px;
  display: inline-block;
  vertical-align: middle;
  position: absolute;
  left: 0;
  top: 15px;
}

.dxsdx_cpsdx_fl ul li:after {
  content: "";
  display: inline-block;
  vertical-align: middle;
  position: absolute;
  left: 6px;
  top: 0px;
  width: 1px;
  height: calc(100%);
  background-color: #eee;
}

.dxsdx_cpsdx_fl ul li.cur:before {
  content: "\e61f";
  color: #bf935f;
  top: 30px;
}

.dxsdx_cpsdx_fl ul li.cur:after {
  background-color: #bf935f;
}

.dxsdx_cpsdx_fl ul li:first-child.cur:before {
  top: 15px;
}

.dxsdx_cpsdx_fl ul li:first-child.cur:after {
  top: 33px;
}

.dxsdx_cpsdx_fl ul li:nth-child(1).cur:after, .dxsdx_cpsdx_fl ul li:last-child.cur:after {
  height: calc(50%);
}

.dxsdx_cpsdx_fl ul li:last-child:after {
  height: calc(50%);
}

.dxsdx_cpsdx_fl ul li:last-child.cur:after {
  top: 0px;
}

.cpsdx-content {
  padding: 10px;
}

:deep(.ant-collapse>.ant-collapse-item >.ant-collapse-header) {
  border-bottom: 1px solid #EEEEEE;
}

:deep(.ant-switch.ant-switch-checked) {
  background: #bf935f
}

:deep(.ant-switch.ant-switch-checked:hover:not(.ant-switch-disabled)) {
  background: #bf935f
}

</style>