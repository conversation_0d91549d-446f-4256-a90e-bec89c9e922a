subprojects {
    buildscript {
        repositories {
            maven {
                url "https://maven.aliyun.com/repository/public"
            }
        }
    }
    apply plugin: 'java'
    apply plugin: 'java-library'
    apply plugin: 'maven-publish'
    ext {
        springBootVersion = "2.7.11"
        springDependencyManagement = "1.0.11.RELEASE"
        springCloudVersion = "2021.0.5"
        liveVersion = "2.5.3-RC1"
    }
    group = 'com.demo'
    version = '1.0.0-SNAPSHOT'
    sourceCompatibility = '1.8'
    [compileJava, compileTestJava]*.options*.encoding = 'UTF-8'
    compileJava {
        options.compilerArgs << '-parameters'
    }
    compileTestJava {
        options.compilerArgs << '-parameters'
    }
    repositories {
        maven {
            url "https://maven.aliyun.com/repository/public"
        }
        maven {
            credentials {
                username = "${NEXUS_USER}"
                password = "${NEXUS_PASSWORD}"
            }
            url "https://oss.apexsoft.com.cn/repository/maven-public/"
        }
    }

    //依赖缓存时间
    configurations.all {
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    }
    // 上传source
    task sourcesJar(type: Jar) {
        from sourceSets.main.allJava
        classifier = 'sources'
    }
    publishing {
        publications {
            library(MavenPublication) {
                from components.java
                artifact sourcesJar
            }
        }
        repositories {
            maven {
                credentials {
                    username = "${NEXUS_USER}"
                    password = "${NEXUS_PASSWORD}"
                }
                if (project.version.endsWith('-SNAPSHOT')) {
                    url "https://oss.apexsoft.com.cn/repository/maven-snapshots/"
                } else {
                    url "https://oss.apexsoft.com.cn/repository/maven-releases/"
                }
            }
        }
    }


}