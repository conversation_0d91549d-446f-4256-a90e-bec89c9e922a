package com.apex.sdx.svc;

import com.apex.sdx.api.req.query.KhsjzbReq;
import com.apex.sdx.api.resp.common.QueryResponse;
import com.apex.sdx.api.vo.query.KhsjzbVo;
import com.apex.sdx.convert.KhzjsbDtoMapping;
import com.apex.sdx.core.exception.Assert;
import com.apex.sdx.core.interceptor.QueryServiceInterceptor;
import com.apex.sdx.core.mybatis.entity.TicKhsjzb;
import com.apex.sdx.core.mybatis.service.TkhsjzbService;
import com.apexsoft.LiveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import sdx.query.IKhsjzbService;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-04-21
 * @Description:
 */
@Slf4j
@LiveService(interceptor = QueryServiceInterceptor.class, note = "查询客户数据指标")
public class KhsjzbService implements IKhsjzbService {

    @Autowired
    TkhsjzbService tkhsjzbService;

    @Autowired
    private KhzjsbDtoMapping khzjsbDtoMapping;

    @Override
    public QueryResponse<KhsjzbVo> getKhsjzbByCode(KhsjzbReq req) throws Exception {
        Assert.notNull(req, KhsjzbReq::getKhh);
        QueryResponse<KhsjzbVo> result = new QueryResponse<>(1, "查询成功");
        String khh = req.getKhh();
        String rq = req.getRq();
        String ids = req.getIdxCodes();

        //表数据
        List<TicKhsjzb> khsjzbList = tkhsjzbService.queryKhsjzbByKhhCode(khh,rq,ids);
        //转化为vo数据
        List<KhsjzbVo> khsjzbVos = khzjsbDtoMapping.listConver(khsjzbList);
        result.setRecords(khsjzbVos);
        return result;
    }

}
