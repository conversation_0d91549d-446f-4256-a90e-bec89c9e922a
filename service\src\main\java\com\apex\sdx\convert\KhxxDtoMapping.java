package com.apex.sdx.convert;

import com.apex.sdx.api.vo.khgl.KhxxVo;
import com.apex.sdx.core.converter.IMapping;
import com.apex.sdx.core.converter.MapStructConfig;
import com.apex.sdx.core.mybatis.entity.Tkhxx;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025-01-16
 * @Description:
 */
@Mapper(config = MapStructConfig.class)
@Component
public interface KhxxDtoMapping extends IMapping<KhxxVo, Tkhxx> {
}
