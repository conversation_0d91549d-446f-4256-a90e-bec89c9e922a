<template>
  <div class="dxsdx_cxtj">
    <div class="cxtj_item">
      <span>开始日期：</span>
      <a-date-picker v-model:value="ksrq" style="width: 250px" placeholder="开始日期" />
    </div>
    <div class="cxtj_item">
      <span>结束日期：</span>
      <a-date-picker v-model:value="jsrq" style="width: 250px" placeholder="结束日期" />
    </div>
    <div class="cxtj_item">
      <a class="btn" @click="getJgxxhcsq">查询</a>
      <a class="btn fz" @click="reset">重置</a>
    </div>
    <a-divider style="margin: 12px 0"/>
    <div>
      <a-table
        :columns="columns"
        :data-source="data"
        :loading="loading"
        :pagination="pagination"
        @change="pageChange"
        :scroll="{x: 1500}"
      ></a-table>
    </div>
  </div>
</template>
<script>
import {defineComponent} from "vue";
import {getDcNote} from "@utils/bussinessUtils";
import {commonApi} from "@/api/common";
import dayjs from "dayjs";
import {message} from "ant-design-vue";
export default defineComponent ({
  name: "jgxxhc",
  inject: ["khh"],
  props: ["dictArray", "khjbxx"],
  data() {
    return {
      ksrq: null,
      jsrq: null,
      columns: [
        { title: '申请编号', dataIndex: 'sqbh', key: 'sqbh', fixed: true, width: 100 },
        { title: '处理标志', dataIndex: 'clbz', key: 'clbz', width: 100,
          customRender: ({ text }) => {
            return getDcNote("ZDZH_CLBZ", text, this.dictArray);
          }
        },
        { title: '结果说明', dataIndex: 'jgsm', key: 'jgsm', width: 150,},
        { title: '客户号', dataIndex: 'khh',  key: 'khh', width: 150},
        { title: '客户名称', dataIndex: 'khmc',  key: 'khmc', width: 100},
        { title: '证件编号', dataIndex: 'zjbh',  key: 'zjbh', width: 150,},
        { title: '辅助证件编号', dataIndex: 'fzzjbh',  key: 'fzzjbh', width: 150,},
        { title: '申请日期', dataIndex: 'sqrq',  key: 'sqrq', width: 120,},
        { title: '申请时间', dataIndex: 'sqsj',  key: 'sqsj', width: 120,},
        { title: '营业部', dataIndex: 'yyb',  key: 'yyb', width: 150,
          customRender: ({ text }) => {
            return getDcNote("YYB", text, this.dictArray);
          }
        },
        { title: '申请柜员', dataIndex: 'sqgy',  key: 'sqgy', width: 100, },
        { title: '开户机构代码', dataIndex: 'khjgdm',  key: 'khjgdm', width: 150, },
        { title: '开户网点代码', dataIndex: 'khwddm',  key: 'khwddm', width: 150, },
      ],
      data: [],
      open: false,
      loading: false,
      current: 1,
      pageSize: 10,
      total: 0,
    }
  },
  computed: {
    pagination() {
      return {
        total: this.total,
        current: this.current,
        pageSize: this.pageSize,
        //showQuickJumper: true,
        showSizeChanger: true,
        showTotal(total) {
          return "共 " + total + " 条";
        },
      };
    }
  },
  methods: {
    pageChange(page){
      this.current = page.current;
      this.pageSize = page.pageSize;
      this.getJgxxhcsq();
    },
    reset(){
      this.ksrq = null;
      this.jsrq = null;
    },
    getJgxxhcsq() {
      this.loading = true;
      commonApi.executeAMS(
        "sdx.query.IZdZhywsqService",
        "jgkhxxhcsqcx",
        {
          khh: this.khh,
          ksrq: this.ksrq ? dayjs(this.ksrq).format("YYYYMMDD") : null,
          jsrq: this.jsrq ? dayjs(this.jsrq).format("YYYYMMDD") : null,
          isSearchCount: true,
          pagenum: this.current,
          pagesize: this.pageSize
        },
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.data = res.records || [];
        this.total = res.total;
      }).finally(() => {
        this.loading = false;
      })
    },
  },
});
</script>
<style scoped>
.dxsdx_cxtj{line-height: 32px; padding: 0 5px 3px 10px;}
.dxsdx_cxtj .cxtj_item{ white-space: nowrap;margin-bottom: 12px; font-size: 14px; display: inline-block; vertical-align: middle; margin-right: 20px;}
.dxsdx_cxtj .cxtj_item span{color: #888; display: inline-block; margin-right: 15px; vertical-align: middle;}
.dxsdx_cxtj .cxtj_item input[type=text]{display: inline-block; vertical-align: middle; width: 340px; height: 32px; line-height: 30px; border:1px solid #d6d6d6; border-radius: 4px; padding: 0 10px;}
.dxsdx_cxtj .cxtj_item input[type=text]:focus{outline:1px solid #d0ad6b;}
a.btn{min-width: 80px; padding: 0 10px; margin-right: 10px; text-align: center; line-height: 32px;border-radius: 4px; background-color: #f6e5d1; color: #bf935f; cursor: pointer; display: inline-block; vertical-align: middle;}
a.btn.fz{background-color: #fff; color: #777;border: 1px solid #d6d6d6; line-height: 30px;}
a.btn:hover,a.btn.fz:Hover{ background-color: #bf935f; color: #fff; border: none; line-height: 32px;}
</style>