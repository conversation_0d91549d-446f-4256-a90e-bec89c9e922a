package com.apex.sdx.gateway.base.controller;

import com.alibaba.fastjson.JSONObject;
import com.apex.sdx.gateway.aas.modules.index.service.DefaultServiceHandler;
import com.apex.sdx.gateway.base.dao.CommonDao;
import com.apex.sdx.gateway.base.model.CommonResponse;
import com.apex.sdx.gateway.base.model.JSONResponse;
import com.apex.sdx.gateway.aas.modules.index.model.AuthUser;
import com.apexsoft.live.session.UserAuthenticateSession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.net.URLEncoder;
import java.util.UUID;


@RequestMapping("/file")
@Controller
@Slf4j
public class FileController {

    @Autowired
    CommonDao commonDao;

    private DefaultServiceHandler defaultServiceHandler = new DefaultServiceHandler();

    @RequestMapping(value = "/fileUpload",name = "文件上传")
    @ResponseBody
    public JSONResponse fileUpload(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "上传成功");
        File file = null;
        try{
            AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
            String uid = user.getId()+"";

            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) httpServletRequest;
            // 获得文件：
            MultipartFile multipartFile = multipartRequest.getFile("file");
            file = new File(new File("").getAbsoluteFile()+File.separator+ UUID.randomUUID());
            String fileName = multipartFile.getOriginalFilename();
            multipartFile.transferTo(file);

            JSONObject result = commonDao.streamUploadService("ecif.xtgl.IStreamService", "uploadFile", file, fileName, uid);
            log.info("文件上传结果:"+result);
            if(result.getIntValue("code")<0){
                response = new CommonResponse(JSONResponse.CODE_FAIL, "上传失败");
            }else{
                JSONObject data = new JSONObject();
                data.put("filepath", result.getString("filepath"));
                data.put("filelocalpath", result.getString("filelocalpath"));
                response.setData(data);
            }

        }catch (Exception e){
            log.error(e.getMessage(),e);
            response = new CommonResponse(JSONResponse.CODE_FAIL, "上传失败");
        }
        return response;
    }

    @RequestMapping(value = "/fileDownload/{filepath}", name = "文件下载")
    public void fileDownload(@PathVariable String filepath, HttpServletRequest request, HttpServletResponse response) throws Throwable {
        JSONObject result = null;
        File file = null;
        String filename = "";
        try{
            result = commonDao.streamDownloadService("ecif.xtgl.IStreamService","downloadFile", filepath);
            if(result.getIntValue("code")<0){
                throw new Exception("下载失败");
            }else{
                filename = result.getString("filename");
                file = (File)result.get("file");
            }
            filename = URLEncoder.encode(filename,"UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" +filename
                    +";"+"filename*=utf-8''"+filename);
            response.setContentType("application/octet-stream");
            FileUtils.copyFile(file, response.getOutputStream());
        }catch (Exception e){
            log.error(e.getMessage(),e);
            throw e;
        }finally {
            file.delete();
        }
    }
}
