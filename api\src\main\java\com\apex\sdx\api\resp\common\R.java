package com.apex.sdx.api.resp.common;

import com.apexsoft.LiveProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * @Auther yeyilun
 * @Date 2022 01 06
 * @Description:
 */
@Getter
@Setter
@SuperBuilder
public class R implements Serializable {
    public R() {
    }

    public R(int code) {
        this.code = code;
    }

    public R(int code, String note) {
        this.code = code;
        this.note = note;
    }

    /**
     * 返回编码
     */
    @LiveProperty(note = "返回编码", index = 2001)
    private int code;

    /**
     * 编码说明
     */
    @LiveProperty(note = "编码说明", index = 2002)
    private String note;

    /**
     * 错误编码
     */
    @LiveProperty(note = "错误编码", index = 2003)
    private String errorCode;
}
