<template>
  <div>
    <p>适当性管理系统</p>
    <div>
      <a-button @click="goRoute('/czmx')">操作明细查询</a-button>
      <a-button @click="goRoute('/xtgl/fwhcsx')">服务缓存刷新</a-button>
      <a-button @click="goRoute('/sdxgl/sdxConfig')">规则配置</a-button>
      <a-button @click="goRoute('/gzxx')">规则信息查询</a-button>
      <a-button @click="goRoute('/dhcx/index')">单户查询</a-button>
      <a-button @click="goRoute('/index/index')">首页大屏</a-button>
      <a-button @click="goRoute('/login')">登录页面</a-button>
      <a-button @click="goRoute('/khqj')">客户全景</a-button>
    </div>
  </div>
</template>

<script>

export default {
  methods: {
    goRoute(path) {
      this.$router.push(path)
    }
  }
}
</script>
