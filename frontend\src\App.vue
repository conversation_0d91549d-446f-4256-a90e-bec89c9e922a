<template>
  <a-config-provider :locale='zhCN'>
    <div id="main">
      <router-view/>
    </div>
  </a-config-provider>
</template>

<script>

import zhCN from "ant-design-vue/lib/locale/zh_CN";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn"; // 导入 dayjs 的中文语言包

dayjs.locale("zh-cn"); // 设置 dayjs 的语言为中文

export default {
  name: 'App',
  data() {
    return {
      zhCN: zhCN,
    };
  },
  /*watch:{
    "$route": {
      handler() {
        debugger
        if(!this.$store.getters.userid) {
          this.$store.dispatch('user/getUserInfo')
        }
      }
    }
  }*/
};

</script>
<style scoped>
#main{
  height: calc(100%);
}
</style>