package com.apex.sdx.core.mybatis.service.impl;


import com.apex.sdx.api.vo.khgl.KhjyqxVo;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhjyqx;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTkhjyqxMapper;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhjyqxService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class TOdsYgtCifTkhjyqxServiceImpl extends ServiceImpl<TOdsYgtCifTkhjyqxMapper, TOdsYgtCifTkhjyqx>
        implements TOdsYgtCifTkhjyqxService {

    @Override
    public Page<KhjyqxVo> queryKhjyqx(String khh, String ywzh, String gdh, Integer jyqx, Integer zt, boolean isSearchCount, int pagesize, int pagenum) {
        Page<KhjyqxVo> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);

        try {
            return this.baseMapper.queryKhjyqx(page, khh, ywzh, gdh, jyqx, zt);
        } catch (Exception e) {
            String note = String.format("查询客户交易权限列表异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }
}




