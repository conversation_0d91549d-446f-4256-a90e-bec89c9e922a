<template>
  <div style="height: calc(100%)">
    <a-affix>
      <div class="khqj_header">
        <a-row class="header_row">
          <a-col :span="1" style="text-align: center">
            <img src="../../assets/images/sfqj_logo.png" width="35">
          </a-col>
          <a-col :span="3">
            <span>客户适当性全景视图</span>
          </a-col>
          <a-col :span="5">
            <div class="khqj_search_input">
              <a-input
                  :bordered="false"
                  style="width: 200px; padding-left: 20px;margin-right: 20px;background: #F2F2F2;border-radius: 16px;"
                  v-model:value="khh"
                  placeholder="请输入客户号"
              />
              <a-button type="primary" size="small" @click="onSearch" style="background-color: #B48A3B" shape="circle" :icon="h(SearchOutlined)"></a-button>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-affix>
    <div class="khqj_main">
      <div class="main_left_sider">
        <div class="profile-card">
          <div class="avatar-wrapper">
            <a-avatar :size="80" class="avatar">
              <template #icon>
                <span class="iconfont icon-user_man" style="font-size: 40px"></span>
              </template>
            </a-avatar>
          </div>
          <div class="header-content" >
            <div class="company-name">{{ khjbxx?.khmc }}</div>
            <div class="company-code">
              <span>{{ khjbxx?.khh }}</span>
              <a-tag color="orange" :bordered="false">
                {{ getDcNote("SDX_KHLB", khjbxx?.khlb, dictArray) }}
              </a-tag>
            </div>
            <div class="contact-info">
              <a-row>
                <a-col :span="5" class="contact-icon">
                  <span class="iconfont icon-yyb"></span>
                </a-col>
                <a-col :span="19">
                  {{ khjbxx?.yyb }}/{{ getDcNote("YYB", khjbxx?.yyb, dictArray) }}
                </a-col>
              </a-row>
              <a-row>
                <a-col :span="5" class="contact-icon">
                  <span class="iconfont icon-dh"></span>
                </a-col>
                <a-col :span="19">
                  <a-row>
                    <a-col>
                      <span>{{ khjbxx?.dh }}</span>
                    </a-col>
                  </a-row>
                  <a-row>
                    <a-col>
                      <a-tag color="#00B42A">
                        核查通过
                      </a-tag>
                    </a-col>
                  </a-row>
                </a-col>
              </a-row>
            </div>
            <div class="status-cards">
              <div class="status-card">
                <a-row>
                  <a-col offset="2" :span="7" class="title">距离风险测评到期剩余天数</a-col>
                  <a-col :span="4" style="padding: 0 10px">
                    <a-tag style="padding-inline: 4px;" :color="show_cpyxq_syts<0?'#f50':'orange'" :bordered="false">
                      {{ show_cpyxq_syts < 0 ? '过期' : '剩余' }}
                    </a-tag>
                  </a-col>
                  <a-col :span="8" offset="0">
                    <span class="days" :title="khjbxx?.cpyxq">{{ show_cpyxq_syts < 0?Math.abs(show_cpyxq_syts):show_cpyxq_syts }}</span>
                    <span class="unit">天</span>
                  </a-col>
                </a-row>
              </div>
              <div class="status-card" v-if="isZytzz">
                <a-row>
                  <a-col offset="2" :span="7" class="title">距专业投资者到期剩余天数</a-col>
                  <a-col :span="4" style="padding: 0 10px">
                    <a-tag style="padding-inline: 4px;" :color="show_tzzpdyxq_syts<0?'#f50':'orange'">
                      {{ show_tzzpdyxq_syts < 0 ? '过期' : '剩余' }}
                    </a-tag>
                  </a-col>
                  <a-col :span="8" offset="0">
                    <span class="days" style="color: #FF1919"
                          :title="khjbxx?.tzzpdyxq">{{ show_tzzpdyxq_syts < 0?Math.abs(show_tzzpdyxq_syts):show_tzzpdyxq_syts }}</span>
                    <span class="unit">天</span>
                  </a-col>
                </a-row>
              </div>
            </div>
<!--            <a-divider/>-->
          </div>
          <div class="menu-wrapper">
            <a-menu
                v-model:selectedKeys="state.curMenuKey"
                mode="inline"
                :items="menus"
                class="operation-menu"
                @click="changeMenu"
            ></a-menu>
          </div>
        </div>
      </div>
      <div class="main_right_content">
        <router-view v-slot="{ Component }">
          <keep-alive :include="cachedComponents">
            <component :is="Component" :dictArray="dictArray" :khjbxx="khjbxx" :key="$route.fullPath" />
          </keep-alive>
        </router-view>
      </div>
    </div>
  </div>
</template>
<script>
import "@/assets/css/iconfont.css";
import {SearchOutlined, UserOutlined} from "@ant-design/icons-vue";
import {computed, h, reactive} from "vue";
import {decrypt, encrypt, getDcNote} from "@utils/bussinessUtils";
import {commonApi} from "@/api/common";
import {message} from "ant-design-vue";
import {dictApi} from "@/api/xtgl/dict";
import dayjs from "dayjs";

const state = reactive({
  collapsed: false,
  curMenuKey: ["khsdxgk"],
});
//需要缓存的组件
const cachedComponents = ['zhsdx', 'qxsdx', 'sdxlh'];

const menus = [
  {
    icon: () => h("span", {
      class: "iconfont icon-khsdxgk",
    }),
    label: "客户适当性概况",
    title: "客户适当性概况",
    path: "/khsdxgk",
    key: "khsdxgk",
  },
  {
    icon: () => h("span", {
      class: "iconfont icon-zhsdx",
    }),
    label: "账户适当性",
    title: "账户适当性",
    key: "zhsdx",
    path: "/zhsdx",
  },
  {
    icon: () => h("span", {
      class: "iconfont icon-qxsdx",
    }),
    label: "权限适当性",
    title: "权限适当性",
    key: "qxsdx",
    path: "/qxsdx",
  },
  {
    icon: h("span", {
      class: "iconfont icon-cpsdx",
    }),
    label: "产品适当性",
    title: "产品适当性",
    key: "cpsdx",
    path: "/cpsdx",
  },
  {
    icon: h("span", {
      class: "iconfont icon-sdxsj",
    }),
    label: "适当性事件",
    title: "适当性事件",
    key: "sdxsj",
    path: "/sdxsj",
  },
  {
    icon: h("span", {
      class: "iconfont icon-sdxlh",
    }),
    label: "适当性留痕",
    title: "适当性留痕",
    key: "sdxlh",
    path: "/sdxlh",
  },
  {
    icon: h("span", {
      class: "iconfont icon-khzmcl",
    }),
    label: "客户证明材料",
    title: "客户证明材料",
    key: "khzmcl",
    path: "/khzmcl",
  },
];
export default {
  name: "khqj",
  provide() {
    return {
      khh: computed(() => this.khh), // 使用 computed 使其保持响应性
      LastTradingDay: computed(() => this.LastTradingDay) // 使用 computed 使其保持响应性
    }
  },
  components: {
    UserOutlined,
  },
  data() {
    return {
      khh: "",
      khjbxx: null,
      show_cpyxq_syts: 0,
      show_tzzpdyxq_syts: 0,
      LastTradingDay: "",//上一个交易日
      srbs: "",
      activeKey: "1",
      state,
      menus,
      cachedComponents,
      dictArray: [],
      isZytzz:false,//是否专业投资者
    }
  },
  methods: {
    h,
    SearchOutlined,
    getDcNote,
    changeMenu(item) {
      this.$router.push({
        path: item.key,
        query: {khh: encrypt(this.khh)}
      })
    },
    getSjzd() {
      dictApi.cxsjzd({
        fldm: "SDX_KHLB;SDX_TZZFL;SDX_TZPZ;SDX_TZQX;SDX_YQSY;SDX_FXCSNL;SDX_JYS;SDX_KHZT;SDX_XQFXDJ;" +
            "SDX_FQQD;SFYZ_CXLX;SFYZ_CZLB;SFYZ_CLJG;SDX_ZJLB;SJHC_HCLB;SJHC_HCJG;SJHC_SJZT;ZDZH_CLBZ;ZDZH_GLGXLX;SDX_SJZT;" +
            "SDX_SJCLFS;SDX_TZZT;SDX_CPFXDJ;SDX_CPTZPZ;SDX_CPTZQX;SDX_CPYQSY;SDX_CXYWLB;SDX_JSLB;HMD_JYMDLX;HMD_JYDX;HMD_JYJG;" +
            "SDX_XB;XY_LXLJFS;XY_HTZT;XY_HTSX;SDX_YWFXDJ;SDX_PPJG;SDX_CPJYZT"
      }).then((res) => {
        if (res.code > 0) {
          this.dictArray = res.sjzd;
          this.getYyList();
        }
      });
    },
    //获取上一个交易日
    async getJyr() {
      commonApi.executeAMS(
          "sdx.query.IJyrService",
          "cxjyr",
          {zrr: dayjs().format("YYYYMMDD"), ts: -1},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        } else {
          this.LastTradingDay = res?.jyr + '' || dayjs().subtract(1, 'day').format('YYYYMMDD') + '';
        }
      })
    },
    getYyList() {
      commonApi.executeAMS(
          "sdx.query.IYybcxService",
          "yybcx",
          {orgcode: null},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.dictArray.YYB = res.records.map(item => ({
          fldm: 'YYB',
          flmc: '营业部',
          cbm: item.orgcode,
          id: item.id + '',
          fid: item.fid,
          ibm: item.id + '',
          note: item.name
        }))
      })
    },
    khjbxxcx() {
      commonApi.executeAMS(
          "sdx.khgl.IKhxxcxService",
          "khjbxxcx",
          {khh: this.khh},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        if (res.records.length <= 0) {
          message.error("该客户[" + this.khh + "]基本信息不存在,请确认客户号！");
        }
        this.khjbxx = res.records[0] || [];
        this.show_cpyxq_syts = this.khjbxx.cpyxqsyts;//测评有效期_剩余天数
        this.show_tzzpdyxq_syts = this.khjbxx.tzzpdyxqsyts;
      }).finally(() => {
      })
    },

    onSearch() {
      if (this.khh.length > 0) {
        let khh = encrypt(this.khh);
        this.$router.push({
          path: "/khqj",
          query: {khh: khh}
        }).then(() => {
          // 重新获取数据
          state.curMenuKey = ["khsdxgk"];
          this.khjbxxcx();
        });
      }
    }
  },
  watch:{
    isZytzz(){
      return this.show_tzzpdyxq_syts?true:false;
    },
  },
  created() {
    let params = this.$route.query.khh;
    this.khh = decrypt(params);
  },

  async  mounted() {//ready?
    await this.getJyr();
    this.getSjzd();
    this.khjbxxcx();
    console.log(this.$route.fullPath)
    if(this.$route.fullPath.indexOf("/khqj/khsdxgk")<0){//页面刷新时，默认显示第一个菜单
      this.changeMenu(menus[0]);
    }
  }
};
</script>

<style scoped>
.khqj_header {
  box-shadow: 0px 2px 6px 0px #f0f2f5;
  background: white;
  height: 50px;
}

.header_row {
  height: 50px;
  align-items: center;
}

.khqj_search_input {
  /*background: #F2F2F2;
  border-radius: 16px;*/
  height: 30px;
}

.khqj_main {
  display: flex;
  height: calc(100% - 50px);
  z-index: 666;
  background-image: url('../../assets/images/khsdxgl_bg_pic.png');
  background-size: cover;
  background-position: left bottom;
  background-repeat: no-repeat;
}

.main_left_sider {
  width: 330px;
  padding: 10px 5px 10px 10px;
}

.main_right_content {
  width: calc(100% - 330px);
  padding: 10px 10px 10px 5px;
  overflow-y: scroll;
  height: 100%;
}

.profile-card {
  height: calc(100% - 50px);
  position: relative;
  background: white;
  border-radius: 8px;
  padding: 48px 16px 16px; /* 上方留出头像的空间 */
  margin-top: 50px; /* 为头像预留空间 */
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.avatar-wrapper {
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
  top: 0;
}

.avatar {
  background: #ffd6d6;
}

.header-content {
  line-height: 30px;
  //height: 58%;
  max-height: 350px;
}

.company-name {
  text-align: center;
  font-size: 16px;
  color: #333333;
}

.company-code {
  text-align: center;
  font-size: 14px;
  color: #888888;
}

.contact-info {
  padding-top: 10px;
  line-height: 35px;
}

.contact-icon {
  color: #BF935F;
  padding-right: 10px;
  text-align: right;
}

.status-cards {
  display: flex;
  gap: 16px;
  margin: 16px 0;
  flex-direction: column;
}

.status-card {
  background: #FFFFFF;
  box-shadow: 1px 2px 6px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 15px 10px 10px 10px;
}

.status-card :deep(.ant-tag) {
  white-space: normal;
  line-height: 18px;
}

.status-card .title {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  line-height: 18px;
}

.status-card .days {
  font-weight: 400;
  font-size: 28px;
  color: #333333;
  line-height: 32px;
  text-align: right;
  width: 62px;
  display: inline-block;
}

.status-card .unit {
  font-weight: 400;
  font-size: 14px;
  color: #888888;
  line-height: 14px;
  padding-left: 10px;
}

.menu-wrapper {
  display: flex;
  flex-grow: 1;
  height: 42%;
}

.operation-menu {
  overflow-y: auto;
  display: inline-block;
}

:deep(.ant-menu-light.ant-menu-root.ant-menu-inline) {
  border-inline-end: none;
}

:deep(.ant-menu-light .ant-menu-item-selected) {
  color: #BF935F;
  background-color: #FBF4EC;
}


</style>
