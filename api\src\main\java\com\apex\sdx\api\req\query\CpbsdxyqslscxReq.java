package com.apex.sdx.api.req.query;

import com.apex.sdx.api.req.common.Request;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025/6/6 16:47
 * @Description: TODO
 */
@Getter
@Setter
public class CpbsdxyqslscxReq extends Request {

    @LiveProperty(note = "开始日期", index = 1)
    private String ksrq;

    @LiveProperty(note = "结束日期", index = 2)
    private String jsrq;

    @LiveProperty(note = "产品代码", index = 3)
    private String cpdm;

    @LiveProperty(note = "客户号", index = 4)
    private String khh;
}
