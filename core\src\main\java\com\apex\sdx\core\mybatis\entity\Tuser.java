package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @TableName tuser
 */
@TableName(value ="tuser", schema = "livebos")
@Data
public class Tuser implements Serializable {
    private Long id;

    private String userid;

    private String password;

    private String name;

    private Long grade;

    private Date lastlogin;

    private Long logins;

    private Date chgpwdtime;

    private Long chgpwdlimit;

    private Long status;

    private String iplimit;

    private String certno;

    private Integer orgid;

    private Date locktime;

    private Integer retrycount;

    private Date lasttrytime;

    private Integer userattribute;

    private byte[] photo;

    private static final long serialVersionUID = 1L;
}