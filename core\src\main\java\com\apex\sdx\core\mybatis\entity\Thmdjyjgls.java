package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName thmdjyjgls
 */
@TableName(value ="thmdjyjgls")
@Data
public class Thmdjyjgls implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private Long jymdlx;

    /**
     * 
     */
    private Long jydx;

    /**
     * 
     */
    private String khh;

    /**
     * 
     */
    private Long zjlb;

    /**
     * 
     */
    private String zjbh;

    /**
     * 
     */
    private String khxm;

    /**
     * 
     */
    private String khqc;

    /**
     * 
     */
    private Long khlb;

    /**
     * 
     */
    private Long xb;

    /**
     * 
     */
    private Long gj;

    /**
     * 
     */
    private Integer csrq;

    /**
     * 
     */
    private String mdkhxm;

    /**
     * 
     */
    private Long mdzjlb;

    /**
     * 
     */
    private String mdzjbh;

    /**
     * 
     */
    private Long mdxb;

    /**
     * 
     */
    private Long mdgj;

    /**
     * 
     */
    private Integer mdcsrq;

    /**
     * 
     */
    private String mdwbid;

    /**
     * 
     */
    private Long hmdid;

    /**
     * 
     */
    private Long jymdjg;

    /**
     * 
     */
    private String ywdm;

    /**
     * 
     */
    private Integer rq;

    /**
     * 
     */
    private String fztdxid;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Thmdjyjgls other = (Thmdjyjgls) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getJymdlx() == null ? other.getJymdlx() == null : this.getJymdlx().equals(other.getJymdlx()))
            && (this.getJydx() == null ? other.getJydx() == null : this.getJydx().equals(other.getJydx()))
            && (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
            && (this.getZjlb() == null ? other.getZjlb() == null : this.getZjlb().equals(other.getZjlb()))
            && (this.getZjbh() == null ? other.getZjbh() == null : this.getZjbh().equals(other.getZjbh()))
            && (this.getKhxm() == null ? other.getKhxm() == null : this.getKhxm().equals(other.getKhxm()))
            && (this.getKhqc() == null ? other.getKhqc() == null : this.getKhqc().equals(other.getKhqc()))
            && (this.getKhlb() == null ? other.getKhlb() == null : this.getKhlb().equals(other.getKhlb()))
            && (this.getXb() == null ? other.getXb() == null : this.getXb().equals(other.getXb()))
            && (this.getGj() == null ? other.getGj() == null : this.getGj().equals(other.getGj()))
            && (this.getCsrq() == null ? other.getCsrq() == null : this.getCsrq().equals(other.getCsrq()))
            && (this.getMdkhxm() == null ? other.getMdkhxm() == null : this.getMdkhxm().equals(other.getMdkhxm()))
            && (this.getMdzjlb() == null ? other.getMdzjlb() == null : this.getMdzjlb().equals(other.getMdzjlb()))
            && (this.getMdzjbh() == null ? other.getMdzjbh() == null : this.getMdzjbh().equals(other.getMdzjbh()))
            && (this.getMdxb() == null ? other.getMdxb() == null : this.getMdxb().equals(other.getMdxb()))
            && (this.getMdgj() == null ? other.getMdgj() == null : this.getMdgj().equals(other.getMdgj()))
            && (this.getMdcsrq() == null ? other.getMdcsrq() == null : this.getMdcsrq().equals(other.getMdcsrq()))
            && (this.getMdwbid() == null ? other.getMdwbid() == null : this.getMdwbid().equals(other.getMdwbid()))
            && (this.getHmdid() == null ? other.getHmdid() == null : this.getHmdid().equals(other.getHmdid()))
            && (this.getJymdjg() == null ? other.getJymdjg() == null : this.getJymdjg().equals(other.getJymdjg()))
            && (this.getYwdm() == null ? other.getYwdm() == null : this.getYwdm().equals(other.getYwdm()))
            && (this.getRq() == null ? other.getRq() == null : this.getRq().equals(other.getRq()))
            && (this.getFztdxid() == null ? other.getFztdxid() == null : this.getFztdxid().equals(other.getFztdxid()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getJymdlx() == null) ? 0 : getJymdlx().hashCode());
        result = prime * result + ((getJydx() == null) ? 0 : getJydx().hashCode());
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getZjlb() == null) ? 0 : getZjlb().hashCode());
        result = prime * result + ((getZjbh() == null) ? 0 : getZjbh().hashCode());
        result = prime * result + ((getKhxm() == null) ? 0 : getKhxm().hashCode());
        result = prime * result + ((getKhqc() == null) ? 0 : getKhqc().hashCode());
        result = prime * result + ((getKhlb() == null) ? 0 : getKhlb().hashCode());
        result = prime * result + ((getXb() == null) ? 0 : getXb().hashCode());
        result = prime * result + ((getGj() == null) ? 0 : getGj().hashCode());
        result = prime * result + ((getCsrq() == null) ? 0 : getCsrq().hashCode());
        result = prime * result + ((getMdkhxm() == null) ? 0 : getMdkhxm().hashCode());
        result = prime * result + ((getMdzjlb() == null) ? 0 : getMdzjlb().hashCode());
        result = prime * result + ((getMdzjbh() == null) ? 0 : getMdzjbh().hashCode());
        result = prime * result + ((getMdxb() == null) ? 0 : getMdxb().hashCode());
        result = prime * result + ((getMdgj() == null) ? 0 : getMdgj().hashCode());
        result = prime * result + ((getMdcsrq() == null) ? 0 : getMdcsrq().hashCode());
        result = prime * result + ((getMdwbid() == null) ? 0 : getMdwbid().hashCode());
        result = prime * result + ((getHmdid() == null) ? 0 : getHmdid().hashCode());
        result = prime * result + ((getJymdjg() == null) ? 0 : getJymdjg().hashCode());
        result = prime * result + ((getYwdm() == null) ? 0 : getYwdm().hashCode());
        result = prime * result + ((getRq() == null) ? 0 : getRq().hashCode());
        result = prime * result + ((getFztdxid() == null) ? 0 : getFztdxid().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", jymdlx=").append(jymdlx);
        sb.append(", jydx=").append(jydx);
        sb.append(", khh=").append(khh);
        sb.append(", zjlb=").append(zjlb);
        sb.append(", zjbh=").append(zjbh);
        sb.append(", khxm=").append(khxm);
        sb.append(", khqc=").append(khqc);
        sb.append(", khlb=").append(khlb);
        sb.append(", xb=").append(xb);
        sb.append(", gj=").append(gj);
        sb.append(", csrq=").append(csrq);
        sb.append(", mdkhxm=").append(mdkhxm);
        sb.append(", mdzjlb=").append(mdzjlb);
        sb.append(", mdzjbh=").append(mdzjbh);
        sb.append(", mdxb=").append(mdxb);
        sb.append(", mdgj=").append(mdgj);
        sb.append(", mdcsrq=").append(mdcsrq);
        sb.append(", mdwbid=").append(mdwbid);
        sb.append(", hmdid=").append(hmdid);
        sb.append(", jymdjg=").append(jymdjg);
        sb.append(", ywdm=").append(ywdm);
        sb.append(", rq=").append(rq);
        sb.append(", fztdxid=").append(fztdxid);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}