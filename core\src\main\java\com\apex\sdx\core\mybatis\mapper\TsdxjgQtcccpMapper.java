package com.apex.sdx.core.mybatis.mapper;

import com.apex.sdx.api.vo.compute.QtcccptjVo;
import com.apex.sdx.api.vo.query.CpccsdxVo;
import com.apex.sdx.core.mybatis.entity.TsdxjgQtcccp;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tsdxjg_qtcccp】的数据库操作Mapper
 * @createDate 2025-06-11 18:35:15
 * @Entity com.apex.sdx.core.mybatis.entity.TsdxjgQtcccp
 */
public interface TsdxjgQtcccpMapper extends BaseMapper<TsdxjgQtcccp> {

    @Select({"<script> " +
            "select b.cpmc,a.* " +
            "from sdx.tsdxjg_qtcccp a left join sdx.tcp_cpxx b on a.cpdm = b.cpdm " +
            "where a.KHH = #{khh} and a.SJLY = #{sjlx} " +
            " and a.rq = #{rq} " +
            "   <if test='onlysdx == true'>" +
            "       and (a.sdxjg = -1 or a.sdxjg = 0) " +
            "   </if>" +
            "order by a.rq desc" +
            "</script>"})
    List<CpccsdxVo> selectByKhhSjlxAndSdxjg(@Param("khh") String khh, @Param("sjlx") Integer sjlx, @Param("onlysdx") boolean onlysdx, @Param("rq") String rq);

    List<QtcccptjVo> compute(@Param("khh") String khh, @Param("ywxt") Integer ywxt, @Param("rq") String rq);
}




