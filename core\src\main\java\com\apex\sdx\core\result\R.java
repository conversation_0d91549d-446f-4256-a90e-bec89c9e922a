package com.apex.sdx.core.result;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@ApiModel("返回结果实体类")
public class R {

    @ApiModelProperty("返回码")
    private Integer code;
    @ApiModelProperty("返回信息")
    private String note;
    @ApiModelProperty("数据")
    private Map<String, Object> data = new HashMap<>();

    /**
     * 构造函数私有化
     */
    private R(){}

    /**
     * 返回成功结果
     * @return
     */
    public static R ok(){
        R r = new R();
        r.setCode(ResponseEnum.SUCCESS.getCode());
        r.setNote(ResponseEnum.SUCCESS.getNote());
        return r;
    }

    /**
     * 返回失败结果
     * @return
     */
    public static R error(){
        R r = new R();
        r.setCode(ResponseEnum.ERROR.getCode());
        r.setNote(ResponseEnum.ERROR.getNote());
        return r;
    }

    /**
     * 设置特定的结果
     * @param responseEnum
     * @return
     */
    public static R setResult(ResponseEnum responseEnum){
        R r = new R();
        r.setCode(responseEnum.getCode());
        r.setNote(responseEnum.getNote());
        return r;
    }
    
    public static R setResult(JSONObject result){
        R r = new R();
        r.setCode(result.getInteger("code"));
        r.setNote(result.getString("note"));
        return r;
    }

    public R data(String key, Object value){
        this.data.put(key, value);
        return this;
    }

    public R data(Map<String, Object> map){
        this.setData(map);
        return this;
    }

    /**
     * 设置特定的响应消息
     * @param note
     * @return
     */
    public R note(String note){
        this.setNote(note);
        return this;
    }


    /**
     * 设置特定的响应码
     * @param code
     * @return
     */
    public R code(Integer code){
        this.setCode(code);
        return this;
    }

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public Map<String, Object> getData() {
		return data;
	}

	public void setData(Map<String, Object> data) {
		this.data = data;
	}

	@Override
	public String toString() {
		return JSONObject.toJSONString(this);
	}
	
	
}
