<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.TsdxjgQtdytgMapper">

    <resultMap id="BaseResultMap" type="com.apex.sdx.core.mybatis.entity.TsdxjgQtdytg">
            <id property="rq" column="RQ" jdbcType="DECIMAL"/>
            <id property="khh" column="KHH" jdbcType="VARCHAR"/>
            <id property="cpdm" column="CPDM" jdbcType="DECIMAL"/>
            <result property="yyb" column="YYB" jdbcType="DECIMAL"/>
            <result property="ddqd" column="DDQD" jdbcType="DECIMAL"/>
            <result property="ddrq" column="DDRQ" jdbcType="DECIMAL"/>
            <result property="ddzt" column="DDZT" jdbcType="DECIMAL"/>
            <result property="cpmc" column="CPMC" jdbcType="VARCHAR"/>
            <result property="khfxdj" column="KHFXDJ" jdbcType="DECIMAL"/>
            <result property="cpfxdj" column="CPFXDJ" jdbcType="DECIMAL"/>
            <result property="fxdjsdx" column="FXDJSDX" jdbcType="DECIMAL"/>
            <result property="sdxjg" column="SDXJG" jdbcType="DECIMAL"/>
            <result property="sjly" column="SJLY" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        RQ,KHH,CPDM,
        YYB,DDQD,DDRQ,
        DDZT,CPMC,KHFXDJ,
        CPFXDJ,FXDJSDX,SDXJG,
        SJLY
    </sql>
</mapper>
