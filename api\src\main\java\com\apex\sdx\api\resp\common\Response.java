package com.apex.sdx.api.resp.common;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * 通用服务响应对象
 *
 * <AUTHOR>
 */

@Getter
@Setter
@SuperBuilder
public class Response extends R {

    public Response() {
        super(-1);
    }

    public Response(int code) {
        super(code);
    }

    public Response(int code, String note) {
        super(code, note);
    }

//    /**
//     * 错误编码
//     */
//    @LiveProperty(note = "错误编码", index = 1001)
//    private String errorCode;

    /**
     * 处理状态
     */
    @LiveProperty(note = "处理状态", index = 1002)
    private int clzt;

    /**
     * 流水号
     */
    @LiveProperty(note = "流水号", index = 1003)
    private Long lsh;


    public Response code(int code) {
        this.setCode(code);
        return this;
    }
    public Response note(String note) {
        this.setNote(note);
        return this;
    }
    /**
     * 构造错误信息
     *
     * @param code 返回编码
     * @param note 返回说明
     * @return
     */
    public static String buildErrorMsg(int code, String note) {
        return String.format("[code=%s]%s", code, note);
    }

    /**
     * 构造检查通过返回对象
     *
     * @return
     */
    public static Response checkSuccess() {
        return Response.builder().code(1).note("业务检查通过").build();
    }

    public static Response ok(){
        return Response.builder()
                .code(ResponseEnum.SUCCESS.getCode())
                .note(ResponseEnum.SUCCESS.getNote())
                .clzt(8)
                .build();
    }

    public static Response error(){
        return Response.builder()
                .code(ResponseEnum.ERROR_1.getCode())
                .note(ResponseEnum.ERROR_1.getNote())
                .clzt(9)
                .build();
    }
}
