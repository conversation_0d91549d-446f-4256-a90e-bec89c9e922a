package com.apex.sdx.api.req.query;

import com.apex.sdx.api.req.common.Request;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @Date 2025-01-16
 * @Description:
 */
@Setter
@Getter
@Validated
public class SjzdReq extends Request {

    @LiveProperty(note = "分类代码", index = 1)
    private String fldm;

    @LiveProperty(note = "营业部", index = 2)
    private Integer yyb;

}
