package com.apex.sdx.api.vo.query;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-03-04
 * @Description:
 */
@Setter
@Getter
public class SdxsjDescDataVo {


    @LiveProperty(note = "要素代码", index = 11)
    private String ysdm;

    @LiveProperty(note = "要素名称", index = 12)
    private String ysmc;

    @LiveProperty(note = "要素值", index = 13)
    private String value;

    @LiveProperty(note = "要素列表", index = 14)
    private List<SdxsjDescDataVo> columns;

}
