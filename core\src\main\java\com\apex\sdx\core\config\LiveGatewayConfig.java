package com.apex.sdx.core.config;

import com.apexsoft.client.GatewayClient;
import com.apexsoft.client.Request;
import com.apexsoft.client.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

/**
 * @Auther yeyilun
 * @Date 2021 12 11
 * @Description:
 */
@Slf4j
@Configuration
public class LiveGatewayConfig {
	@Value("${esb.ip:}")
	private String ip;

    @Value("${esb.user:}")
    private String user;

    @Value("${esb.pwd:}")
    private String pwd;

    @Value("${esb.port:}")
    private String port;

    @Bean
    public GatewayClient gatewayClient() {
        GatewayClient client = null;
        try {
            client = GatewayClient.Instance.singleton(GatewayClient.ProtocolVersion.ESB);
            client.destory();
            client.ignoreCertVerify();
        	client.addHTTPSServer(ip, Integer.parseInt(port), null, null);
            client.setAuth(user, pwd);

            InvocationHandler handler = new GatewayClientProxy(client);
            Object bean = Proxy.newProxyInstance(GatewayClient.class.getClassLoader(),
                    new Class[]{GatewayClient.class},
                    handler);
            log.info("蜂巢初始化成功");
            return (GatewayClient)bean;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("蜂巢认证失败：" + e.getMessage());
            //如果初始化异常，销毁对象，回收资源
            if (client != null) {
                client.destory();
            }
        }
        return client;
    }

    class GatewayClientProxy implements InvocationHandler {

        private final GatewayClient gatewayClient;
        public GatewayClientProxy(GatewayClient gatewayClient) {
            this.gatewayClient = gatewayClient;
        }
        @Override
        public Object invoke(Object o, Method method, Object[] args) throws Throwable {
            Long t = System.currentTimeMillis();
            if("service".equals(method.getName())) {
                try {
                    Request req = (Request)args[0];
                    log.info("esb["+req.getServiceId()+"]请求参数：" + req.toJSON());
                    Object result = method.invoke(gatewayClient, args);
                    Response res = (Response) result;
                    log.info("esb["+req.getServiceId()+"]请求耗时：" + (System.currentTimeMillis() - t)
                            + "，请求结果：" + res.toJSONString());
                    return res;
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("esb请求异常", e);
                    Response response = new Response();
                    response.put("code", -1);
                    response.put("note", "esb请求异常，" + e.getMessage());
                    return response;
                }
            } else {
                return method.invoke(gatewayClient, args);
            }
        }
    }
}
