<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.TicZbcsMapper">

    <resultMap id="BaseResultMap" type="com.apex.sdx.core.mybatis.entity.TicZbcs">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="fid" column="FID" jdbcType="DECIMAL"/>
            <result property="grade" column="Grade" jdbcType="DECIMAL"/>
            <result property="type" column="Type" jdbcType="DECIMAL"/>
            <result property="name" column="Name" jdbcType="VARCHAR"/>
            <result property="fdncode" column="FDNCode" jdbcType="VARCHAR"/>
            <result property="idxCode" column="IDX_CODE" jdbcType="VARCHAR"/>
            <result property="idxCodeSrc" column="IDX_CODE_SRC" jdbcType="VARCHAR"/>
            <result property="idxNm" column="IDX_NM" jdbcType="VARCHAR"/>
            <result property="idxDisplayName" column="IDX_DISPLAY_NAME" jdbcType="VARCHAR"/>
            <result property="idxCl" column="IDX_CL" jdbcType="DECIMAL"/>
            <result property="idxGrd" column="IDX_GRD" jdbcType="DECIMAL"/>
            <result property="status" column="Status" jdbcType="DECIMAL"/>
            <result property="descr" column="DESCR" jdbcType="VARCHAR"/>
            <result property="prcs" column="PRCS" jdbcType="DECIMAL"/>
            <result property="department" column="Department" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,FID,Grade,
        Type,Name,FDNCode,
        IDX_CODE,IDX_CODE_SRC,IDX_NM,
        IDX_DISPLAY_NAME,IDX_CL,IDX_GRD,
        Status,DESCR,PRCS,
        Department
    </sql>
</mapper>
