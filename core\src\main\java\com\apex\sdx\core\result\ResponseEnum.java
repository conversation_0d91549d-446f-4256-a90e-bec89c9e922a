package com.apex.sdx.core.result;

public enum ResponseEnum {

    SUCCESS(1, "操作成功"),
    ERROR(-1, "服务器内部错误"),
	ESB_ERROR(-2, "esb请求异常"),
	ERROR_BUSINESS(-3, "业务请求异常");

    private ResponseEnum(Integer code, String note) {
		this.code = code;
		this.note = note;
	}
    
	private Integer code;//状态码
    private String note;//消息
    
	public Integer getCode() {
		return code;
	}

	public String getNote() {
		return note;
	}
    
}
