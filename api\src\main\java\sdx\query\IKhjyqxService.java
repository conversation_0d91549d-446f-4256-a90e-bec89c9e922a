package sdx.query;

import com.apex.sdx.api.req.khgl.KhjyqxcxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.vo.khgl.KhjyqxVo;
import com.apexsoft.LiveMethod;

/**
 * <AUTHOR>
 * @Date 2025-01-22
 * @Description:
 */
public interface IKhjyqxService {
    @LiveMethod(paramAsRequestBody = true, note = "查询客户交易权限")
    QueryPageResponse<KhjyqxVo> khjyqxcx(KhjyqxcxReq req) throws Exception;
}
