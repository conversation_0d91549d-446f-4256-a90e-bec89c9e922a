<template>
  <div style="height: 100%; width: 100%">
    <div :id="chartsId" ref="chart" style="margin:10px auto; width: 100%; height: 100%;">

    </div>
  </div>
</template>
<script>

let barCharts = [];
export default {
  name: "pieChart",
  props: ["title", "self_pie_data"],//从别的页面传递进来
  data() {
    return {
      self_pie_data2:{
        "subtext":'我个性 60%',
        "data": [
          { value: 1233, name: '我个性适当' },
          { value: 2233, name: '我个性不适当' },
        ],
      }
    };
  },
  methods: {
    initBarChart() {
      let el = document.getElementById(this.chartsId);
      barCharts[this.chartsId] = this.$echarts.init(el);
      this.setOption();
    },
    setOption() {
      let _this = this;
      let option = this.getOption();
      //这里塞值个性的数据
      barCharts[this.chartsId].setOption(option);
      setTimeout(() => {
        _this.handleMouseOut();
      }, 1000);

      // 使用ECharts自带的事件系统
      _this.removeListener(); // 移除之前的监听事件，防止重复添加
      barCharts[_this.chartsId].on('mouseover', _this.handleMouseOver);
      barCharts[_this.chartsId].on('mouseout', _this.handleMouseOut);
    },
    removeListener() {
      // 移除图表
      barCharts[this.chartsId].off('mouseover'); // 先移除旧监听
      barCharts[this.chartsId].off('mouseout'); // 先移除旧监听
    },
    cencel() {
      // 取消所有高亮
      barCharts[this.chartsId].dispatchAction({
        type: 'downplay',
        seriesIndex: 0
      });
    },
    handleMouseOver(params) {
      this.cencel();
      // 高亮当前悬停的项
      if (params.dataIndex !== undefined) {
        barCharts[this.chartsId].dispatchAction({
          type: 'highlight',
          seriesIndex: params.seriesIndex,
          dataIndex: params.dataIndex
        });
      }
    },
    handleMouseOut() {
      this.cencel();
      // 鼠标移出时恢复默认高亮状态（如需要）
      // 这里可以根据需求调整，例如重新高亮第一项
      barCharts[this.chartsId].dispatchAction({
        type: 'highlight',
        seriesIndex: 0,//要操作的系列（series）的索引，默认单系列图表是 0，多系列时按顺序递增（0, 1, 2...）
        dataIndex: 1  //表示要操作的数据的索引
      });
    },
    getOption() {
      let _this = this;
      let option = {
        /*title: {
          text: '适当',
          x: 'center',
          y: '50',
          textStyle: {
            fontSize: 14
          },
          subtext: this.self_pie_data.subtext||"0%",//'90 (80%)',
          subtextStyle: {
            fontWeight: 'bold',
            fontSize: 18,
            color:' #35B5FF',
          }
        },*/
        /*tooltip: {
          trigger: 'item'
        },*/
        legend: {
          bottom: '5%',
          left: 'center',
          //icon: 'circle', // 设置图例为小圆点
          itemWidth: 10,  // 控制圆点宽度
          itemHeight: 10, // 控制圆点高度
          itemGap: 25,   // 图例项之间的间隔
        },
        color:['#36A6FC','#FFC730'],
        series: [
          {
            //minAngle: 5,//最小的扇区角度（0 ~ 360），用于防止某个值过小导致扇区太小影响交互。
            clockwise: false,//是否顺时针排列，默认为 true，可以设为 false。设置为 true 时，数据顺序与饼图扇区（数据项）是相反的。
            //关闭图例悬浮行为
            legendHoverLink:false,
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['50%', '38%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                formatter: function (params) {
                  return '{c|' + params.name + ' }' + '\n\n{a|' + params.value +' ('+ _this.self_pie_data.datazb[params.dataIndex]+ ') }' ;
                },
                rich: {
                  a: {
                    fontSize: '18px',
                    color: '#35B5FF',
                    fontWeight: 'bold',
                  },
                  c: {
                    fontSize: '14px',
                    color: '#333333',
                    fontWeight: '400',
                  },
                },
              }
            },
            labelLine: {
              show: false
            },
            /*data: [
              { value: 1048, name: '适当' },
              { value: 735, name: '不适当' },
            ]*/
            data: this.self_pie_data.data||[{value:"0","name":"适当"},{value:"0","name":"不适当"},]
          }
        ],
        grid: {

          containLabel: true,
        },
      };
      return option;
    }
  },
  watch: {
    self_pie_data(newValue, oldValue){//监听送过来的数据有无变化
      if(newValue !== oldValue) {
        this.setOption();//有变化的话 更新数据 重新设置饼图
      }
    }
  },
  computed: {
    chartsId() {
      return "id_" + this.title;
    },
  },
  mounted() {
    /*if (this.$props.dataset && this.$props.dataset.length > 0) {
      this.initBarChart();
    }*/
    this.initBarChart();
  },
  unmounted() {
    this.removeListener();
    if (barCharts[this.chartsId]) {
      barCharts[this.chartsId].dispose();
      delete barCharts[this.chartsId];
    }
  },
};
</script>
<style scoped>

</style>