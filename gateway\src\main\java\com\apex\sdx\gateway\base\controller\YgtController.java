package com.apex.sdx.gateway.base.controller;

import com.alibaba.fastjson.JSONObject;
import com.apex.ams.client.dynamic.ServerReflectionLoader;
import com.apex.sdx.gateway.base.dao.LivebosGrpcDao;
import com.apex.sdx.gateway.base.model.CommonResponse;
import com.apex.sdx.gateway.base.service.LbProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


@RequestMapping("/test")
@Controller
@Slf4j
public class YgtController {
    @Autowired
    LivebosGrpcDao livebosGrpcDao;
    @Autowired
    LbProjectService lbProjectService;

    @RequestMapping("/livebosgrpc")
    @ResponseBody
    public JSONObject queryUserProject() {
        //JSONObject object = JSONObject.parseObject(body);
        try{
            CommonResponse response = lbProjectService.getProjectMenus(0,"SYSTEM");
            System.out.println(response.toString());
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }

        JSONObject result = new JSONObject();
        result.put("code",1);
        result.put("note","测试成功");
        return result;
    }


    @RequestMapping("/clearServiceCache")
    @ResponseBody
    public void clearServiceCache() {
        //ServerReflectionLoader.getInstance().refresh(namespace, serviceId); //这个是刷新单个服务
        ServerReflectionLoader.getInstance().clear(); //这个是全刷
    }
}
