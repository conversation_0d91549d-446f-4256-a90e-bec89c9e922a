package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;

/**
 * 
 * @TableName t_ods_ygt_cif_tgmsfcxsq
 */
@TableName(value ="t_ods_ygt_cif_tgmsfcxsq", schema = "ods")
@Data
public class TOdsYgtCifTgmsfcxsq implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 
     */
    private Integer sqrq;

    /**
     * 
     */
    private String sqsj;

    /**
     * 
     */
    private String zjbh;

    /**
     * 
     */
    private String sqxm;

    /**
     * 
     */
    private Long czr;

    /**
     * 
     */
    private Long czlb;

    /**
     * 
     */
    private Long yyb;

    /**
     * 
     */
    private Long cxlx;

    /**
     * 
     */
    private String bdlsh;

    /**
     * 
     */
    private Long zjlb;

    /**
     * 
     */
    private Integer csrq;

    /**
     * 
     */
    private String xm;

    /**
     * 
     */
    private Long nation;

    /**
     * 
     */
    private Long xb;

    /**
     * 
     */
    private String filepath;

    /**
     * 
     */
    private String zjbhhcjg;

    /**
     * 
     */
    private String khzp;

    /**
     * 
     */
    private String xmhcjg;

    /**
     * 
     */
    private Long clzt;

    /**
     * 
     */
    private Long cljg;

    /**
     * 
     */
    private String jgdm;

    /**
     * 
     */
    private String jgsm;

    /**
     * 
     */
    private String clsj;

    /**
     * 
     */
    private Long jklx;

    /**
     * 
     */
    private Integer sbrq;

    /**
     * 
     */
    private String sbsj;

    /**
     * 
     */
    private Integer hbrq;

    /**
     * 
     */
    private String hbsj;

    /**
     * 
     */
    private BigDecimal bdfz;

    /**
     * 
     */
    private Integer zjqsrq;

    /**
     * 
     */
    private Integer zjjzrq;

    /**
     * 
     */
    private String hzcxjg;

    /**
     * 
     */
    private Long gjdm;

    /**
     * 
     */
    private String tdbh;

    /**
     * 
     */
    private String rxbdjg;

    /**
     * 
     */
    private byte[] photo;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TOdsYgtCifTgmsfcxsq other = (TOdsYgtCifTgmsfcxsq) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSqrq() == null ? other.getSqrq() == null : this.getSqrq().equals(other.getSqrq()))
            && (this.getSqsj() == null ? other.getSqsj() == null : this.getSqsj().equals(other.getSqsj()))
            && (this.getZjbh() == null ? other.getZjbh() == null : this.getZjbh().equals(other.getZjbh()))
            && (this.getSqxm() == null ? other.getSqxm() == null : this.getSqxm().equals(other.getSqxm()))
            && (this.getCzr() == null ? other.getCzr() == null : this.getCzr().equals(other.getCzr()))
            && (this.getCzlb() == null ? other.getCzlb() == null : this.getCzlb().equals(other.getCzlb()))
            && (this.getYyb() == null ? other.getYyb() == null : this.getYyb().equals(other.getYyb()))
            && (this.getCxlx() == null ? other.getCxlx() == null : this.getCxlx().equals(other.getCxlx()))
            && (this.getBdlsh() == null ? other.getBdlsh() == null : this.getBdlsh().equals(other.getBdlsh()))
            && (this.getZjlb() == null ? other.getZjlb() == null : this.getZjlb().equals(other.getZjlb()))
            && (this.getCsrq() == null ? other.getCsrq() == null : this.getCsrq().equals(other.getCsrq()))
            && (this.getXm() == null ? other.getXm() == null : this.getXm().equals(other.getXm()))
            && (this.getNation() == null ? other.getNation() == null : this.getNation().equals(other.getNation()))
            && (this.getXb() == null ? other.getXb() == null : this.getXb().equals(other.getXb()))
            && (this.getFilepath() == null ? other.getFilepath() == null : this.getFilepath().equals(other.getFilepath()))
            && (this.getZjbhhcjg() == null ? other.getZjbhhcjg() == null : this.getZjbhhcjg().equals(other.getZjbhhcjg()))
            && (this.getKhzp() == null ? other.getKhzp() == null : this.getKhzp().equals(other.getKhzp()))
            && (this.getXmhcjg() == null ? other.getXmhcjg() == null : this.getXmhcjg().equals(other.getXmhcjg()))
            && (this.getClzt() == null ? other.getClzt() == null : this.getClzt().equals(other.getClzt()))
            && (this.getCljg() == null ? other.getCljg() == null : this.getCljg().equals(other.getCljg()))
            && (this.getJgdm() == null ? other.getJgdm() == null : this.getJgdm().equals(other.getJgdm()))
            && (this.getJgsm() == null ? other.getJgsm() == null : this.getJgsm().equals(other.getJgsm()))
            && (this.getClsj() == null ? other.getClsj() == null : this.getClsj().equals(other.getClsj()))
            && (this.getJklx() == null ? other.getJklx() == null : this.getJklx().equals(other.getJklx()))
            && (this.getSbrq() == null ? other.getSbrq() == null : this.getSbrq().equals(other.getSbrq()))
            && (this.getSbsj() == null ? other.getSbsj() == null : this.getSbsj().equals(other.getSbsj()))
            && (this.getHbrq() == null ? other.getHbrq() == null : this.getHbrq().equals(other.getHbrq()))
            && (this.getHbsj() == null ? other.getHbsj() == null : this.getHbsj().equals(other.getHbsj()))
            && (this.getBdfz() == null ? other.getBdfz() == null : this.getBdfz().equals(other.getBdfz()))
            && (this.getZjqsrq() == null ? other.getZjqsrq() == null : this.getZjqsrq().equals(other.getZjqsrq()))
            && (this.getZjjzrq() == null ? other.getZjjzrq() == null : this.getZjjzrq().equals(other.getZjjzrq()))
            && (this.getHzcxjg() == null ? other.getHzcxjg() == null : this.getHzcxjg().equals(other.getHzcxjg()))
            && (this.getGjdm() == null ? other.getGjdm() == null : this.getGjdm().equals(other.getGjdm()))
            && (this.getTdbh() == null ? other.getTdbh() == null : this.getTdbh().equals(other.getTdbh()))
            && (this.getRxbdjg() == null ? other.getRxbdjg() == null : this.getRxbdjg().equals(other.getRxbdjg()))
            && (Arrays.equals(this.getPhoto(), other.getPhoto()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSqrq() == null) ? 0 : getSqrq().hashCode());
        result = prime * result + ((getSqsj() == null) ? 0 : getSqsj().hashCode());
        result = prime * result + ((getZjbh() == null) ? 0 : getZjbh().hashCode());
        result = prime * result + ((getSqxm() == null) ? 0 : getSqxm().hashCode());
        result = prime * result + ((getCzr() == null) ? 0 : getCzr().hashCode());
        result = prime * result + ((getCzlb() == null) ? 0 : getCzlb().hashCode());
        result = prime * result + ((getYyb() == null) ? 0 : getYyb().hashCode());
        result = prime * result + ((getCxlx() == null) ? 0 : getCxlx().hashCode());
        result = prime * result + ((getBdlsh() == null) ? 0 : getBdlsh().hashCode());
        result = prime * result + ((getZjlb() == null) ? 0 : getZjlb().hashCode());
        result = prime * result + ((getCsrq() == null) ? 0 : getCsrq().hashCode());
        result = prime * result + ((getXm() == null) ? 0 : getXm().hashCode());
        result = prime * result + ((getNation() == null) ? 0 : getNation().hashCode());
        result = prime * result + ((getXb() == null) ? 0 : getXb().hashCode());
        result = prime * result + ((getFilepath() == null) ? 0 : getFilepath().hashCode());
        result = prime * result + ((getZjbhhcjg() == null) ? 0 : getZjbhhcjg().hashCode());
        result = prime * result + ((getKhzp() == null) ? 0 : getKhzp().hashCode());
        result = prime * result + ((getXmhcjg() == null) ? 0 : getXmhcjg().hashCode());
        result = prime * result + ((getClzt() == null) ? 0 : getClzt().hashCode());
        result = prime * result + ((getCljg() == null) ? 0 : getCljg().hashCode());
        result = prime * result + ((getJgdm() == null) ? 0 : getJgdm().hashCode());
        result = prime * result + ((getJgsm() == null) ? 0 : getJgsm().hashCode());
        result = prime * result + ((getClsj() == null) ? 0 : getClsj().hashCode());
        result = prime * result + ((getJklx() == null) ? 0 : getJklx().hashCode());
        result = prime * result + ((getSbrq() == null) ? 0 : getSbrq().hashCode());
        result = prime * result + ((getSbsj() == null) ? 0 : getSbsj().hashCode());
        result = prime * result + ((getHbrq() == null) ? 0 : getHbrq().hashCode());
        result = prime * result + ((getHbsj() == null) ? 0 : getHbsj().hashCode());
        result = prime * result + ((getBdfz() == null) ? 0 : getBdfz().hashCode());
        result = prime * result + ((getZjqsrq() == null) ? 0 : getZjqsrq().hashCode());
        result = prime * result + ((getZjjzrq() == null) ? 0 : getZjjzrq().hashCode());
        result = prime * result + ((getHzcxjg() == null) ? 0 : getHzcxjg().hashCode());
        result = prime * result + ((getGjdm() == null) ? 0 : getGjdm().hashCode());
        result = prime * result + ((getTdbh() == null) ? 0 : getTdbh().hashCode());
        result = prime * result + ((getRxbdjg() == null) ? 0 : getRxbdjg().hashCode());
        result = prime * result + (Arrays.hashCode(getPhoto()));
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sqrq=").append(sqrq);
        sb.append(", sqsj=").append(sqsj);
        sb.append(", zjbh=").append(zjbh);
        sb.append(", sqxm=").append(sqxm);
        sb.append(", czr=").append(czr);
        sb.append(", czlb=").append(czlb);
        sb.append(", yyb=").append(yyb);
        sb.append(", cxlx=").append(cxlx);
        sb.append(", bdlsh=").append(bdlsh);
        sb.append(", zjlb=").append(zjlb);
        sb.append(", csrq=").append(csrq);
        sb.append(", xm=").append(xm);
        sb.append(", nation=").append(nation);
        sb.append(", xb=").append(xb);
        sb.append(", filepath=").append(filepath);
        sb.append(", zjbhhcjg=").append(zjbhhcjg);
        sb.append(", khzp=").append(khzp);
        sb.append(", xmhcjg=").append(xmhcjg);
        sb.append(", clzt=").append(clzt);
        sb.append(", cljg=").append(cljg);
        sb.append(", jgdm=").append(jgdm);
        sb.append(", jgsm=").append(jgsm);
        sb.append(", clsj=").append(clsj);
        sb.append(", jklx=").append(jklx);
        sb.append(", sbrq=").append(sbrq);
        sb.append(", sbsj=").append(sbsj);
        sb.append(", hbrq=").append(hbrq);
        sb.append(", hbsj=").append(hbsj);
        sb.append(", bdfz=").append(bdfz);
        sb.append(", zjqsrq=").append(zjqsrq);
        sb.append(", zjjzrq=").append(zjjzrq);
        sb.append(", hzcxjg=").append(hzcxjg);
        sb.append(", gjdm=").append(gjdm);
        sb.append(", tdbh=").append(tdbh);
        sb.append(", rxbdjg=").append(rxbdjg);
        sb.append(", photo=").append(photo);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}