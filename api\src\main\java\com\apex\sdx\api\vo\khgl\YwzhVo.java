package com.apex.sdx.api.vo.khgl;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025/1/23 11:27
 * @Description: TODO
 */
@Setter
@Getter
public class YwzhVo {

    @LiveProperty(note = "集中交易业务账户", index = 11)
    private String ywzh;

    @LiveProperty(note = "集中交易账户状态", index = 12)
    private String zhzt;

    @LiveProperty(note = "集中交易营业部", index = 13)
    private String yyb;

    @LiveProperty(note = "集中交易开户日期", index = 14)
    private Integer khrq;
    
    @LiveProperty(note = "适当性结果", index = 15)
    private Long sdxjg;
}
