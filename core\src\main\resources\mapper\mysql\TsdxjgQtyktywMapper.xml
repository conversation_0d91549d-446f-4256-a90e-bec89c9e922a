<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.TsdxjgQtyktywMapper">

    <resultMap id="BaseResultMap" type="com.apex.sdx.core.mybatis.entity.TsdxjgQtyktyw">
            <id property="khh" column="KHH" jdbcType="VARCHAR"/>
            <id property="rq" column="RQ" jdbcType="DECIMAL"/>
            <id property="cxywlb" column="CXYWLB" jdbcType="DECIMAL"/>
            <result property="khfxdj" column="KHFXDJ" jdbcType="DECIMAL"/>
            <result property="khtzpz" column="KHTZPZ" jdbcType="VARCHAR"/>
            <result property="khtzqx" column="KHTZQX" jdbcType="DECIMAL"/>
            <result property="khyqsy" column="KHYQSY" jdbcType="VARCHAR"/>
            <result property="ywfxdj" column="YWFXDJ" jdbcType="DECIMAL"/>
            <result property="ywtzpz" column="YWTZPZ" jdbcType="DECIMAL"/>
            <result property="ywtzqx" column="YWTZQX" jdbcType="DECIMAL"/>
            <result property="ywyqsy" column="YWYQSY" jdbcType="DECIMAL"/>
            <result property="fxdjsdx" column="FXDJSDX" jdbcType="DECIMAL"/>
            <result property="tzpzsdx" column="TZPZSDX" jdbcType="DECIMAL"/>
            <result property="tzqxsdx" column="TZQXSDX" jdbcType="DECIMAL"/>
            <result property="yqsysdx" column="YQSYSDX" jdbcType="DECIMAL"/>
            <result property="sdxjg" column="SDXJG" jdbcType="DECIMAL"/>
            <result property="yyb" column="YYB" jdbcType="DECIMAL"/>
            <result property="qsbsdxy" column="QSBSDXY" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        KHH,RQ,CXYWLB,
        KHFXDJ,KHTZPZ,KHTZQX,
        KHYQSY,YWFXDJ,YWTZPZ,
        YWTZQX,YWYQSY,FXDJSDX,
        TZPZSDX,TZQXSDX,YQSYSDX,
        SDXJG,YYB,QSBSDXY
    </sql>


    <select id="compute" resultType="com.apex.sdx.api.vo.compute.QtyktywtjVo">
        select
            max(tq.rq)                                                           as rq,
            <if test="khh != null and khh != ''">
                max(tq.khh)                                                      as khh,
            </if>
            count(distinct (KHH))                                                as khsl,
            count(1)                                                             as ktsl,
            sum(case when tq.sdxjg = 1 then 1 else 0 end)                        as sdsl,
            sum(case when tq.sdxjg = 0 then 1 else 0 end)                        as bsdsl,
            sum(case when tq.sdxjg = -1 then 1 else 0 end)                       as btgsl,
            ifnull(sum(case when tq.sdxjg = 1 then 1 else 0 end) / count(1), 0)  as sdzb,
            ifnull(sum(case when tq.sdxjg = 0 then 1 else 0 end) / count(1), 0)  as bsdzb,
            ifnull(sum(case when tq.sdxjg = -1 then 1 else 0 end) / count(1), 0) as btgzb,
            sum(case when tq.qsbsdxy = 0 then 1 else 0 end)                      as wqsbsdxysl,
            sum(case when tq.qsbsdxy = 1 then 1 else 0 end)                      as yqsbsdxysl
        from sdx.tsdxjg_qtyktyw tq
        <where>
            <if test="khh != null and khh != ''">
                and tq.khh = #{khh}
            </if>
            <if test="sdxjg != null and sdxjg != ''">
                and tq.sdxjg = #{sdxjg}
            </if>
            <if test="rq != null and rq != ''">
                and tq.rq = #{rq}
            </if>
        </where>
    </select>


    <select id="compute4cxywlb" resultType="com.apex.sdx.api.vo.compute.QtyktywtjVo">
        select
            t.ibm                                        as cxywlb,
            t.note                                       as cxywmc,
            ifnull(ktsl, 0)                              as ktsl,
            ifnull(sdsl, 0)                              as sdsl,
            ifnull(sdsl / ktsl, 0)                       as sdzb,
            ifnull(bsdsl, 0)                             as bsdsl,
            ifnull(bsdsl / ktsl, 0)                      as bsdzb,
            ifnull(btgsl, 0)                             as btgsl,
            ifnull(btgsl / ktsl, 0)                      as btgzb,
            ifnull(wqsbsdxysl, 0)                        as wqsbsdxysl,
            ifnull(yqsbsdxysl, 0)                        as yqsbsdxysl
        from
        (
            select
                cxywlb,
                count(cxywlb)                                as ktsl,
                sum(case when sdxjg = 1 then 1 else 0 end)   as sdsl,
                sum(case when sdxjg = 0 then 1 else 0 end)   as bsdsl,
                sum(case when sdxjg = -1 then 1 else 0 end)  as btgsl,
                sum(case when qsbsdxy = 0 then 1 else 0 end) as wqsbsdxysl,
                sum(case when qsbsdxy = 1 then 1 else 0 end) as yqsbsdxysl
            from
                sdx.tsdxjg_qtyktyw tq
            <where>
                <if test="khh != null and khh != ''">
                    and tq.khh = #{khh}
                </if>
                <if test="rq != null and rq != ''">
                    and tq.rq = #{rq}
                </if>
            </where>
            group by
                cxywlb
        ) as qtyktyw
            right join livebos.txtdm t on t.ibm = qtyktyw.cxywlb
        where
            t.fldm = 'SDX_CXYWLB'
            <if test="cxywlb != null and cxywlb != ''">
                and qtyktyw.cxywlb = #{cxywlb}
            </if>
        order by bsdsl desc
    </select>


    <select id="computemxBySdx" resultType="com.apex.sdx.api.vo.compute.QtyktywtjVo">
        select
        tq.khh,
        tq.cxywlb,
        t.note as cxywmc,
        case tq.sdxjg
        when -1 then '不通过'
        when 0 then '不适当'
        when 1 then '适当'
        else '未知'
        end as sdxjg,
        case tq.qsbsdxy
        when 0 then '未签署'
        when 1 then '已签署'
        else '未知'
        end as qsbsdxy
        from
        sdx.tsdxjg_qtyktyw tq
        left join livebos.txtdm t on t.ibm = tq.cxywlb
        where
        t.fldm = 'SDX_CXYWLB'
        and tq.sdxjg = 0
        <if test="khh != null and khh != ''">
            and tq.khh = #{khh}
        </if>
        <if test="cxywlb != null and cxywlb != ''">
            and tq.cxywlb = #{cxywlb}
        </if>
        <if test="qsbsdxy != null and qsbsdxy != ''">
            and tq.qsbsdxy = #{qsbsdxy}
        </if>
        <if test="rq != null and rq != ''">
            and tq.rq = #{rq}
        </if>
        order by tq.khh, tq.cxywlb
    </select>

</mapper>
