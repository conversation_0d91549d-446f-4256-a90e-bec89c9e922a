<template>
  <div>
    <div :id="chartsId" style=" width:90%; height:260px;margin: 0 25px auto">
    </div>
  </div>

</template>
<script>
import {defineComponent} from 'vue';

let barCharts = [];
export default defineComponent({
  props: ["dataset", "title", "pieTitle"],
  data() {
    return {
      data: [],//[{ value: 735, name: '不适当' }, { value: 310, name: '适当' }, { value: 135, name: '积极'}],
      colorList: [['#FF7800', '#FF1919'], ['#3686FD', '#00B42A']],
    }
  },
  methods: {
    initBarChart() {
      let el = document.getElementById(this.chartsId)
      barCharts[this.chartsId] = this.$echarts.init(el);
      this.setOption();
      // 添加窗口大小变化监听
      window.addEventListener('resize', this.handleResize);
    },
    setOption() {
      let _this = this;
      _this.data = _this.$props.dataset;
      let option = _this.getOption();
      barCharts[_this.chartsId].setOption(option);
      // 初始渲染后立即调整尺寸
      this.$nextTick(() => {
        barCharts[this.chartsId]?.resize();
      });
    },
    handleResize() {
      if (barCharts[this.chartsId]) {
        barCharts[this.chartsId].resize();
      }
    },
    getOption() {
      let _this = this;
      let option = {
        title: _this.getTitle(),
        tooltip: {
          show: true,
        },
     /*   grid:
            {
              left: '0%',
              right: '0%',
              top: -10,
              bottom: '5%'
            },*/
        series: [...this.getSeriesData(), ...this.getSeriesData1(), ...this.getSeriesData2(),
        ],
      };
      return option;
    },
    getTitle() {
      let _this = this;
      let data = [];
      _this.$props.pieTitle.forEach((item, index) => {
        data.push({
          text: item.name,
          subtext: '已处理：' + item.value,
          left: (10 + index * 20) + '%',
          top: '82%',
          textAlign: 'center',
          textStyle: {
            color: '#333333',
            fontSize: 13,
          },
          subtextStyle: {
            color: '#999',
            fontSize: 10
          }
        })
      })
      return data;
    },
    //最外环
    getSeriesData1() {
      let data = [];
      let _this = this;
      _this.$props.dataset.forEach((item, index) => {
        let pieData = _this.$props.pieTitle;
        data.push({
          type: 'pie',
          minAngle: 10,//最小的扇区角度（0 ~ 360），用于防止某个值过小导致扇区太小影响交互。
          radius: [87, 91],
          center: [(10 + index * 20) + '%', '45%'],
          datasetIndex: index,
          clockwise: false, //顺时加载
          z: 2,
          label: {
            position: 'center',
            formatter: function (params) {
              let percent = (item.value / (pieData[index].value + item.value) * 100).toFixed(2);
              return '{a|' + item.name + '}\n' + '{a|' + item.value + '} {b|/' + percent + '%}';
            },
            rich: {
              a: {
                color: '#333333'
              },
              b: {
                color: '#999999'
              },
            }
          },
          data: [{
            value: item.value,
            name: item.name,
          }],
          itemStyle: {
            color: "rgba(255, 120, 0, 0.15)",
            borderWidth: 0
          },
          tooltip: {
            show: false
          },
          //  hoverAnimation: false,
          emphasis: {
            // disabled: true,  // 完全禁用高亮效果
            scale: false,
          },
        });
      });

      return data;
    },
    //中间数据环
    getSeriesData() {
      let data = [];
      let _this = this;
      _this.$props.dataset.forEach((item, index) => {
        let pieData = _this.$props.pieTitle;
        let percent = (item.value / (pieData[index].value + item.value));
        data.push({
          type: 'gauge',
          radius: '62%',
          center: [(10 + index * 20) + '%', '45%'],
          datasetIndex: index,
          clockwise: false,
          startAngle: '-270',
          endAngle: '90',
          splitNumber: 15,
          pointer: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: [
                [percent, '#FF7800'],
                [1, 'rgba(255, 71, 13, 0.15)']
              ],
              width: 16
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            distance: -30,
            length: 30,
            lineStyle: {
              color: '#fff',
              width: 4
            }
          },
          axisLabel: {
            show: false
          },
          detail: {
            show: false,
          },
        });
      });

      return data;
    },

    //最里环
    getSeriesData2() {
      let data = [];
      let _this = this;
      _this.$props.dataset.forEach((item, index) => {
        data.push({
          type: 'pie',
          radius: [57, 60],
          center: [(10 + index * 20) + '%', '45%'],
          datasetIndex: index,
          clockwise: false, //顺时加载
          label: {
            show: false,
          },
          itemStyle: {
            color: "rgba(255, 120, 0, 0.1)",
          },
          data: [{
            value: item.value,
            name: item.name,
          }],
          tooltip: {
            show: false,
          },
          //  hoverAnimation: false,
          emphasis: {
            disabled: true,  // 完全禁用高亮效果
            scale: false,
          },
        });
      });

      return data;
    },

  },
  mounted() {
    if (this.$props.dataset && this.$props.dataset.length > 0) {
      this.initBarChart()
    }
  },
  computed: {
    chartsId() {
      return "id_" + this.title
    },
  },
  watch: {
    dataset(newValue, oldValue) {
      if (newValue && newValue.length > 0 && JSON.stringify(newValue) != JSON.stringify(oldValue)) {
        if (barCharts[this.chartsId]) {
          this.setOption();
        } else {
          this.initBarChart();
        }
      }
    }
  },
  unmounted() {
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize);
    if (barCharts[this.chartsId]) {
      barCharts[this.chartsId].dispose();
      delete barCharts[this.chartsId];
    }
  },
});
</script>

<style scoped>
</style>