package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.core.mybatis.entity.Vxtdm;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 *
 */
public interface VxtdmService extends IService<Vxtdm> {

    /**
     * 获取字典集合
     *
     * @param fldm 分类代码
     * @return
     */
    List<Vxtdm> querySjzd(String fldm);

    /**
     * 获取字典集合
     *
     * @param fldm 分类代码
     * @return
     */
    Map<String, Vxtdm> querySjzd4Map(String fldm);

    /**
     * 获取字典集合
     *
     * @param fldm 分类代码
     * @return
     */
    String getNote(String fldm, String ibm, String defaultNote);

    String getNote(String fldm, String ibm);

    String getNote(String fldm, Integer ibm);
}
