package com.apex.sdx.api.req.common;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/1/11
 */
@Setter
@Getter
public class PageRequest {

    /**
     * 每页显示记录数
     */
    @LiveProperty(note = "每页显示记录数", index = 1001)
    @NotNull
    private int pagesize = 10;

    /**
     * 查询分页
     */
    @LiveProperty(note = "查询分页", index = 1002)
    @NotNull
    private int pagenum = 1;


    @LiveProperty(note = "是否查询总数", index = 1003)
    @NotNull
    private boolean isSearchCount = true;
}
