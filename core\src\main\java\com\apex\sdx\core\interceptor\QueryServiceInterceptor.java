package com.apex.sdx.core.interceptor;

import com.apex.sdx.api.resp.common.R;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.logger.RecordsLogger;
import com.apexsoft.*;
import lombok.extern.slf4j.Slf4j;
import sdx.common.IService;

import java.lang.reflect.InvocationTargetException;

/**
 * 查询服务拦截器
 *
 * <AUTHOR>
 */
@LiveServiceInterceptor(isGlobal = false)
@Slf4j
public class QueryServiceInterceptor implements ILiveServiceInterceptor {


    /**
     * @param callable      服务处理函数
     * @param serviceInfo   服务信息
     * @param method        方法信息
     * @param targetService 服务对象
     * @param paramValues   方法参数
     * @param protocol      RPC协议
     * @return
     * @throws Exception
     */
    @Override
    public <T> T intercept(LiveServiceCallable<T> callable, ServiceInfo serviceInfo, InterfaceMethodInfo method, Object targetService, Object[] paramValues, RPCProtocol protocol) throws Throwable {
        long invokeDate = System.currentTimeMillis();
        String func = serviceInfo.getServiceId() + "/" + method.getMethodName();
        Object result = null;
        try {
            if (targetService instanceof IService) {
                if ("check".equals(method.getMethodName())) {
                    this.commonsCheck(paramValues[0]);
                    result = this.responseInvoke(callable, func, method.getMethodName());
                } else if ("execute".equals(method.getMethodName())) {
                    result = this.executeInvoke(callable, (IService)targetService, paramValues, func);
                } else {
                    result = R.builder()
                            .code(-1)
                            .errorCode(func + "-01")
                            .note("服务调用异常，方法[" + method.getMethodName() + "]不存在")
                            .build();
                }
            } else {
                // 普通查询服务
                result = callable.call();
            }
        } catch (Throwable e) {
            String note = "业务查询失败：";
            if (e instanceof InvocationTargetException) {
                e = ((InvocationTargetException) e).getTargetException();
            }
            int code = -1;
            if(e instanceof BusinessException){
                BusinessException be = (BusinessException) e;
                code = be.getCode();
            } else {
                note += e.getMessage();
                log.error(note, e);
            }
            note += e.getMessage();
            result = R.builder()
                    .code(code)
                    .note(note)
                    .errorCode(func + code)
                    .build();
        } finally {
            RecordsLogger.log(func, invokeDate, result, paramValues[0]);
        }
        return (T) result;
    }
    private <T> T executeInvoke(LiveServiceCallable<T> callable, IService targetService,
                                Object[] paramValues, String func) throws Throwable {
        // 执行excute业务处理前，先执行check业务检查
        R checkResp = this.checkInvoke(targetService, paramValues, func);
        // check失败时返回
        if (checkResp != null && checkResp.getCode() < 0) {
            return (T) checkResp;
        }
        // 检查成功执行execute
        return (T) this.responseInvoke(callable, func, "execute");
    }
    /**
     * 调用本服务的check方法
     *
     * @return
     */
    private R checkInvoke(IService targetService, Object[] paramValues, String func) {
        //检查通用入参
        this.commonsCheck(paramValues[0]);
        Object obj = targetService.check(paramValues[0]);
        if (obj == null) {
            return R.builder().code(1).note("业务检查通过").build();
        }
        R resp = (R) obj;
        int code = resp.getCode();
        if (code <= 0) {
            resp.setErrorCode(func + code);
        }
        return resp;
    }

    /**
     * 进入execute/check拦截器
     */
    private <T> R responseInvoke(LiveServiceCallable<T> callable, String func, String methodName) throws Throwable {
        R resp = (R) callable.call();
        if (resp == null && "check".equals(methodName)) {
            return R.builder().code(1).note("业务检查通过").build();
        }
        if (resp != null) {
            int code = resp.getCode();
            if (code <= 0 && resp.getErrorCode() == null) {
                resp.setErrorCode(func + code);
            }
        }
        return resp;
    }

    /**
     * 通用参数检查
     *
     * @param obj 参数对象
     */
    private void commonsCheck(Object obj) {
        // TODO 通用参数检查
    }
}
