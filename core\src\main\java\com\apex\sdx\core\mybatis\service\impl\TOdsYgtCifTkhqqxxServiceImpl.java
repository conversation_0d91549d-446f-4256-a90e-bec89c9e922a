package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhxyxx;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhqqxx;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhqqxxService;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTkhqqxxMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_ods_ygt_cif_tkhqqxx】的数据库操作Service实现
* @createDate 2025-02-07 16:12:39
*/
@Service
public class TOdsYgtCifTkhqqxxServiceImpl extends ServiceImpl<TOdsYgtCifTkhqqxxMapper, TOdsYgtCifTkhqqxx>
    implements TOdsYgtCifTkhqqxxService{

    @Override
    public TOdsYgtCifTkhqqxx getQqzhjbxx(String khh) {
        LambdaQueryWrapper<TOdsYgtCifTkhqqxx> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(TOdsYgtCifTkhqqxx::getKhh,
                        TOdsYgtCifTkhqqxx::getTzzfl,
                        TOdsYgtCifTkhqqxx::getPjzf,
                        TOdsYgtCifTkhqqxx::getYwzh,
                        TOdsYgtCifTkhqqxx::getSzxgedsx,
                        TOdsYgtCifTkhqqxx::getShxgedsx)
                .eq(TOdsYgtCifTkhqqxx::getKhh, khh);
        return this.getOne(queryWrapper, false);
    }
}




