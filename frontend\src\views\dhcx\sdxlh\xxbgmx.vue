<template>
  <div class="dxsdx_cxtj">
    <div class="cxtj_item">
      <span>开始日期：</span>
      <a-date-picker v-model:value="ksrq" style="width: 250px" placeholder="开始日期" />
    </div>
    <div class="cxtj_item">
      <span>结束日期：</span>
      <a-date-picker v-model:value="jsrq" style="width: 250px" placeholder="结束日期" />
    </div>
    <div class="cxtj_item">
      <a class="btn" @click="getXxbgmx">查询</a>
      <a class="btn fz" @click="reset">重置</a>
    </div>
    <a-divider style="margin: 12px 0"/>
    <div>
      <a-table
        :columns="columns"
        :data-source="data"
        :loading="loading"
        :pagination="pagination"
        @change="pageChange"
      ></a-table>
    </div>
  </div>
</template>
<script>
import {defineComponent} from "vue";
import {getDcNote} from "@utils/bussinessUtils";
import {commonApi} from "@/api/common";
import dayjs from "dayjs";
import {message} from "ant-design-vue";

export default defineComponent({
  name: "xxbgmx",
  inject: ["khh"],
  props: ["dictArray", "khjbxx"],
  data() {
    return {
      ksrq: null,
      jsrq: null,
      columns: [
        { title: '修改日期', dataIndex: 'xgrq', key: 'xgrq', fixed: true},
        { title: '修改时间', dataIndex: 'xgsj', key: 'xgsj',},
        { title: '字段名', dataIndex: 'zdmc', key: 'zdmc', width: 150,},
        { title: '原值', dataIndex: 'oldname',  key: 'oldname', maxWidth: 200},
        { title: '新值', dataIndex: 'newname',  key: 'newname', maxWidth: 200},
        { title: '修改人', dataIndex: 'xgr',  key: 'xgr', width: 100,},
        { title: '修改渠道', dataIndex: 'xgqd',  key: 'xgqd', width: 100,
          customRender: ({ text }) => {
            return getDcNote("SDX_FQQD", text, this.dictArray);
          }
        },
      ],
      data: [],
      open: false,
      loading: false,
      current: 1,
      pageSize: 10,
      total: 0,
    }
  },
  computed: {
    pagination() {
      return {
        total: this.total,
        current: this.current,
        pageSize: this.pageSize,
        //showQuickJumper: true,
        showSizeChanger: true,
        showTotal(total) {
          return "共 " + total + " 条";
        },
      };
    }
  },
  methods: {
    pageChange(page){
      this.current = page.current;
      this.pageSize = page.pageSize;
      this.getXxbgmx();
    },
    reset(){
      this.ksrq = null;
      this.jsrq = null;
    },
    getXxbgmx() {
      this.loading = true;
      commonApi.executeAMS(
        "sdx.query.IKhzlbgService",
        "khzlbgmxcx",
        {
          khh: this.khh,
          ksrq: this.ksrq ? dayjs(this.ksrq).format("YYYYMMDD") : null,
          jsrq: this.jsrq ? dayjs(this.jsrq).format("YYYYMMDD") : null,
          isSearchCount: true,
          pagenum: this.current,
          pagesize: this.pageSize
        },
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        this.data = res.records || [];
        this.total = res.total;
      }).finally(() => {
        this.loading = false;
      })
    },
  },
});
</script>
<style scoped>
.dxsdx_cxtj{line-height: 32px; padding: 0 5px 3px 10px;}
.dxsdx_cxtj .cxtj_item{ white-space: nowrap;margin-bottom: 12px; font-size: 14px; display: inline-block; vertical-align: middle; margin-right: 20px;}
.dxsdx_cxtj .cxtj_item span{color: #888; display: inline-block; margin-right: 15px; vertical-align: middle;}
.dxsdx_cxtj .cxtj_item input[type=text]{display: inline-block; vertical-align: middle; width: 340px; height: 32px; line-height: 30px; border:1px solid #d6d6d6; border-radius: 4px; padding: 0 10px;}
.dxsdx_cxtj .cxtj_item input[type=text]:focus{outline:1px solid #d0ad6b;}
a.btn{min-width: 80px; padding: 0 10px; margin-right: 10px; text-align: center; line-height: 32px;border-radius: 4px; background-color: #f6e5d1; color: #bf935f; cursor: pointer; display: inline-block; vertical-align: middle;}
a.btn.fz{background-color: #fff; color: #777;border: 1px solid #d6d6d6; line-height: 30px;}
a.btn:hover,a.btn.fz:Hover{ background-color: #bf935f; color: #fff; border: none; line-height: 32px;}
</style>