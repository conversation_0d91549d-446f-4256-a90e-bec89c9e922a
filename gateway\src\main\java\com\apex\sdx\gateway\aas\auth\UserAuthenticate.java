package com.apex.sdx.gateway.aas.auth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.apex.ams.livebos.services.UserInfoReply;
import com.apex.sdx.gateway.aas.common.session.UserSession;
import com.apex.sdx.gateway.aas.modules.index.model.AuthData;
import com.apex.sdx.gateway.aas.modules.index.model.AuthUser;
import com.apex.sdx.gateway.base.dao.LivebosGrpcDao;
import com.apex.sdx.gateway.base.model.JSONResponse;
import com.apex.sdx.gateway.base.service.LbProjectService;
import com.apex.sdx.gateway.base.service.UserService;
import com.apexsoft.live.exception.AuthException;
import com.apexsoft.live.session.AbstractUserAuthenticate;
import com.apexsoft.live.session.UserAuthenticateContext;
import com.apexsoft.live.utils.AES;
import com.apexsoft.livebos.ILiveBOSUserConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Component
public class UserAuthenticate extends AbstractUserAuthenticate {

    @Value("${application.name}")
    private String appName;
    @Value("${application.debug:false}")
    private boolean debug;
    @Value("${sso.enable:false}")
    private boolean ssoEnable;

    @Value("${sso.client.id:}")
    private String ssoClientId;
    @Value("${sso.client.url:}")
    private String ssoClientUrl;

    @Value("${sso.server.base-url:}")
    private String ssoServerBaseUrl;
    @Value("${sso.server.login-url:}")
    private String ssoServerLoginUrl;

    @Autowired
    private ILiveBOSUserConverter liveBOSUserConverter;
    @Autowired
    private LivebosGrpcDao livebosGRPC;
    @Autowired
    private LbProjectService lbProjectService;
    @Autowired
    private UserService userService;
    /**
     * 继承aas登录的方法
     * @param userAuthenticateContext
     * @param request
     * @param response
     */
    @Override
    public void auth(UserAuthenticateContext userAuthenticateContext, HttpServletRequest request, HttpServletResponse response) throws AuthException{
        AuthUser<JSONObject> authUser = new AuthUser<JSONObject>();
        Map<String, Object> data = new HashMap<>();
        String userid = "";
        boolean simulation;
        
        //从request取登录参数
        AuthData authData = new AuthData();
        authUser.setAccessToken(UserSession.getAccessToken());
        authUser.setClientId(authData.getClientId());
        UserInfoReply result = null;
        try{
            JSONObject requestObj = JSONObject.parseObject(getData(request));
            if(requestObj!=null && !requestObj.isEmpty()){
                authData=getAudhData(requestObj, request);
            }
            log.info("===柜员认证入参["+JSONObject.toJSONString(authData)+"]===");

            userid = authData.getUser();
            JSONObject ext = authData.getExt()==null?new JSONObject():authData.getExt();
            simulation = ext.containsKey("simulation")?ext.getBoolean("simulation"):false;
            if(!simulation){
                result = livebosGRPC.validateUser(userid,authData.getPassword());
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
            throw new AuthException("登录失败:"+e.getMessage());
        }

        //用户名密码验证
        if(result!=null && result.getResult()<0){
            throw new AuthException("登录失败:"+result.getMessage());
        }

        JSONObject userInfo = null;
        try {
            if(result==null){
                result = livebosGRPC.getUserInfo(userid);
            }
            authUser = (AuthUser<JSONObject>) liveBOSUserConverter.buildUser(result);
            if(!"admin".equals(userid)){
                authUser.setSimulation(simulation);
            }else{
                authUser.setSimulation(false);
            }
            // 用户权限
            JSONArray authList = livebosGRPC.queryUserAuthList(result.getUserId(), null);
            authUser.setAuthList(authList);
            //设置会话中存储的信息
            userAuthenticateContext.setUserinfo(authUser);
        }catch (Exception e){
            log.error(e.getMessage(),e);
            throw new AuthException("登录失败:"+e.getMessage());
        }


        //livebos登录
        liveBOSUserConverter.handleLiveBOSLogin(request,authData.getUser());//业务系统登录成功后再调用

        //设置返回给前端的报文
        data.put("code", JSONResponse.CODE_SUCCESS);
        data.put("note", "柜员登录认证成功");
        data.put("userinfo", userInfo);
        userAuthenticateContext.setAuthResponse(data);
    }

    /**
     * 不校验登录接口
     * @return
     */
    @Override
    public List<String> getExcludeUrls() {
        List<String> excludeUrls = super.getExcludeUrls();
        excludeUrls.add("/sso/*");
        if(debug) {
            excludeUrls.add("/debug/*");
            excludeUrls.add("/service/**");
        }
        return excludeUrls;
    }

    @Override
    public void handleLogoutResult(UserAuthenticateContext context, HttpServletRequest request, HttpServletResponse response) throws IOException {
        //livebos退出
        liveBOSUserConverter.handleLiveBOSLogout(request);
        super.handleLogoutResult(context, request, response);
    }

    @Override
    public void handleNoAuthResult(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setStatus(403);
        Map<String, Object> data = new HashMap<>();
        data.put("code", -403);
        data.put("note", "会话过期");
        //开启统一认证，需要跳转到统一认证的网址登录
        if(ssoEnable){
            String redirectUrl = ssoServerBaseUrl+ssoServerLoginUrl+"?"
                    +"client_id="+ ssoClientId + "&redirect_uri=" + ssoClientUrl
                    +"&response_type=code"+"&scope=openid"+"&state="+ UUID.randomUUID()
                    +"d&nonce="+UUID.randomUUID();
            data.put("redirect_url", redirectUrl);
        }
        handleJSONReponse(response, data);
    }


    public static String getData(HttpServletRequest request) throws IOException {
        InputStreamReader is = null;
        try {
            is = new InputStreamReader(request.getInputStream(), request.getCharacterEncoding());
            char[] chars = new char[8 * 1024];
            int len;
            StringBuilder sb = new StringBuilder();
            while ((len = is.read(chars)) != -1) {
                sb = sb.append(chars, 0, len);
            }
            return sb.toString();

        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public AuthData getAudhData(JSONObject data, HttpServletRequest request)throws Exception{
        AuthData authData = new AuthData();
        try {
            authData.setExt(data.getJSONObject("ext"));
        }catch (Exception e){
            authData.setExt(new JSONObject());
        }
        authData.setClientId(data.getString("clientId"));

        String signature=data.getString("signature");
        String decryptData = AES.decrypt(signature, appName);
        if (decryptData == null) {
            String referer = request.getHeader("Referer");
            if (StringUtils.isEmpty(referer) || !referer.endsWith("swagger-ui.html")) {
                throw new AuthException(-1, "非法请求,秘钥不合法");
            }

            log.error("加密串解码失败:" +signature);
        } else {
            try {
                JSONObject json = JSON.parseObject(decryptData);
                authData.setUser(json.getString("user"));
                authData.setPassword(json.getString("password"));
                try {
                    authData.setTimestamp(Long.valueOf(json.getString("timestamp")));
                } catch (Exception var16) {
                    log.warn("timestamp解析异常", var16);
                }
            } catch (Exception var17) {
                String[] maps = decryptData.split("&");
                String[] var9 = maps;
                int var10 = maps.length;

                for(int var11 = 0; var11 < var10; ++var11) {
                    String keyvalue = var9[var11];
                    int firstIndex = keyvalue.indexOf("=");
                    if (keyvalue.substring(0, firstIndex).equals("user")) {
                        authData.setUser(keyvalue.substring(firstIndex + 1));
                    } else if (keyvalue.substring(0, firstIndex).equals("password")) {
                        authData.setPassword(keyvalue.substring(firstIndex + 1));
                    } else if (keyvalue.substring(0, firstIndex).equals("timestamp")) {
                        try {
                            authData.setTimestamp(Long.valueOf(keyvalue.substring(firstIndex + 1)));
                        } catch (Exception var15) {
                            log.warn("timestamp解析异常", var15);
                        }
                    }
                }
            }
        }
        return authData;
    }

    public static void main(String[] args) {
        String decryptData = AES.decrypt("s7n/qycCFUcOJd97qGT8eoS/0Eymjd0tb0NcoCoCTYNLCs2ixU2CFgf7NJXKIG105UxDrOvs9RBcOnWROtd5Fpy299nxrueNJgrh/z6MoHNSQQKBVAjNSqzHsoYYFrm3CFfSxYejyixO5P8AHjVgB99kTC0ZGqvf9Ie1SOsMSGg7vV0vKLrfyX41iVRGwq0SWgebiBCweSN+pk6SaYnjvgw+iSh3sPjdR+UhXJ5iXnWNDVwtHYBdGDrhPyl5dJxhdHvoEX6zV5webGYADuHWNUZLns4YHcla89lRsJachjOlPJ0XQetRaEWnzrjuGpNnklUraimxhbfu1lEd4HJ5lw==" +
                "", "ygt_api_gatway");
        System.out.println(decryptData);

        String signData = "mode=user&clientId=jgyy_api_gateway&user=admin&password=111111&ext={\"czzd\":\"PC;IIP=NA;IPORT=NA;LIP=***********;MAC=005056B60D13;HD=NA;PCN=BJ-VDIEQ01-013;CPU=1F8BFBFF000306F0;PI=C^NTFS^99;VOL=52BE4942\"}&timestamp=1675852420696";
        String signDatamw = AES.encrypt(signData,"jgyy_api_gateway");
        System.out.println(signDatamw);
    }
}