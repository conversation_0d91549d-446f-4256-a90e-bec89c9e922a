package com.apex.sdx.api.vo.query;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025-04-21
 * @Description:
 */
@Getter
@Setter
public class KhsjzbVo {

    /**
     * 客户号
     */
    @LiveProperty(note = "客户号", index = 1)
    private String khh;
    /**
     * 日期
     */
    @LiveProperty(note = "日期", index = 2)
    private String rq;
    /**
     * 指标code
     */
    @LiveProperty(note = "指标code", index = 3)
    private String idxCode;

    @LiveProperty(note = "指标id", index = 4)
    private int idxId;

    @LiveProperty(note = "结果集", index = 5)
    private BigDecimal result;

    @LiveProperty(note = "cycle", index = 6)
    private Integer cycle;

    @LiveProperty(note = "相关数据", index = 7)
    private String xgsj;

}
