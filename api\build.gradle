plugins {
    id 'java'
    //springBoot插件
    id 'org.springframework.boot' version "${springBootVersion}" apply false
    //spring依赖管理插件
    id 'io.spring.dependency-management' version "${springDependencyManagement}"
    id 'java-library'
}

dependencyManagement {
    imports {
        mavenBom org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        mavenBom "com.apexsoft:live-spring-boot2-bom:${liveVersion}"
    }
    resolutionStrategy{
        cacheChangingModulesFor 0, 'seconds'
    }
}
compileJava {
    options.compilerArgs << '-parameters'
}
compileTestJava{
    options.compilerArgs << '-parameters'
}
group = 'com.apexsoft'
version = '1.0.0'
//依赖
dependencies {
    //基础
    implementation "com.apexsoft:live-spring-boot-starter:${liveVersion}"
    implementation "com.apexsoft:live-service-annotation:${liveVersion}"
    //微服务
    /*implementation "com.apexsoft:live-service-grpc-exporter-starter:${liveVersion}"
    implementation "com.apex:ams-consul"
    implementation "com.apex:ams-nacos"*/

    //log4j2 异步日志
    implementation "org.springframework.boot:spring-boot-starter-log4j2"
    implementation 'com.lmax:disruptor:3.4.4'

    implementation group: 'com.alibaba', name: 'fastjson', version: '1.2.76'
    implementation group: 'org.apache.httpcomponents', name: 'httpclient', version: '4.5.13'
    implementation group: 'org.dom4j', name: 'dom4j', version: '2.1.3'
    implementation group: 'commons-fileupload', name: 'commons-fileupload', version: '1.4'
    implementation group: 'org.apache.commons', name: 'commons-lang3', version: '3.12.0'

    //lombok
    compileOnly 'org.projectlombok:lombok:1.18.24'
    annotationProcessor 'org.projectlombok:lombok:1.18.24'
    //fastjson包
    api group: 'com.alibaba', name: 'fastjson', version: '1.2.76'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    implementation fileTree(dir: '../libs', includes: ['*.jar'])
}
//单元测试
test {
    useJUnitPlatform()
}

//依赖缓存时间
configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}
configurations {
    all*.exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    all*.exclude group:"org.slf4j",module:"slf4j-log4j12"
}

jar {
    baseName 'sdx-api'
    version ''
}