<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTkhqqxxMapper">

    <resultMap id="BaseResultMap" type="com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhqqxx">
            <result property="khh" column="KHH" jdbcType="VARCHAR"/>
            <result property="ywxt" column="YWXT" jdbcType="DECIMAL"/>
            <result property="ywzh" column="YWZH" jdbcType="VARCHAR"/>
            <result property="glywxt" column="GLYWXT" jdbcType="DECIMAL"/>
            <result property="glywzh" column="GLYWZH" jdbcType="VARCHAR"/>
            <result property="tzzfj" column="TZZFJ" jdbcType="DECIMAL"/>
            <result property="tzzfl" column="TZZFL" jdbcType="DECIMAL"/>
            <result property="zdbzj" column="ZDBZJ" jdbcType="DECIMAL"/>
            <result property="zjgmsx" column="ZJGMSX" jdbcType="DECIMAL"/>
            <result property="pjzf" column="PJZF" jdbcType="DECIMAL"/>
            <result property="shxgedsx" column="SHXGEDSX" jdbcType="DECIMAL"/>
            <result property="szxgedsx" column="SZXGEDSX" jdbcType="DECIMAL"/>
            <result property="email" column="EMAIL" jdbcType="VARCHAR"/>
            <result property="sj" column="SJ" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        KHH,YWXT,YWZH,
        GLYWXT,GLYWZH,TZZFJ,
        TZZFL,ZDBZJ,ZJGMSX,
        PJZF,SHXGEDSX,SZXGEDSX,
        EMAIL,SJ
    </sql>
</mapper>
