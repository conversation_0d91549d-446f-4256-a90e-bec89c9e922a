package sdx.query;

import com.apex.sdx.api.req.compute.SdxsjtjReq;
import com.apex.sdx.api.req.query.SdxsjcxReq;
import com.apex.sdx.api.resp.common.QueryPageResponse;
import com.apex.sdx.api.resp.query.SdxsjDescRes;
import com.apex.sdx.api.vo.compute.SdxsjtjVo;
import com.apex.sdx.api.vo.query.SdxsjVo;
import com.apexsoft.LiveMethod;
import com.apexsoft.LiveProperty;

/**
 * <AUTHOR>
 * @Date 2025-03-04
 * @Description:
 */
public interface ISdxsjService {
    @LiveMethod(paramAsRequestBody = true, note = "适当性事件查询")
    QueryPageResponse<SdxsjVo> sdxsjcx(SdxsjcxReq req) throws Exception;

    /**
     * 适当性事件详情
     * @param id
     * @return
     */
    @LiveMethod(note = "适当性事件详情")
    SdxsjDescRes sdxsjDesc(@LiveProperty(note = "ID", index = 1) Long id);

    @LiveMethod(paramAsRequestBody = true, note = "适当性事件时间轴数据查询")
    QueryPageResponse<SdxsjVo> sdxtimeline(SdxsjcxReq req) throws Exception;

    @LiveMethod(paramAsRequestBody = true, note = "适当性事件统计")
    QueryPageResponse<SdxsjtjVo> sdxsjtj(SdxsjtjReq req) throws Exception;
}
