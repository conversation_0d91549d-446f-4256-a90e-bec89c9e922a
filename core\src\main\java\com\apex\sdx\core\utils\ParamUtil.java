package com.apex.sdx.core.utils;

import com.alibaba.fastjson.JSONObject;
import com.apex.sdx.api.req.common.Request;
import com.apex.sdx.api.resp.common.Response;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.mybatis.service.TxtdmService;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 参数工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ParamUtil {

    private static TxtdmService txtdmService;

    @Autowired
    public void setTxtdmService(TxtdmService txtdmService) {
        ParamUtil.txtdmService = txtdmService;
    }

    /**
     * 校验字段是否必填
     *
     * @param param    请求对象
     * @param paraName 校验字段
     */
    public static void check1Param(Object param, String paraName) {
        // 必填
        checkParamRequired(param, paraName);
    }

    /**
     * 校验字典是否存在
     *
     * @param param    请求对象
     * @param paraName 校验字段
     * @param fldm     字典代码
     */
    public static void check2Param(Object param, String paraName, String fldm) {
        // 必填
        checkParamRequired(param, paraName);
        // 字典检查
        checkParamDict(param, paraName, fldm);
    }

    /**
     * 校验关键字段有效性
     *
     * @param req      请求对象
     * @param paraName 校验字段
     */
    public static void check3Param(JSONObject req, String paraName) {
        // 校验字段有效性
        checkParamValid(req, paraName);
    }

    /**
     * 校验字段有效性
     *
     * @param req
     * @param paraName
     */
    private static void checkParamValid(JSONObject req, String paraName) {
        String checkNote = null;
        Object param = req.get(paraName);
        if (checkNote != null) {
            throw new BusinessException(-598, checkNote);
        }
    }

    /**
     * 校验字段必填
     *
     * @param param
     * @param paraName
     */
    private static void checkParamRequired(Object param, String paraName) {
        boolean isNull = false;
        //参数值非空检查
        if (param instanceof String) {
            String str = (String) param;
            if (StringUtils.isEmpty(str)) {
                isNull = true;
            }
        } else {
            if (param == null) {
                isNull = true;
            }
        }
        if (isNull) {
            throw new BusinessException(-599, "参数[" + paraName + "]不能为空");
        }
    }

    /**
     * 校验字段字典是否有效
     *
     * @param param
     * @param paraName
     * @param fldm
     */
    private static void checkParamDict(Object param, String paraName, String fldm) {
        String ibm = txtdmService.getNoteByIbm(fldm, param.toString());
        if (StringUtils.isEmpty(ibm)) {
            String note = String.format("参数[%s]，无效的数据字典[%s],值[%s]", paraName, fldm, param);
            throw new BusinessException(-598, note);
        }
    }


    /*public static Response checkParam(Object model) {
        try {
            Field[] fields = model.getClass().getDeclaredFields();
            boolean flag = true;
            String fieldstr = "业务要素不存在：";
            for (Field field : fields) {
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                if (annotation != null && annotation.required()) {
                    String name = field.getName();
                    if (!chechFiled(model, name, field)) {
                        flag = false;
                        fieldstr = fieldstr + name + "  ";
                    }
                }
            }
            if (flag) {//校验通过
                return Response.ok().note("检查通过");
            } else {//有参数校验不通过
                return Response.error().note(fieldstr);
            }
        } catch (Exception e) {
            log.error("对非空参数进行校验时异常 e: {}", e.getMessage());
            return Response.error().note("对非空参数进行校验时异常");
        }
    }*/

    private static boolean chechFiled(Object model, String name, Field field) throws Exception {
        boolean flag = true;
        name = name.substring(0, 1).toUpperCase() + name.substring(1); //将属性的首字符大写，方便构造get，set方法
        String type = field.getGenericType().toString();    //获取属性的类型
        if (type.equals("class java.lang.String")) {   //如果type是类类型，则前面包含"class "，后面跟类名
            Method m = model.getClass().getMethod("get" + name);
            String value = (String) m.invoke(model);    //调用getter方法获取属性值
            if (StringUtils.isEmpty(value)) {
                flag = false;
            }
        } else {
            Method m = model.getClass().getMethod("get" + name);
            Object value = m.invoke(model);
            if (value == null) {
                flag = false;
            }
        }
        return flag;
    }


    /**
     * SQL注入字符串验证
     *
     * @param req
     * @return
     */
    public static Response SqlRegularParam(Request req) {
        Response resp = new Response();
        java.lang.reflect.Field[] fields = req.getClass().getFields();
        for (int i = 0; i < fields.length; i++) {
            String ParamName = null, ParamValue = null;
            try {
                ParamName = fields[i].getName();
                ParamValue = fields[i].get(req).toString();
            } catch (IllegalAccessException e) {
                log.error("获取参数值失败，字段名称[{}]", ParamName, e);
                resp.setCode(-580);
                resp.setNote("获取参数值失败");
                return resp;
            }
            //SQL注入正则验证
            try {
                resp = ParamUtil.SqlRegular(ParamName, ParamValue);
            } catch (Exception e) {
                log.error("SQL注入字符串验证失败，字段名称[{}]", ParamName);
                resp.setCode(-581);
                resp.setNote("SQL注入字符串验证失败");
                return resp;
            }
            if (resp.getCode() < 0) {
                return resp;
            }

        }

        return new Response(1, "检查通过");
    }

    /**
     * SQL注入字符串验证
     *
     * @param key
     * @param value
     * @return
     * @throws Exception
     */
    public static Response SqlRegular(Object key, Object value) throws Exception {
        Response resp = new Response();

        String regex = null;
        int code = 1;
        String note = "数据格式合法!";
        if (value == null || value.equals("")) {
            resp.setCode(2);
            resp.setNote("无数据,不检查数据格式");
            return resp;
        }


        regex = "(?:')|(?:--)|(/\\*(?:.|[\\n\\r])*?\\*/)|" + "(\\b(select|update|and|or|delete|insert|truncate|char|into|substr|ascii|declare|exec|count|master|into|drop|execute)\\b)";
        Pattern sqlPattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        if (sqlPattern.matcher(value.toString()).find()) {
            code = -301;
            note = "[" + key + "]不符合数据格式要求:" + value;
        }

        resp.setCode(code);
        resp.setNote(note);
        return resp;
    }


    /**
     * 属性深拷贝，null不拷贝
     *
     * @param source
     * @param target
     */
    public static void copyPropertiesIgnoreNull(Object source, Object target) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<String>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        BeanUtils.copyProperties(src, target, emptyNames.toArray(result));
    }


    /**
     * 多值字典去重
     *
     * @param ibms
     * @return
     */
    public static String dedupDictValue(String ibms) {
        String[] oldArr = ibms.split(";");
        //判断参数是否是多值
        if (oldArr.length > 1) {
            //利用Set集合的不可重复性进行元素过滤
            LinkedHashSet<String> set = new LinkedHashSet<>();
            Collections.addAll(set, oldArr);
            String[] newArr = (String[]) set.toArray(new String[set.size()]);
            //判断去重后的数据是否相同，如果相同直接返回原值
            if (Arrays.equals(oldArr, newArr)) {
                return ibms;
            } else {
                //组装去重后的参数值
                StringBuilder newIbms = null;
                for (String ibm : newArr) {
                    if (newIbms == null) {
                        newIbms = new StringBuilder(ibm);
                    } else {
                        newIbms.append(";").append(ibm);
                    }
                }
                return newIbms.toString();
            }
        }
        return ibms;
    }
}
