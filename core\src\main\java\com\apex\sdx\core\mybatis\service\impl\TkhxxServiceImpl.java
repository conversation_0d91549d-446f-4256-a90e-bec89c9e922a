package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.resp.query.KhsdxfbRes;
import com.apex.sdx.api.vo.khgl.KhxxtjVo;
import com.apex.sdx.api.vo.khgl.TzzcfbVo;
import com.apex.sdx.api.vo.khgl.ZhsdxqkVo;
import com.apex.sdx.api.vo.query.SdxsjVo;
import com.apex.sdx.core.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apex.sdx.core.mybatis.entity.Tkhxx;
import com.apex.sdx.core.mybatis.service.TkhxxService;
import com.apex.sdx.core.mybatis.mapper.TkhxxMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 *
 */
@Slf4j
@Service
public class TkhxxServiceImpl extends ServiceImpl<TkhxxMapper, Tkhxx>
    implements TkhxxService{

    @Override
    public Tkhxx getKhxxByKhhZjbhZjlb(String khh, String zjbh, Integer zjlb) {
        QueryWrapper<Tkhxx> wrapper = new QueryWrapper<>();
        try {
            wrapper.eq("khh", khh);
            if (StringUtils.isNotBlank(zjbh)) {
                wrapper.eq("zjbh", zjbh);
            }
            if (zjlb != null) {
                wrapper.eq("zjlb", zjlb);
            }
            return this.getOne(wrapper);
        } catch (Exception e) {
            String note = String.format("查询基本客户信息异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-610, note);
        }
    }

    @Override
    public List<KhxxtjVo> khxxtj() {
        try {
            Map<String, Object> basicStats = this.baseMapper.khxxtjBasic();
            Map<String, Object> riskStats = this.baseMapper.khxxtjRisk();
            Map<String, Object> dateStats = this.baseMapper.khxxtjDate();
            Map<String, Object> investorStats = this.baseMapper.khxxtjInvestor();
            Map<String, Object> otherStats = this.baseMapper.khxxtjOther();

            KhxxtjVo result = new KhxxtjVo();

            Long totalCount = (Long) basicStats.get("totalCount");
            Long tzzflTotal = (Long) investorStats.get("tzzflTotal");
            Long qtTotal = (Long) otherStats.get("qtTotal");

            // 账户状态统计
            Long zhztzcgs = (Long) basicStats.get("zhztzcgs");
            Long zhztycgs = (Long) basicStats.get("zhztycgs");
            result.setZhztzcgs(zhztzcgs != null ? zhztzcgs.intValue() : 0);
            result.setZhztycgs(zhztycgs != null ? zhztycgs.intValue() : 0);
            result.setZhztzczb(totalCount > 0L && zhztzcgs != null ?
                Math.round(zhztzcgs * 10000.0D / totalCount) / 10000.0D : 0.0D);
            result.setZhztyczb(totalCount > 0L && zhztycgs != null ?
                Math.round(zhztycgs * 10000.0D / totalCount) / 10000.0D : 0.0D);

            // 反洗钱风险等级统计
            Long zhfxqfxdjzcgs = (Long) riskStats.get("zhfxqfxdjzcgs");
            Long zhfxqfxdjycgs = (Long) riskStats.get("zhfxqfxdjycgs");
            result.setZhfxqfxdjzcgs(zhfxqfxdjzcgs != null ? zhfxqfxdjzcgs.intValue() : 0);
            result.setZhfxqfxdjycgs(zhfxqfxdjycgs != null ? zhfxqfxdjycgs.intValue() : 0);
            result.setZhfxqfxdjzczb(totalCount > 0L && zhfxqfxdjzcgs != null ?
                Math.round(zhfxqfxdjzcgs * 10000.0D / totalCount) / 10000.0D : 0.0D);
            result.setZhfxqfxdjyczb(totalCount > 0L && zhfxqfxdjycgs != null ?
                Math.round(zhfxqfxdjycgs * 10000.0D / totalCount) / 10000.0D : 0.0D);

            // 专业投资者测评统计
            Long zytzzcpzcgs = (Long) investorStats.get("zytzzcpzcgs");
            Long zytzzcpycgs = (Long) investorStats.get("zytzzcpycgs");
            result.setZytzzcpzcgs(zytzzcpzcgs != null ? zytzzcpzcgs.intValue() : 0);
            result.setZytzzcpycgs(zytzzcpycgs != null ? zytzzcpycgs.intValue() : 0);
            result.setZytzzcpzczb(tzzflTotal > 0L && zytzzcpzcgs != null ?
                Math.round(zytzzcpzcgs * 10000.0D / tzzflTotal) / 10000.0D : 0.0D);
            result.setZytzzcpyczb(tzzflTotal > 0L && zytzzcpycgs != null ?
                Math.round(zytzzcpycgs * 10000.0D / tzzflTotal) / 10000.0D : 0.0D);

            // 证件有效期统计
            Long zjyxqzcgs = (Long) dateStats.get("zjyxqzcgs");
            Long zjyxqycgs = (Long) dateStats.get("zjyxqycgs");
            result.setZjyxqzcgs(zjyxqzcgs != null ? zjyxqzcgs.intValue() : 0);
            result.setZjyxqycgs(zjyxqycgs != null ? zjyxqycgs.intValue() : 0);
            result.setZjyxqzczb(totalCount > 0L && zjyxqzcgs != null ?
                Math.round(zjyxqzcgs * 10000.0D / totalCount) / 10000.0D : 0.0D);
            result.setZjyxqyczb(totalCount > 0L && zjyxqycgs != null ?
                Math.round(zjyxqycgs * 10000.0D / totalCount) / 10000.0D : 0.0D);

            // 风险测评有效性统计
            Long fxcpyxzcgs = (Long) dateStats.get("fxcpyxzcgs");
            Long fxcpyxycgs = (Long) dateStats.get("fxcpyxycgs");
            result.setFxcpyxzcgs(fxcpyxzcgs != null ? fxcpyxzcgs.intValue() : 0);
            result.setFxcpyxycgs(fxcpyxycgs != null ? fxcpyxycgs.intValue() : 0);
            result.setFxcpyxzczb(totalCount > 0L && fxcpyxzcgs != null ?
                Math.round(fxcpyxzcgs * 10000.0D / totalCount) / 10000.0D : 0.0D);
            result.setFxcpyxyczb(totalCount > 0L && fxcpyxycgs != null ?
                Math.round(fxcpyxycgs * 10000.0D / totalCount) / 10000.0D : 0.0D);

            // 其他信息统计
            Long zhqtxxzcgs = (Long) otherStats.get("zhqtxxzcgs");
            Long zhqtxxycgs = (Long) otherStats.get("zhqtxxycgs");
            result.setZhqtxxzcgs(zhqtxxzcgs != null ? zhqtxxzcgs.intValue() : 0);
            result.setZhqtxxycgs(zhqtxxycgs != null ? zhqtxxycgs.intValue() : 0);
            result.setZhqtxxzczb(qtTotal > 0L && zhqtxxzcgs != null ?
                Math.round(zhqtxxzcgs * 10000.0D / qtTotal) / 10000.0D : 0.0D);
            result.setZhqtxxyczb(qtTotal > 0L && zhqtxxycgs != null ?
                Math.round(zhqtxxycgs * 10000.0D / qtTotal) / 10000.0D : 0.0D);

            List<KhxxtjVo> resultList = new ArrayList<>();
            resultList.add(result);
            return resultList;

        } catch (Exception e) {
            String note = String.format("客户信息统计失败：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }


    @Override
    public List<KhsdxfbRes> queryTzzflxx(String type) {
        try {
            return this.baseMapper.queryTzzflxx(type);
        } catch (Exception e) {
            String note = String.format("查询投资者分类信息异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-610, note);
        }
    }

    @Override
    public List<KhsdxfbRes> queryFxcsnl(String type) {
        try {
            return this.baseMapper.queryFxcsnl(type);
        } catch (Exception e) {
            String note = String.format("查询风险承受能力信息异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-610, note);
        }
    }

    @Override
    public List<KhsdxfbRes> queryAbcTzzflxx() {
        try {
            return this.baseMapper.queryAbcTzzflxx();
        } catch (Exception e) {
            String note = String.format("查询ABC类投资者数据异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-610, note);
        }
    }

    @Override
    public Page<ZhsdxqkVo> queryZhztmx(
            String khh, String zhqk, String zhzt, boolean isSearchCount, int pagesize, int pagenum) {
        Page<SdxsjVo> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);
        try {
            return this.baseMapper.queryZhztmx(page, khh, zhqk, zhzt);
        } catch (Exception e) {
            String note = String.format("查询账户适当性情况-账户状态明细失败：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-602, note);
        }
    }

    @Override
    public Page<ZhsdxqkVo> queryZjyxqmx(
            String khh, String zhqk, String zjjzrq, boolean isSearchCount, int pagesize, int pagenum) {
        Page<SdxsjVo> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);
        try {
            return this.baseMapper.queryZjyxqmx(page, khh, zhqk, zjjzrq);
        } catch (Exception e) {
            String note = String.format("查询账户适当性情况-证件有效期明细失败：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-603, note);
        }
    }

    @Override
    public Page<ZhsdxqkVo> queryFxqfxdjmx(
            String khh, String xqfxdj, boolean isSearchCount, int pagesize, int pagenum) {
        Page<SdxsjVo> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);
        try {
            return this.baseMapper.queryFxqfxdjmx(page, khh, xqfxdj);
        } catch (Exception e) {
            String note = String.format("查询账户适当性情况-反洗钱风险等级明细失败：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-604, note);
        }
    }

    @Override
    public Page<ZhsdxqkVo> queryFxcpyxmx(
            String khh, String zhqk, String cpyxq, boolean isSearchCount, int pagesize, int pagenum) {
        Page<SdxsjVo> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);
        try {
            return this.baseMapper.queryFxcpyxmx(page, khh, zhqk, cpyxq);
        } catch (Exception e) {
            String note = String.format("查询账户适当性情况-风险测评有效明细失败：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-605, note);
        }
    }

    @Override
    public Page<ZhsdxqkVo> queryZytzzcpmx(
            String khh, String tzzpdyxq, boolean isSearchCount, int pagesize, int pagenum) {
        Page<SdxsjVo> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);
        try {
            return this.baseMapper.queryZytzzcpmx(page, khh, tzzpdyxq);
        } catch (Exception e) {
            String note = String.format("查询账户适当性情况-专业投资者测评明细失败：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-606, note);
        }
    }

    @Override
    public Page<ZhsdxqkVo> queryQtxxmx(
            String khh, String qtxxzt, boolean isSearchCount, int pagesize, int pagenum) {
        Page<SdxsjVo> page = new Page<>(pagenum, pagesize);
        page.setSearchCount(isSearchCount);
        try {
            return this.baseMapper.queryQtxxmx(page, khh, qtxxzt);
        } catch (Exception e) {
            String note = String.format("查询账户适当性情况-其它信息明细失败：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-607, note);
        }
    }

    @Override
    public TzzcfbVo queryTzzcfb() {
        TzzcfbVo result = new TzzcfbVo();
        try {
            // 获取风险承受能力字典数据
            List<Map<String, Object>> fxcsnlDicList = this.baseMapper.queryFxcsnlDic();
            
            // 获取投资者数据统计
            List<Map<String, Object>> statsList = this.baseMapper.queryTzzcfbStats();
            
            // 初始化返回数据
            List<String> fxcsnlArray = new ArrayList<>();
            List<Integer> pttzrCountArray = new ArrayList<>();
            List<Double> pttzrPercentArray = new ArrayList<>();
            List<Integer> zytzrCountArray = new ArrayList<>();
            List<Double> zytzrPercentArray = new ArrayList<>();
            
            // 处理风险承受能力字典，保持顺序
            Map<String, String> fxcsnlMap = new LinkedHashMap<>(); // 使用LinkedHashMap保持插入顺序
            List<String> ibmOrderList = new ArrayList<>(); // 保存ibm的顺序
            for (Map<String, Object> dic : fxcsnlDicList) {
                String ibm = String.valueOf(dic.get("ibm"));
                String note = String.valueOf(dic.get("note"));
                fxcsnlMap.put(ibm, note);
                ibmOrderList.add(ibm); // 记录ibm的顺序
                fxcsnlArray.add(note);
            }
            
            // 初始化数据统计映射
            Map<String, Integer> pttzrCountMap = new HashMap<>();
            Map<String, Integer> zytzrCountMap = new HashMap<>();
            
            // 初始化数量统计，按字典顺序
            for (String ibm : ibmOrderList) {
                pttzrCountMap.put(ibm, 0);
                zytzrCountMap.put(ibm, 0);
            }
            
            // 计算各风险等级的投资者数量
            int pttzrTotal = 0;
            int zytzrTotal = 0;
            
            for (Map<String, Object> stat : statsList) {
                // 处理cpdj为null的情况
                Object cpdjObj = stat.get("cpdj");
                if (cpdjObj == null || "null".equals(String.valueOf(cpdjObj)) || StringUtils.isEmpty(String.valueOf(cpdjObj))) {
                    // cpdj为空或null时不参与计算
                    continue;
                }
                String cpdj = String.valueOf(cpdjObj);
                
                // 验证cpdj是否在字典范围内
                if (!fxcsnlMap.containsKey(cpdj)) {
                    // cpdj不在有效范围时不参与计算
                    continue;
                }
                
                // 处理tzzfl为null的情况
                Object tzzflObj = stat.get("tzzfl");
                if (tzzflObj == null || "null".equals(String.valueOf(tzzflObj)) || StringUtils.isEmpty(String.valueOf(tzzflObj))) {
                    // tzzfl为空或null时不参与计算
                    continue;
                }
                Integer tzzfl = Integer.parseInt(String.valueOf(tzzflObj));
                
                // 处理count值
                Object countObj = stat.get("count");
                Integer count = 0;
                if (countObj != null && !"null".equals(String.valueOf(countObj))) {
                    count = Integer.parseInt(String.valueOf(countObj));
                }

                // 根据投资者类型进行分类统计
                if (tzzfl == 0) { // 普通投资者
                    pttzrCountMap.put(cpdj, pttzrCountMap.getOrDefault(cpdj, 0) + count);
                    pttzrTotal += count;
                } else if (tzzfl == 1 || tzzfl == 2 || tzzfl == 3) { // 明确判断专业投资者（1、2、3都是专业投资者）
                    zytzrCountMap.put(cpdj, zytzrCountMap.getOrDefault(cpdj, 0) + count);
                    zytzrTotal += count;
                }
                // 其他tzzfl值不参与统计
            }
            
            // 填充返回数据，按字典顺序
            for (String ibm : ibmOrderList) {
                int ptCount = pttzrCountMap.getOrDefault(ibm, 0);
                int zyCount = zytzrCountMap.getOrDefault(ibm, 0);

                pttzrCountArray.add(ptCount);
                zytzrCountArray.add(zyCount);

                // 计算占比
                double ptPercent = pttzrTotal > 0 ? (double) ptCount / pttzrTotal * 100 : 0;
                double zyPercent = zytzrTotal > 0 ? (double) zyCount / zytzrTotal * 100 : 0;

                // 保留2位小数
                pttzrPercentArray.add(Math.round(ptPercent * 100) / 100.0);
                zytzrPercentArray.add(Math.round(zyPercent * 100) / 100.0);
            }
            
            result.setFxcsnlArray(fxcsnlArray);
            result.setPttzrCountArray(pttzrCountArray);
            result.setPttzrPercentArray(pttzrPercentArray);
            result.setZytzrCountArray(zytzrCountArray);
            result.setZytzrPercentArray(zytzrPercentArray);
            
            return result;
        } catch (Exception e) {
            String note = String.format("查询投资者构成及分布失败：%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-608, note);
        }
    }
}




