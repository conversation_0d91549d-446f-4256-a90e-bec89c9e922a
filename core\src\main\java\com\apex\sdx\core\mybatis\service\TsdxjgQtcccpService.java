package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.api.vo.compute.QtcccptjVo;
import com.apex.sdx.api.vo.query.CpccsdxVo;
import com.apex.sdx.core.mybatis.entity.TsdxjgQtcccp;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface TsdxjgQtcccpService extends IService<TsdxjgQtcccp> {

    /**
     * 根据客户号和数据类型查询
     * @param khh
     * @param sjlx
     * @param onlysdx
     * @return
     */
    List<CpccsdxVo> selectByKhhSjlxAndSdxjg(String khh, Integer sjlx, boolean onlysdx,String rq);

    List<QtcccptjVo> compute(String khh, Integer ywxt, String rq);

}
