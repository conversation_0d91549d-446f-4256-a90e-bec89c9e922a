package com.apex.sdx.core.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.incrementer.IKeyGenerator;

public class DBKeyGenerator implements IKeyGenerator {

    /**
     * 执行 key 生成 SQL
     *
     * @param incrementerName 序列名称(对应类上注解 {KeySequence#value()} 的值)
     * @return sql
     */
    @Override
    public String executeSql(String incrementerName) {
        return "SELECT livebos.FUNC_NEXTID('"+incrementerName+"') FROM DUAL";
    }

    @Override
    public DbType dbType() {
        return null;
    }
}
