<template>
  <!-- 服务缓存刷新-->
  <div class="fwhcsx_main">
    <a-card title="服务缓存刷新" :bordered="false" class="fwhcsx_card">
      <a-checkbox-group v-model="value">
        <a-row>
          <a-col :span="24">
            <a-checkbox value="2">刷新系统参数</a-checkbox>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-checkbox value="1">刷新系统字典</a-checkbox>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-checkbox value="3">刷新平台系统参数</a-checkbox>
          </a-col>
        </a-row>
      </a-checkbox-group>
      <br><br>
      <a-row>
        <a-col :span="12">
          <a-button type="primary" :loading="refreshAllLoading" @click="refreshAll">刷新所有</a-button>
        </a-col>
        <a-col :span="12">
          <a-button type="primary" :loading="refreshLoading" @click="refresh">确定</a-button>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script>
import dict from "@/api/xtgl/dict";
export default {
  name: "fwhcsx",
  data() {
    return {
      value: [],
      refreshLoading: false,
      refreshAllLoading: false
    };
  },
  methods:{
    refreshAll(){
      dict.refresh("").then((ret)=>{
        this.$message.success(ret.note, 3);
      })
    },
    refresh(){
      if(this.value.length===0) {
        this.$message.warning("请选择你要操作的类型", 3);
        return;
      }
      dict.refresh(this.value.join(";")).then((ret)=>{
        this.$message.success(ret.note, 3);
      })
    }
  }
}
</script>

<style scoped>
.fwhcsx_main{
  padding: 30px;
  display: flex;
}
.fwhcsx_card{
  width: 300px;
  margin: auto;
  border: 1px solid #e0dcdc;
}
</style>