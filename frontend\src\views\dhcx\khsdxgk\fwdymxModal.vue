<template>
  <modal-component
    :title="title"
    :open="isOpen"
    :height="600"
    width="1050px"
    :on-cancel="handleOk"
  >
    <template #content>
      <div class="modal-container">
        <div>您的适当性基本属性信息</div>
        <a-row>
          <a-col :span="6">
            <div>风险承受能力 :</div>
            <div style="font-weight: 400;font-size: 34px;color: #BF935F;line-height: 36px">{{ getDcNote("SDX_FXCSNL", khjbxx?.cpdj, dictArray) }}</div>
          </a-col>
          <a-col :span="6" style="border-left: #EEEEEE solid 1px;padding-left: 20px">
            <div>测评得分 :</div>
            <div><span style="font-weight: 400;font-size: 34px;color: #BF935F;line-height: 36px">{{ khjbxx?.cpdj }}</span>分</div>
          </a-col>
          <a-col :span="12">
            <div style="background: #F5F9FC;border-radius: 4px; padding: 0 10px">
              <div>投资品种 : <span
                style="font-size: 14px;color: #333333;">{{ getDcNote("SDX_TZPZ", khjbxx?.tzpz, dictArray) }}</span>
              </div>
              <div>投资期限 : <span style="font-size: 14px;color: #333333;">{{ getDcNote("SDX_TZQX", khjbxx?.tzqx, dictArray) }}</span></div>
              <div>预期收益 : <span style="font-size: 14px;color: #333333;">{{ getDcNote("SDX_YQSY", khjbxx?.yqsy, dictArray) }}</span></div>
            </div>
          </a-col>
        </a-row>
        <a-tabs v-model:activeKey="activeKey" size="small">
          <template #rightExtra>
            <div>
              <a-switch v-model:checked="checked" style="margin: 0 10px 0 20px"/>
              <span style="font-weight: 400;font-size: 14px;color: #333333; line-height: 55px">仅看不适当</span>
            </div>
          </template>
          <a-tab-pane key="1004" tab="投顾服务">
            <a-table sticky :columns="columns" :data-source="tgfwData" :pagination="false" :loading="loading"></a-table>
          </a-tab-pane>
          <a-tab-pane key="1005" tab="公募投顾">
            <a-table sticky :columns="columns" :data-source="gmtgData" :pagination="false" :loading="loading"></a-table>
          </a-tab-pane>
        </a-tabs>
      </div>
    </template>
  </modal-component>
</template>
<script>
import {defineComponent} from "vue";
import ModalComponent from "@components/ModalComponent.vue";
import {commonApi} from "@/api/common";
import {message} from "ant-design-vue";
import {formatDate, getDcNote} from "@utils/bussinessUtils";

export default defineComponent({
  name: "fwdymxModal",
  components: { ModalComponent },
  inject: ["khh", "LastTradingDay"],
  props: {
    open: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      required: true
    },
    dictArray: {
      required: true
    },
    khjbxx: {
      required: true
    }
  },
  data() {
    return {
      activeKey: "1004",
      columns: [
        { title: "产品名称", dataIndex: "cpmc", key: "cpmc" },
        { title: "产品风险等级", dataIndex: "cpfxdj", key: "cpfxdj",
          customRender: ({text})=> {
            return getDcNote("SDX_CPFXDJ", text, this.dictArray)
          }
        },
        { title: "风险等级适当性", dataIndex: "fxdjsdx", key: "fxdjsdx",
          customRender: ({text}) => {
            return getDcNote("SDX_PPJG", text, this.dictArray);
          }
        },
        { title: "适当性结果", dataIndex: "sdxjg", key: "sdxjg",
          customRender: ({text}) => {
            return getDcNote("SDX_PPJG", text, this.dictArray);
          }
        },
      ],
      tgfwData: [],
      gmtgData: [],
      checked: false,
      isOpen: this.open,
      sdxjg: null,
      loading: false,
    };
  },
  watch: {
    open(newVal) {
      this.isOpen = newVal
      this.getKfsjjcc();
    },
    activeKey(newVal) {
      this.getKfsjjcc();
    },
    checked(newVal) {
      this.getKfsjjcc();
    }
  },
  mounted() {

  },
  methods: {
    getDcNote,
    handleOk(){
      this.isOpen = false;
      this.$emit("update:open", false);
    },
    getKfsjjcc(){
      this.loading = true;
      commonApi.executeAMS(
        "sdx.query.ICxtgcpsdxService",
        "tgcpsdxcx",
        { khh: this.khh, sjlx: this.activeKey, onlysdx: this.checked, rq: this.LastTradingDay},
      ).then((res) => {
        if (res.code < 0) {
          message.error(res.note);
        }
        if (this.activeKey === "1004") {
          this.tgfwData = res.records || [];
        } else if (this.activeKey === "1005"){
          this.gmtgData = res.records || [];
        }

      }).finally(() => {
        this.loading = false;
      })
    }
  }
});
</script>
<style scoped>
.modal-container {
  padding: 10px 10px 10px 20px;
  background-color: #FFFFFF;
  height: 100%;
  border-radius: 4px;
  line-height: 30px;
  color: #888888;
  overflow-y: auto;
}

:deep(.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
  color: #B48A3B;
}

:deep(.ant-tabs .ant-tabs-ink-bar) {
  background: #B48A3B
}
</style>