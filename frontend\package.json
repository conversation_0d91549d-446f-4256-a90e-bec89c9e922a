{"name": "vue-sdx", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.6", "@vueuse/core": "^12.7.0", "axios": "^1.7.4", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "jquery": "^3.6.4", "lodash": "^4.17.21", "vue": "^3.2.13", "vue-router": "^4.0.3", "vuex": "^4.0.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@antv/x6": "^2.0.0", "@antv/x6-vue-shape": "^2.0.0", "@types/node": "^22.10.5", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}