package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

/**
 * 
 * @TableName tsdxjg_qtcccp
 */
@TableName(value ="tsdxjg_qtcccp",schema = "sdx")
public class TsdxjgQtcccp implements Serializable {
    /**
     * 
     */
    private Integer rq;

    /**
     * 
     */
    private String khh;

    /**
     * 
     */
    private String cpdm;

    /**
     * 
     */
    private Integer sjly;

    /**
     * 
     */
    private Long khfxdj;

    /**
     * 
     */
    private String khtzpz;

    /**
     * 
     */
    private Long khtzqx;

    /**
     * 
     */
    private String khyqsy;

    /**
     * 
     */
    private Long cpfxdj;

    /**
     * 
     */
    private Long cptzpz;

    /**
     * 
     */
    private Long cptzqx;

    /**
     * 
     */
    private Long cpyqsy;

    /**
     * 
     */
    private Long fxdjsdx;

    /**
     * 
     */
    private Long tzpzsdx;

    /**
     * 
     */
    private Long tzqxsdx;

    /**
     * 
     */
    private Long yqsysdx;

    /**
     * 
     */
    private Long sdxjg;

    /**
     * 
     */
    private Long yyb;

    /**
     * 
     */
    private Integer kcrq;

    /**
     * TA代码
     */
    private String tadm;

    /**
     * TA名称
     */
    private String tamc;

    /**
     * 基金账号
     */
    private String jjzh;

    /**
     * 交易账号
     */
    private String jyzh;

    /**
     * 产品名称
     */
    private String cpmc;

    /**
     * 产品状态
     */
    private Long cpzt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public Integer getRq() {
        return rq;
    }

    /**
     * 
     */
    public void setRq(Integer rq) {
        this.rq = rq;
    }

    /**
     * 
     */
    public String getKhh() {
        return khh;
    }

    /**
     * 
     */
    public void setKhh(String khh) {
        this.khh = khh;
    }

    /**
     * 
     */
    public String getCpdm() {
        return cpdm;
    }

    /**
     * 
     */
    public void setCpdm(String cpdm) {
        this.cpdm = cpdm;
    }

    /**
     * 
     */
    public Integer getSjly() {
        return sjly;
    }

    /**
     * 
     */
    public void setSjly(Integer sjly) {
        this.sjly = sjly;
    }

    /**
     * 
     */
    public Long getKhfxdj() {
        return khfxdj;
    }

    /**
     * 
     */
    public void setKhfxdj(Long khfxdj) {
        this.khfxdj = khfxdj;
    }

    /**
     * 
     */
    public String getKhtzpz() {
        return khtzpz;
    }

    /**
     * 
     */
    public void setKhtzpz(String khtzpz) {
        this.khtzpz = khtzpz;
    }

    /**
     * 
     */
    public Long getKhtzqx() {
        return khtzqx;
    }

    /**
     * 
     */
    public void setKhtzqx(Long khtzqx) {
        this.khtzqx = khtzqx;
    }

    /**
     * 
     */
    public String getKhyqsy() {
        return khyqsy;
    }

    /**
     * 
     */
    public void setKhyqsy(String khyqsy) {
        this.khyqsy = khyqsy;
    }

    /**
     * 
     */
    public Long getCpfxdj() {
        return cpfxdj;
    }

    /**
     * 
     */
    public void setCpfxdj(Long cpfxdj) {
        this.cpfxdj = cpfxdj;
    }

    /**
     * 
     */
    public Long getCptzpz() {
        return cptzpz;
    }

    /**
     * 
     */
    public void setCptzpz(Long cptzpz) {
        this.cptzpz = cptzpz;
    }

    /**
     * 
     */
    public Long getCptzqx() {
        return cptzqx;
    }

    /**
     * 
     */
    public void setCptzqx(Long cptzqx) {
        this.cptzqx = cptzqx;
    }

    /**
     * 
     */
    public Long getCpyqsy() {
        return cpyqsy;
    }

    /**
     * 
     */
    public void setCpyqsy(Long cpyqsy) {
        this.cpyqsy = cpyqsy;
    }

    /**
     * 
     */
    public Long getFxdjsdx() {
        return fxdjsdx;
    }

    /**
     * 
     */
    public void setFxdjsdx(Long fxdjsdx) {
        this.fxdjsdx = fxdjsdx;
    }

    /**
     * 
     */
    public Long getTzpzsdx() {
        return tzpzsdx;
    }

    /**
     * 
     */
    public void setTzpzsdx(Long tzpzsdx) {
        this.tzpzsdx = tzpzsdx;
    }

    /**
     * 
     */
    public Long getTzqxsdx() {
        return tzqxsdx;
    }

    /**
     * 
     */
    public void setTzqxsdx(Long tzqxsdx) {
        this.tzqxsdx = tzqxsdx;
    }

    /**
     * 
     */
    public Long getYqsysdx() {
        return yqsysdx;
    }

    /**
     * 
     */
    public void setYqsysdx(Long yqsysdx) {
        this.yqsysdx = yqsysdx;
    }

    /**
     * 
     */
    public Long getSdxjg() {
        return sdxjg;
    }

    /**
     * 
     */
    public void setSdxjg(Long sdxjg) {
        this.sdxjg = sdxjg;
    }

    /**
     * 
     */
    public Long getYyb() {
        return yyb;
    }

    /**
     * 
     */
    public void setYyb(Long yyb) {
        this.yyb = yyb;
    }

    /**
     * 
     */
    public Integer getKcrq() {
        return kcrq;
    }

    /**
     * 
     */
    public void setKcrq(Integer kcrq) {
        this.kcrq = kcrq;
    }

    /**
     * TA代码
     */
    public String getTadm() {
        return tadm;
    }

    /**
     * TA代码
     */
    public void setTadm(String tadm) {
        this.tadm = tadm;
    }

    /**
     * TA名称
     */
    public String getTamc() {
        return tamc;
    }

    /**
     * TA名称
     */
    public void setTamc(String tamc) {
        this.tamc = tamc;
    }

    /**
     * 基金账号
     */
    public String getJjzh() {
        return jjzh;
    }

    /**
     * 基金账号
     */
    public void setJjzh(String jjzh) {
        this.jjzh = jjzh;
    }

    /**
     * 交易账号
     */
    public String getJyzh() {
        return jyzh;
    }

    /**
     * 交易账号
     */
    public void setJyzh(String jyzh) {
        this.jyzh = jyzh;
    }

    /**
     * 产品名称
     */
    public String getCpmc() {
        return cpmc;
    }

    /**
     * 产品名称
     */
    public void setCpmc(String cpmc) {
        this.cpmc = cpmc;
    }

    /**
     * 产品状态
     */
    public Long getCpzt() {
        return cpzt;
    }

    /**
     * 产品状态
     */
    public void setCpzt(Long cpzt) {
        this.cpzt = cpzt;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TsdxjgQtcccp other = (TsdxjgQtcccp) that;
        return (this.getRq() == null ? other.getRq() == null : this.getRq().equals(other.getRq()))
            && (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
            && (this.getCpdm() == null ? other.getCpdm() == null : this.getCpdm().equals(other.getCpdm()))
            && (this.getSjly() == null ? other.getSjly() == null : this.getSjly().equals(other.getSjly()))
            && (this.getKhfxdj() == null ? other.getKhfxdj() == null : this.getKhfxdj().equals(other.getKhfxdj()))
            && (this.getKhtzpz() == null ? other.getKhtzpz() == null : this.getKhtzpz().equals(other.getKhtzpz()))
            && (this.getKhtzqx() == null ? other.getKhtzqx() == null : this.getKhtzqx().equals(other.getKhtzqx()))
            && (this.getKhyqsy() == null ? other.getKhyqsy() == null : this.getKhyqsy().equals(other.getKhyqsy()))
            && (this.getCpfxdj() == null ? other.getCpfxdj() == null : this.getCpfxdj().equals(other.getCpfxdj()))
            && (this.getCptzpz() == null ? other.getCptzpz() == null : this.getCptzpz().equals(other.getCptzpz()))
            && (this.getCptzqx() == null ? other.getCptzqx() == null : this.getCptzqx().equals(other.getCptzqx()))
            && (this.getCpyqsy() == null ? other.getCpyqsy() == null : this.getCpyqsy().equals(other.getCpyqsy()))
            && (this.getFxdjsdx() == null ? other.getFxdjsdx() == null : this.getFxdjsdx().equals(other.getFxdjsdx()))
            && (this.getTzpzsdx() == null ? other.getTzpzsdx() == null : this.getTzpzsdx().equals(other.getTzpzsdx()))
            && (this.getTzqxsdx() == null ? other.getTzqxsdx() == null : this.getTzqxsdx().equals(other.getTzqxsdx()))
            && (this.getYqsysdx() == null ? other.getYqsysdx() == null : this.getYqsysdx().equals(other.getYqsysdx()))
            && (this.getSdxjg() == null ? other.getSdxjg() == null : this.getSdxjg().equals(other.getSdxjg()))
            && (this.getYyb() == null ? other.getYyb() == null : this.getYyb().equals(other.getYyb()))
            && (this.getKcrq() == null ? other.getKcrq() == null : this.getKcrq().equals(other.getKcrq()))
            && (this.getTadm() == null ? other.getTadm() == null : this.getTadm().equals(other.getTadm()))
            && (this.getTamc() == null ? other.getTamc() == null : this.getTamc().equals(other.getTamc()))
            && (this.getJjzh() == null ? other.getJjzh() == null : this.getJjzh().equals(other.getJjzh()))
            && (this.getJyzh() == null ? other.getJyzh() == null : this.getJyzh().equals(other.getJyzh()))
            && (this.getCpmc() == null ? other.getCpmc() == null : this.getCpmc().equals(other.getCpmc()))
            && (this.getCpzt() == null ? other.getCpzt() == null : this.getCpzt().equals(other.getCpzt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getRq() == null) ? 0 : getRq().hashCode());
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getCpdm() == null) ? 0 : getCpdm().hashCode());
        result = prime * result + ((getSjly() == null) ? 0 : getSjly().hashCode());
        result = prime * result + ((getKhfxdj() == null) ? 0 : getKhfxdj().hashCode());
        result = prime * result + ((getKhtzpz() == null) ? 0 : getKhtzpz().hashCode());
        result = prime * result + ((getKhtzqx() == null) ? 0 : getKhtzqx().hashCode());
        result = prime * result + ((getKhyqsy() == null) ? 0 : getKhyqsy().hashCode());
        result = prime * result + ((getCpfxdj() == null) ? 0 : getCpfxdj().hashCode());
        result = prime * result + ((getCptzpz() == null) ? 0 : getCptzpz().hashCode());
        result = prime * result + ((getCptzqx() == null) ? 0 : getCptzqx().hashCode());
        result = prime * result + ((getCpyqsy() == null) ? 0 : getCpyqsy().hashCode());
        result = prime * result + ((getFxdjsdx() == null) ? 0 : getFxdjsdx().hashCode());
        result = prime * result + ((getTzpzsdx() == null) ? 0 : getTzpzsdx().hashCode());
        result = prime * result + ((getTzqxsdx() == null) ? 0 : getTzqxsdx().hashCode());
        result = prime * result + ((getYqsysdx() == null) ? 0 : getYqsysdx().hashCode());
        result = prime * result + ((getSdxjg() == null) ? 0 : getSdxjg().hashCode());
        result = prime * result + ((getYyb() == null) ? 0 : getYyb().hashCode());
        result = prime * result + ((getKcrq() == null) ? 0 : getKcrq().hashCode());
        result = prime * result + ((getTadm() == null) ? 0 : getTadm().hashCode());
        result = prime * result + ((getTamc() == null) ? 0 : getTamc().hashCode());
        result = prime * result + ((getJjzh() == null) ? 0 : getJjzh().hashCode());
        result = prime * result + ((getJyzh() == null) ? 0 : getJyzh().hashCode());
        result = prime * result + ((getCpmc() == null) ? 0 : getCpmc().hashCode());
        result = prime * result + ((getCpzt() == null) ? 0 : getCpzt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", rq=").append(rq);
        sb.append(", khh=").append(khh);
        sb.append(", cpdm=").append(cpdm);
        sb.append(", sjly=").append(sjly);
        sb.append(", khfxdj=").append(khfxdj);
        sb.append(", khtzpz=").append(khtzpz);
        sb.append(", khtzqx=").append(khtzqx);
        sb.append(", khyqsy=").append(khyqsy);
        sb.append(", cpfxdj=").append(cpfxdj);
        sb.append(", cptzpz=").append(cptzpz);
        sb.append(", cptzqx=").append(cptzqx);
        sb.append(", cpyqsy=").append(cpyqsy);
        sb.append(", fxdjsdx=").append(fxdjsdx);
        sb.append(", tzpzsdx=").append(tzpzsdx);
        sb.append(", tzqxsdx=").append(tzqxsdx);
        sb.append(", yqsysdx=").append(yqsysdx);
        sb.append(", sdxjg=").append(sdxjg);
        sb.append(", yyb=").append(yyb);
        sb.append(", kcrq=").append(kcrq);
        sb.append(", tadm=").append(tadm);
        sb.append(", tamc=").append(tamc);
        sb.append(", jjzh=").append(jjzh);
        sb.append(", jyzh=").append(jyzh);
        sb.append(", cpmc=").append(cpmc);
        sb.append(", cpzt=").append(cpzt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}