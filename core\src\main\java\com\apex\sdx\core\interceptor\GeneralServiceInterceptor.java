package com.apex.sdx.core.interceptor;

import com.alibaba.fastjson.JSON;
import com.apex.sdx.api.resp.common.Response;
import com.apex.sdx.core.Constants;
import com.apex.sdx.core.exception.BusinessException;
import com.apexsoft.*;
import sdx.common.IService;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.InvocationTargetException;

/**
 * 一般服务拦截器
 *
 * <AUTHOR>
 */
@LiveServiceInterceptor(isGlobal = false)
@Slf4j
public class GeneralServiceInterceptor implements ILiveServiceInterceptor {


    /**
     * @param callable      服务处理函数
     * @param serviceInfo   服务信息
     * @param method        方法信息
     * @param targetService 服务对象
     * @param paramValues   方法参数
     * @param protocol      RPC协议
     * @return
     * @throws Exception
     */
    @Override
    public <T> T intercept(LiveServiceCallable<T> callable, ServiceInfo serviceInfo, InterfaceMethodInfo method,
                           Object targetService, Object[] paramValues, RPCProtocol protocol) throws Exception {
        String func = serviceInfo.getServiceId();
        if ("check".equals(method.getMethodName())) {
            return (T) this.responseInvoke(callable, func, "check");
        } else if ("execute".equals(method.getMethodName())) {
            return this.executeInvoke(callable, serviceInfo, targetService, paramValues, func);
        } else {
            return (T) Response.builder()
                    .code(-1)
                    .errorCode(func + "-001")
                    .note("服务调用异常，方法[" + method.getMethodName() + "]不存在")
                    .clzt(Constants.SERVICE_STATUS_CLSB)
                    .build();
        }
    }

    private <T> T executeInvoke(LiveServiceCallable<T> callable, ServiceInfo serviceInfo, Object targetService,
                                Object[] paramValues, String func) {
        try {
            // 执行excute业务处理前，先执行check业务检查
            Response checkResp = this.checkInvoke(serviceInfo, targetService, paramValues, func);
            // check失败时返回
            if (checkResp != null && checkResp.getCode() < 0) {
                return (T) checkResp;
            }
            // 检查成功执行execute
            return (T) this.responseInvoke(callable, func, "execute");
        } catch (Exception e) {
            log.error("[" + func + "]" + e.getMessage(), e);
            throw new RuntimeException("[" + func + "]" + e.getMessage());
        }
    }

    /**
     * 调用本服务的check方法
     *
     * @return
     */
    private Response checkInvoke(ServiceInfo serviceInfo, Object targetService, Object[] paramValues, String func) {
        try {
            IService ygtService = (IService) targetService;
            Object obj = ygtService.check(paramValues[0]);
            if (obj == null) {
                return Response.builder().code(1).note("业务检查通过").build();
            }
            Response resp = (Response) obj;
            int code = resp.getCode();
            if (resp.getClzt() == 0) {
                if (code > 0) {
                    resp.setClzt(Constants.SERVICE_STATUS_CLCG);
                } else {
                    resp.setClzt(Constants.SERVICE_STATUS_CLSB);
                }
            }
            if (code <= 0) {
                resp.setErrorCode(func + code);
            }
            return resp;
        } catch (BusinessException e) {
            return Response.builder()
                    .code(e.getCode())
                    .note(e.getMessage())
                    .errorCode(func + e.getCode())
                    .clzt(Constants.SERVICE_STATUS_CLSB)
                    .build();
        } catch (Exception e) {
            String msg = "";
            if (e instanceof InvocationTargetException) {
                msg = ((InvocationTargetException) e).getTargetException().getMessage();
            } else {
                msg = e.getMessage();
            }
            String note = String.format("业务执行前检查异常，调用服务[%s]，请求数据[%s]，异常信息[%s]", func, JSON.toJSONString(paramValues[0]), msg);
            log.error(note, e);
            return Response.builder()
                    .code(-1)
                    .note("业务执行前检查异常:" + msg)
                    .errorCode(func + "-001")
                    .clzt(Constants.SERVICE_STATUS_CLSB)
                    .build();
        }
    }

    /**
     * 进入execute/check拦截器
     */
    private <T> Response responseInvoke(LiveServiceCallable<T> callable, String func, String methodName) {
        try {
            Response resp = (Response) callable.call();
            if (resp == null && "check".equals(methodName)) {
                return Response.builder().code(1).note("业务检查通过").build();
            }
            if (resp != null) {
                int code = resp.getCode();
                if (resp.getClzt() == 0) {
                    if (code > 0) {
                        resp.setClzt(Constants.SERVICE_STATUS_CLCG);
                    } else {
                        resp.setClzt(Constants.SERVICE_STATUS_CLSB);
                    }
                }
                if (code <= 0 && resp.getErrorCode() == null) {
                    resp.setErrorCode(func + code);
                }
            }
            return resp;
        } catch (BusinessException e) {
            return Response.builder()
                    .code(e.getCode())
                    .note(e.getMessage())
                    .errorCode(func + e.getCode())
                    .clzt(Constants.SERVICE_STATUS_CLSB)
                    .build();
        } catch (Throwable e) {
            String note = "业务处理失败：";
            if (e instanceof InvocationTargetException) {
                note += ((InvocationTargetException) e).getTargetException().getMessage();
            } else {
                note += e.getMessage();
            }
            log.error(note, e);
            return Response.builder()
                    .code(-1)
                    .note(note)
                    .errorCode(func + "-001")
                    .clzt(Constants.SERVICE_STATUS_CLSB)
                    .build();
        }
    }
}
