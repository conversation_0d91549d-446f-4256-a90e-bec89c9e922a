package com.apex.sdx.api.vo.query;

import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025-03-07
 * @Description:
 */
@Setter
@Getter
public class XyzhVo {
    @LiveProperty(note = "信用等级", index = 1)
    private String xydj;

    @LiveProperty(note = "等级说明", index = 2)
    private String djsm;

    @LiveProperty(note = "授信系数", index = 3)
    private String sxxs;

    @LiveProperty(note = "合同融资额度", index = 4)
    private BigDecimal htrzed;

    @LiveProperty(note = "合同融券额度", index = 5)
    private BigDecimal htrqed;

    @LiveProperty(note = "最大融资授信额度", index = 6)
    private BigDecimal djrzsxed;

    @LiveProperty(note = "最大融券授信额度", index = 7)
    private BigDecimal djrqsxed;

    @LiveProperty(note = "信用专属邮箱", index = 8)
    private String email;

    @LiveProperty(note = "评级总分", index = 9)
    private BigDecimal pjzf;

    @LiveProperty(note = "业务账号", index = 10)
    private String ywzh;

    @LiveProperty(note = "关联业务账号", index = 11)
    private String glywzh;

    @LiveProperty(note = "融资授信额度", index = 12)
    private BigDecimal rzsxed;

    @LiveProperty(note = "融券授信额度", index = 13)
    private BigDecimal rqsxed;

    @LiveProperty(note = "授信总额度", index = 14)
    private BigDecimal sxzed;

    @LiveProperty(note = "合同编号", index = 15)
    private String htbh;

    @LiveProperty(note = "辅助利率模板", index = 16)
    private Integer fzllmb;

    @LiveProperty(note = "利率了结方式", index = 17)
    private Integer lxljfs;

    @LiveProperty(note = "币种", index = 18)
    private Integer bz;

    @LiveProperty(note = "合同状态", index = 19)
    private Integer htzt;

    @LiveProperty(note = "开始日期", index = 20)
    private Integer ksrq;

    @LiveProperty(note = "结束日期", index = 21)
    private Integer jsrq;

    @LiveProperty(note = "合同控制属性", index = 22)
    private String htsx;

    @LiveProperty(note = "客户号", index = 23)
    private String khh;
}
