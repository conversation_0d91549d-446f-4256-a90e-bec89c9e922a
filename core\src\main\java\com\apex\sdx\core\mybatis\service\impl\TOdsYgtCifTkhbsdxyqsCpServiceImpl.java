package com.apex.sdx.core.mybatis.service.impl;

import com.apex.sdx.api.vo.query.CpbsdxyqslscxVo;
import com.apex.sdx.core.exception.BusinessException;
import com.apex.sdx.core.mybatis.entity.TOdsYgtCifTkhbsdxyqsCp;
import com.apex.sdx.core.mybatis.mapper.TOdsYgtCifTkhbsdxyqsCpMapper;
import com.apex.sdx.core.mybatis.service.TOdsYgtCifTkhbsdxyqsCpService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_ods_ygt_cif_tkhbsdxyqs_cp】的数据库操作Service实现
 * @createDate 2025-06-06 16:59:26
 */
@Service
public class TOdsYgtCifTkhbsdxyqsCpServiceImpl extends ServiceImpl<TOdsYgtCifTkhbsdxyqsCpMapper, TOdsYgtCifTkhbsdxyqsCp>
        implements TOdsYgtCifTkhbsdxyqsCpService {

    @Override
    public List<CpbsdxyqslscxVo> cpbsdxyqslscx(String khh,String ksrq, String jsrq, String cpdm) {
        try {
            return this.baseMapper.cpbsdxyqslscx(khh,ksrq, jsrq, cpdm);
        } catch (Exception e) {
            String note = String.format("查询产品不适当协议签署流水异常:%s", e.getMessage());
            log.error(note, e);
            throw new BusinessException(-601, note);
        }
    }
}




