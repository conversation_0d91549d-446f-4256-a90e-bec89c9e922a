package com.apex.sdx.api.req.query;

import com.apex.sdx.api.req.common.PageRequest;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2025-03-04
 * @Description:
 */
@Getter
@Setter
public class SdxsjcxReq extends PageRequest {
    @LiveProperty(note = "客户号", index = 1)
    private String khh;

    @LiveProperty(note = "处理状态", index = 2)
    private String clzt;

    @LiveProperty(note = "起始日期", index = 3)
    private String ksrq;
    @LiveProperty(note = "结束日期", index = 4)
    private String jsrq;

}
