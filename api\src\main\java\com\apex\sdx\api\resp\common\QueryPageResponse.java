package com.apex.sdx.api.resp.common;

import com.apexsoft.LiveProperty;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 通用查询服务响应对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class QueryPageResponse<T> extends R {

    public QueryPageResponse() {
        this.setCode(-1);
        this.setNote("");
    }

    public QueryPageResponse(int code) {
        this.setCode(code);
    }

    public QueryPageResponse(int code, String note) {
        this.setCode(code);
        this.setNote(note);
    }

    public void setRecords(List<T> records) {
        this.records = records;
        this.count = records.size();
    }

    /**
     * 查询数据集
     */
    @LiveProperty(note = "查询数据集", index = 1001)
    private List<T> records;

    /**
     * 查询数据集记录数
     */
    @LiveProperty(note = "查询数据集记录数", index = 1002)
    private int count;

    /**
     * 分页总记录数
     */
    @LiveProperty(note = "分页总记录数", index = 1003)
    private long total = 0L;

    /**
     * 当前分页数
     */
    @LiveProperty(note = "当前分页数", index = 1004)
    private long curpage = 1L;

    /**
     * 每页显示记录数
     */
    @LiveProperty(note = "每页显示记录数", index = 1005)
    private long pagesize = 10L;

    /**
     * 总分页数
     */
    @LiveProperty(note = "总分页数", index = 1006)
    private long pages = 1;

    /**
     * 是否有下一页，0|否;1|是
     */
    @LiveProperty(note = "是否有下一页，0|否;1|是", index = 1007)
    private int hasnext = 0;

    /**
     * 构造返回的分页信息
     *
     * @param records
     * @param page
     */
    public void buildPage(List<T> records, Page page) {
        this.setRecords(records);
        this.setTotal(page.getTotal());
        this.setCurpage(page.getCurrent());
        this.setPagesize(page.getSize());
        this.setPages(page.getPages());
        if (page.hasNext()) {
            this.setHasnext(1);
        }
    }

    public QueryPageResponse<T> page(Page<T> page) {
        this.setTotal(page.getTotal());
        this.setCurpage(page.getCurrent());
        this.setPagesize(page.getSize());
        this.setPages(page.getPages());
        this.setRecords(page.getRecords());
        this.setCount(page.getRecords().size());
        if (page.hasNext()) {
            this.setHasnext(1);
        }
        return this;
    }
}
