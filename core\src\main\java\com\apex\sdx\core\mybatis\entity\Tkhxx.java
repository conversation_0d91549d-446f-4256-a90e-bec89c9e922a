package com.apex.sdx.core.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 
 * @TableName tkhxx
 */
@TableName(value ="tkhxx")
@Data
public class Tkhxx implements Serializable {
    /**
     * 客户号
     */
    @TableId
    private String khh;

    /**
     * 客户简称
     */
    private String khjc;

    /**
     * 客户名称
     */
    private String khmc;

    /**
     * 数据字典: GT_KHZT
     */
    private Integer khzt;

    /**
     * 营业部
     */
    private Integer yyb;

    /**
     * 客户群组
     */
    private Integer khqz;

    /**
     * 数据字典: GT_KHLB
     */
    private Integer khlb;

    /**
     * 数据字典: KH_KHFS
     */
    private Integer khfs;

    /**
     * 开户日期
     */
    private Integer khrq;

    /**
     * 数据字典: GT_ZJLB
     */
    private Integer zjlb;

    /**
     * 证件编号
     */
    private String zjbh;

    /**
     * 证件起始日期
     */
    private Integer zjqsrq;

    /**
     * 30001231代表长期
     */
    private Integer zjjzrq;

    /**
     * 证件地址
     */
    private String zjdz;

    /**
     * 证件地址邮编
     */
    private String zjdzyb;

    /**
     * 证件发证机关
     */
    private String zjfzjg;

    /**
     * 地址
     */
    private String dz;

    /**
     * 邮政编码
     */
    private String yzbm;

    /**
     * 电话
     */
    private String dh;

    /**
     * 手机
     */
    private String sj;

    /**
     * 传真
     */
    private String cz;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 国籍 数据字典: GJ
     */
    private Integer gj;

    /**
     * 一户通号签约标志
     */
    private Integer yhtqybz;

    /**
     * 一户通号签约日期
     */
    private Integer yhtqyrq;

    /**
     * 一码通号
     */
    private String ymth;

    /**
     * 读卡标志
     */
    private Integer dkbz;

    /**
     * 税收居民身份
     */
    private Integer ssjmsf;

    /**
     * 一户通号
     */
    private Integer cid;

    /**
     * 反洗钱行业类别 数据字典：GT_FXQHYLB
     */
    private Integer fxqhylb;

    /**
     * 洗钱风险等级 数据字典：GT_XQFXDJ
     */
    private Integer xqfxdj;

    /**
     * 反洗钱设置日期
     */
    private Integer fxqszrq;

    /**
     * 投资者分类 数据字典：TZZFL
     */
    private Integer tzzfl;

    /**
     * 投资品种
     */
    private String tzpz;

    /**
     * 投资期限 数据字典：TZQX
     */
    private Integer tzqx;

    /**
     * 预期收益
     */
    private String yqsy;

    /**
     * 投资者判定日期
     */
    private Integer tzzpdrq;

    /**
     * 投资者判定有效期
     */
    private Integer tzzpdyxq;

    /**
     * 不良诚信记录
     */
    private String blcxjl;

    /**
     * 其他反洗钱行业类别信息
     */
    private String qtfxqhylbxx;

    /**
     * 测评等级
     */
    private Integer cpdj;

    /**
     * 测评得分
     */
    private BigDecimal cpdf;

    /**
     * 测评日期
     */
    private Integer cprq;

    /**
     * 测评有效期
     */
    private Integer cpyxq;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Tkhxx other = (Tkhxx) that;
        return (this.getKhh() == null ? other.getKhh() == null : this.getKhh().equals(other.getKhh()))
            && (this.getKhjc() == null ? other.getKhjc() == null : this.getKhjc().equals(other.getKhjc()))
            && (this.getKhmc() == null ? other.getKhmc() == null : this.getKhmc().equals(other.getKhmc()))
            && (this.getKhzt() == null ? other.getKhzt() == null : this.getKhzt().equals(other.getKhzt()))
            && (this.getYyb() == null ? other.getYyb() == null : this.getYyb().equals(other.getYyb()))
            && (this.getKhqz() == null ? other.getKhqz() == null : this.getKhqz().equals(other.getKhqz()))
            && (this.getKhlb() == null ? other.getKhlb() == null : this.getKhlb().equals(other.getKhlb()))
            && (this.getKhfs() == null ? other.getKhfs() == null : this.getKhfs().equals(other.getKhfs()))
            && (this.getKhrq() == null ? other.getKhrq() == null : this.getKhrq().equals(other.getKhrq()))
            && (this.getZjlb() == null ? other.getZjlb() == null : this.getZjlb().equals(other.getZjlb()))
            && (this.getZjbh() == null ? other.getZjbh() == null : this.getZjbh().equals(other.getZjbh()))
            && (this.getZjqsrq() == null ? other.getZjqsrq() == null : this.getZjqsrq().equals(other.getZjqsrq()))
            && (this.getZjjzrq() == null ? other.getZjjzrq() == null : this.getZjjzrq().equals(other.getZjjzrq()))
            && (this.getZjdz() == null ? other.getZjdz() == null : this.getZjdz().equals(other.getZjdz()))
            && (this.getZjdzyb() == null ? other.getZjdzyb() == null : this.getZjdzyb().equals(other.getZjdzyb()))
            && (this.getZjfzjg() == null ? other.getZjfzjg() == null : this.getZjfzjg().equals(other.getZjfzjg()))
            && (this.getDz() == null ? other.getDz() == null : this.getDz().equals(other.getDz()))
            && (this.getYzbm() == null ? other.getYzbm() == null : this.getYzbm().equals(other.getYzbm()))
            && (this.getDh() == null ? other.getDh() == null : this.getDh().equals(other.getDh()))
            && (this.getSj() == null ? other.getSj() == null : this.getSj().equals(other.getSj()))
            && (this.getCz() == null ? other.getCz() == null : this.getCz().equals(other.getCz()))
            && (this.getEmail() == null ? other.getEmail() == null : this.getEmail().equals(other.getEmail()))
            && (this.getGj() == null ? other.getGj() == null : this.getGj().equals(other.getGj()))
            && (this.getYhtqybz() == null ? other.getYhtqybz() == null : this.getYhtqybz().equals(other.getYhtqybz()))
            && (this.getYhtqyrq() == null ? other.getYhtqyrq() == null : this.getYhtqyrq().equals(other.getYhtqyrq()))
            && (this.getYmth() == null ? other.getYmth() == null : this.getYmth().equals(other.getYmth()))
            && (this.getDkbz() == null ? other.getDkbz() == null : this.getDkbz().equals(other.getDkbz()))
            && (this.getSsjmsf() == null ? other.getSsjmsf() == null : this.getSsjmsf().equals(other.getSsjmsf()))
            && (this.getCid() == null ? other.getCid() == null : this.getCid().equals(other.getCid()))
            && (this.getFxqhylb() == null ? other.getFxqhylb() == null : this.getFxqhylb().equals(other.getFxqhylb()))
            && (this.getXqfxdj() == null ? other.getXqfxdj() == null : this.getXqfxdj().equals(other.getXqfxdj()))
            && (this.getFxqszrq() == null ? other.getFxqszrq() == null : this.getFxqszrq().equals(other.getFxqszrq()))
            && (this.getTzzfl() == null ? other.getTzzfl() == null : this.getTzzfl().equals(other.getTzzfl()))
            && (this.getTzpz() == null ? other.getTzpz() == null : this.getTzpz().equals(other.getTzpz()))
            && (this.getTzqx() == null ? other.getTzqx() == null : this.getTzqx().equals(other.getTzqx()))
            && (this.getYqsy() == null ? other.getYqsy() == null : this.getYqsy().equals(other.getYqsy()))
            && (this.getTzzpdrq() == null ? other.getTzzpdrq() == null : this.getTzzpdrq().equals(other.getTzzpdrq()))
            && (this.getTzzpdyxq() == null ? other.getTzzpdyxq() == null : this.getTzzpdyxq().equals(other.getTzzpdyxq()))
            && (this.getBlcxjl() == null ? other.getBlcxjl() == null : this.getBlcxjl().equals(other.getBlcxjl()))
            && (this.getQtfxqhylbxx() == null ? other.getQtfxqhylbxx() == null : this.getQtfxqhylbxx().equals(other.getQtfxqhylbxx()))
            && (this.getCpdj() == null ? other.getCpdj() == null : this.getCpdj().equals(other.getCpdj()))
            && (this.getCpdf() == null ? other.getCpdf() == null : this.getCpdf().equals(other.getCpdf()))
            && (this.getCprq() == null ? other.getCprq() == null : this.getCprq().equals(other.getCprq()))
            && (this.getCpyxq() == null ? other.getCpyxq() == null : this.getCpyxq().equals(other.getCpyxq()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getKhh() == null) ? 0 : getKhh().hashCode());
        result = prime * result + ((getKhjc() == null) ? 0 : getKhjc().hashCode());
        result = prime * result + ((getKhmc() == null) ? 0 : getKhmc().hashCode());
        result = prime * result + ((getKhzt() == null) ? 0 : getKhzt().hashCode());
        result = prime * result + ((getYyb() == null) ? 0 : getYyb().hashCode());
        result = prime * result + ((getKhqz() == null) ? 0 : getKhqz().hashCode());
        result = prime * result + ((getKhlb() == null) ? 0 : getKhlb().hashCode());
        result = prime * result + ((getKhfs() == null) ? 0 : getKhfs().hashCode());
        result = prime * result + ((getKhrq() == null) ? 0 : getKhrq().hashCode());
        result = prime * result + ((getZjlb() == null) ? 0 : getZjlb().hashCode());
        result = prime * result + ((getZjbh() == null) ? 0 : getZjbh().hashCode());
        result = prime * result + ((getZjqsrq() == null) ? 0 : getZjqsrq().hashCode());
        result = prime * result + ((getZjjzrq() == null) ? 0 : getZjjzrq().hashCode());
        result = prime * result + ((getZjdz() == null) ? 0 : getZjdz().hashCode());
        result = prime * result + ((getZjdzyb() == null) ? 0 : getZjdzyb().hashCode());
        result = prime * result + ((getZjfzjg() == null) ? 0 : getZjfzjg().hashCode());
        result = prime * result + ((getDz() == null) ? 0 : getDz().hashCode());
        result = prime * result + ((getYzbm() == null) ? 0 : getYzbm().hashCode());
        result = prime * result + ((getDh() == null) ? 0 : getDh().hashCode());
        result = prime * result + ((getSj() == null) ? 0 : getSj().hashCode());
        result = prime * result + ((getCz() == null) ? 0 : getCz().hashCode());
        result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
        result = prime * result + ((getGj() == null) ? 0 : getGj().hashCode());
        result = prime * result + ((getYhtqybz() == null) ? 0 : getYhtqybz().hashCode());
        result = prime * result + ((getYhtqyrq() == null) ? 0 : getYhtqyrq().hashCode());
        result = prime * result + ((getYmth() == null) ? 0 : getYmth().hashCode());
        result = prime * result + ((getDkbz() == null) ? 0 : getDkbz().hashCode());
        result = prime * result + ((getSsjmsf() == null) ? 0 : getSsjmsf().hashCode());
        result = prime * result + ((getCid() == null) ? 0 : getCid().hashCode());
        result = prime * result + ((getFxqhylb() == null) ? 0 : getFxqhylb().hashCode());
        result = prime * result + ((getXqfxdj() == null) ? 0 : getXqfxdj().hashCode());
        result = prime * result + ((getFxqszrq() == null) ? 0 : getFxqszrq().hashCode());
        result = prime * result + ((getTzzfl() == null) ? 0 : getTzzfl().hashCode());
        result = prime * result + ((getTzpz() == null) ? 0 : getTzpz().hashCode());
        result = prime * result + ((getTzqx() == null) ? 0 : getTzqx().hashCode());
        result = prime * result + ((getYqsy() == null) ? 0 : getYqsy().hashCode());
        result = prime * result + ((getTzzpdrq() == null) ? 0 : getTzzpdrq().hashCode());
        result = prime * result + ((getTzzpdyxq() == null) ? 0 : getTzzpdyxq().hashCode());
        result = prime * result + ((getBlcxjl() == null) ? 0 : getBlcxjl().hashCode());
        result = prime * result + ((getQtfxqhylbxx() == null) ? 0 : getQtfxqhylbxx().hashCode());
        result = prime * result + ((getCpdj() == null) ? 0 : getCpdj().hashCode());
        result = prime * result + ((getCpdf() == null) ? 0 : getCpdf().hashCode());
        result = prime * result + ((getCprq() == null) ? 0 : getCprq().hashCode());
        result = prime * result + ((getCpyxq() == null) ? 0 : getCpyxq().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", khh=").append(khh);
        sb.append(", khjc=").append(khjc);
        sb.append(", khmc=").append(khmc);
        sb.append(", khzt=").append(khzt);
        sb.append(", yyb=").append(yyb);
        sb.append(", khqz=").append(khqz);
        sb.append(", khlb=").append(khlb);
        sb.append(", khfs=").append(khfs);
        sb.append(", khrq=").append(khrq);
        sb.append(", zjlb=").append(zjlb);
        sb.append(", zjbh=").append(zjbh);
        sb.append(", zjqsrq=").append(zjqsrq);
        sb.append(", zjjzrq=").append(zjjzrq);
        sb.append(", zjdz=").append(zjdz);
        sb.append(", zjdzyb=").append(zjdzyb);
        sb.append(", zjfzjg=").append(zjfzjg);
        sb.append(", dz=").append(dz);
        sb.append(", yzbm=").append(yzbm);
        sb.append(", dh=").append(dh);
        sb.append(", sj=").append(sj);
        sb.append(", cz=").append(cz);
        sb.append(", email=").append(email);
        sb.append(", gj=").append(gj);
        sb.append(", yhtqybz=").append(yhtqybz);
        sb.append(", yhtqyrq=").append(yhtqyrq);
        sb.append(", ymth=").append(ymth);
        sb.append(", dkbz=").append(dkbz);
        sb.append(", ssjmsf=").append(ssjmsf);
        sb.append(", cid=").append(cid);
        sb.append(", fxqhylb=").append(fxqhylb);
        sb.append(", xqfxdj=").append(xqfxdj);
        sb.append(", fxqszrq=").append(fxqszrq);
        sb.append(", tzzfl=").append(tzzfl);
        sb.append(", tzpz=").append(tzpz);
        sb.append(", tzqx=").append(tzqx);
        sb.append(", yqsy=").append(yqsy);
        sb.append(", tzzpdrq=").append(tzzpdrq);
        sb.append(", tzzpdyxq=").append(tzzpdyxq);
        sb.append(", blcxjl=").append(blcxjl);
        sb.append(", qtfxqhylbxx=").append(qtfxqhylbxx);
        sb.append(", cpdj=").append(cpdj);
        sb.append(", cpdf=").append(cpdf);
        sb.append(", cprq=").append(cprq);
        sb.append(", cpyxq=").append(cpyxq);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}