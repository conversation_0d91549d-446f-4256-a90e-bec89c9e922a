package com.apex.sdx.core.mybatis.service;

import com.apex.sdx.core.mybatis.entity.Thmdjyjgls;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *
 */
public interface ThmdjyjglsService extends IService<Thmdjyjgls> {

    /**
     *
     * @param khh
     * @param ksrq
     * @param jsrq
     * @param jymdlx
     * @param jydx
     * @param jymdjg
     * @param searchCount
     * @param pagenum
     * @param pagesize
     * @return
     */
    Page<Thmdjyjgls> queryByCconditions(String khh, Integer ksrq, Integer jsrq, Integer jymdlx, Integer jydx, Integer jymdjg, boolean searchCount, int pagenum, int pagesize);
}
